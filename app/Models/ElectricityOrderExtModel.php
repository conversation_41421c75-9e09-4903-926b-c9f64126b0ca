<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

/**
 * Class ElectricityOrderExtModel
 * @package App\Models
 * @version June 9, 2025, 2:08 pm CST
 *
 * @property string order_id
 * @property string connector_id
 * @property string driver_name
 * @property string truck_no
 * @property string station_code
 * @property string station_name
 * @property string station_address
 * @property string supplier_name
 * @property string org_name
 * @property string province_code
 * @property string city_code
 * @property string province_name
 * @property string city_name
 * @property numeric power
 * @property string stop_code
 * @property string gas_history_id
 * @property string foss_history_id
 * @property string creator
 * @property string updater
 * @property string remark
 */
class ElectricityOrderExtModel extends BaseModel
{

    public $table = 'foss_electricity_order_ext';

    public $connection = 'mysql_gas';

    public $timestamps = false;

    public $incrementing = false;

    public $primaryKey = 'order_id';

    public $fillable = [
        'order_id',
        'connector_id',
        'driver_name',
        'truck_no',
        'station_code',
        'station_name',
        'station_address',
        'supplier_name',
        'org_name',
        'province_code',
        'city_code',
        'province_name',
        'city_name',
        'power',
        'stop_code',
        'gas_history_id',
        'foss_history_id',
        'creator',
        'updater',
        'remark'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'order_id'        => 'integer',
        'connector_id'    => 'string',
        'driver_name'     => 'string',
        'truck_no'        => 'string',
        'station_code'    => 'string',
        'station_name'    => 'string',
        'station_address' => 'string',
        'supplier_name'   => 'string',
        'org_name'        => 'string',
        'province_code'   => 'string',
        'city_code'       => 'string',
        'province_name'   => 'string',
        'city_name'       => 'string',
        'power'           => 'float',
        'stop_code'       => 'string',
        'gas_history_id'  => 'string',
        'foss_history_id' => 'string',
        'creator'         => 'string',
        'updater'         => 'string',
        'remark'          => 'string'
    ];

    /**
     * @param array $whereParams
     * @param array $select
     * @param bool $lock
     * @return Model|Builder|mixed|object|null|ElectricityOrderExtModel
     */
    public static function getOneElectricityOrderExtByParams(
        array $whereParams,
        array $select = ['*'],
        bool $lock = false
    ) {
        if ($lock) {
            return self::scopeWithCondition(self::select($select), $whereParams)->lockForUpdate()->first();
        }
        return self::scopeWithCondition(self::select($select), $whereParams)->first();
    }

    public static function insertElectricityOrderExt(array $params): bool
    {
        return self::insert($params);
    }

    public static function updateElectricityOrderExt(string $orderId, array $params): bool
    {
        return self::where('order_id', $orderId)->update($params);
    }
}
