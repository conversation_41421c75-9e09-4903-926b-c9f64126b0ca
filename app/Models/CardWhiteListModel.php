<?php


namespace App\Models;


class CardWhiteListModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_card_white_list';

    /**
     * 获取1条白名单记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneWhiteListByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}