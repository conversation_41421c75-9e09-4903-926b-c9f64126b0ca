<?php

namespace App\Listeners;

use App\Events\UnifiedOrder;
use App\Services\NewOrderService;
use App\Services\PaymentService;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class PayOrder implements ShouldQueue
{
    public $connection = 'redis';
    
    public $queue = "async-pay-order";
    
    public $tries = 3;
    
    public $timeout = 200;
    
    public $retryAfter = 1;
    
    private $newOrderService;
    private $paymentService;
    
    /**
     * SyncTrade constructor.
     * @param NewOrderService $newOrderService
     * @param PaymentService $paymentService
     */
    public function __construct(NewOrderService $newOrderService, PaymentService $paymentService)
    {
        $this->newOrderService = $newOrderService;
        $this->paymentService = $paymentService;
    }

    /**
     * Handle the event.
     *
     * @param  UnifiedOrder  $event
     * @return void
     */
    public function handle(UnifiedOrder $event)
    {
        //
        $orderId = $event->orderId;
        if ($orderId) {
            $this->paymentService->finishOrder(['order_id'=>$orderId]);
        }
        
        return;
    }
}
