<?php


namespace App\Http\Controllers;


use App\Http\Defines\CommonError;
use App\Services\WxService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;


class WxController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/19 5:08 下午
     */
    public function asyncBatchSendTemplateMessage(Request $request)
    {
        $rule = [
            'data'       => 'required|array',
            'toUser'     => 'required|array',
            'templateId' => 'required|alpha_dash'
        ];
        $messages = [
            'toUser.required'       => CommonError::WX_TO_USER_REQUIRED,
            'toUser.array'          => CommonError::WX_TO_USER_INVALID,
            'data.required'         => CommonError::WX_TEMPLATE_DATA_REQUIRED,
            'data.array'            => CommonError::WX_TEMPLATE_DATA_INVALID,
            'templateId.required'   => CommonError::WX_TEMPLATE_ID_REQUIRED,
            'templateId.alpha_dash' => CommonError::WX_TEMPLATE_ID_INVALID,
        ];
        $this->validatorThrowRuntimeException($request->all(), $rule, $messages);
        WxService::asyncBatchSendTemplateMessage($request->all());
        return response()->json([
            'code' => CommonError::SUCCESS_CODE,
            'data' => [],
            'msg'  => CommonError::$codeMsg[CommonError::SUCCESS_CODE]
        ]);
    }
}