<?php
namespace App\Library\Upload;

use ALIOSS;
use Framework\Log;
use Request;
use App\Library\Helper\Common;
use Illuminate\Http\File;
//use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Exception;

class Upload {

    private $aliOss = false;//阿里云上传实例
    private $config;

    function __construct(){
        $this->config = config('oss');
        require_once dirname(__FILE__).'/oss_php/sdk.class.php';
        $this->aliOss = new ALIOSS($this->config['id'],$this->config['key'],env("IMG_OSS_BUCKET_URL"));

        $this->aliOss->set_enable_domain_style(true);
        //设置是否打开curl调试模式 参见 demo/tutorial.php
        $this->aliOss->set_debug_mode(FALSE);
    }

    public static function baseUpload($fileName, $realPath)
    {
        $upload = new Upload();
        $path   =  'download/order/'.date('Y-m-d').'/'.date('H').'/'.$fileName;
        $response = $upload->aliOss->upload_file_by_file($upload->config['name'],$path,$realPath);
        if ($response->status != 200) {
            throw new Exception('文件保存失败', 20);
        }
        return $path;
    }

    /**
     * 阿里云图片上传 status==200成功
     * @param $fileField
     * @return string
     * @throws \OSS_Exception
     */
    public static function ossUploadImage($fileField) {
        if (!Request::hasFile($fileField) || !Request::file($fileField)->isValid()) {
            throw new Exception('参数'.$fileField.'对应的图片无效', 20);
        }
        $file = Request::file($fileField);
        $ext = strtolower($file->getClientOriginalExtension());
        if (!in_array($ext, ['jpg','png','jpeg'])) {
            throw new Exception('图片格式错误', 20);
        }
        $size = $file->getSize();
        if ($size > 5*1024*1024) {
            throw new Exception('图片大小超过5M', 20);
        }
        $realPath = $file->getRealPath();
        if (!$realPath) {
            throw new Exception('图片临时文件绝对路径错误', 20);
        }
        $upload = new Upload();
        $imageName = time().rand(100,999).'.'.$ext;
        $path   =  'images/order/'.date('Y-m-d').'/'.date('H').'/'.$imageName;
        $response = $upload->aliOss->upload_file_by_file($upload->config['name'],$path,$realPath);
        if ($response->status != 200) {
            Common::log('error','图片保存失败', ['path' => $path, 'response' => $response]);
            throw new Exception('图片保存失败', 20);
        }
        return $path;
    }

    /**
     * 下载文件到本地文件
     * @param $object
     * @return string
     * @throws \OSS_Exception
     */
    public static function downloadFile($object)
    {
        $config = config('oss');
        require_once dirname(__FILE__).'/oss_php/sdk.class.php';
        $aliOss = new ALIOSS($config['id'],$config['key']);
        $localFile = storage_path(basename($object));
//        $localFile = "/Users/<USER>/Project/etc-service/".basename($object);
        $options = array(
            $aliOss::OSS_FILE_DOWNLOAD => $localFile,
        );
//        $object = "etc-service/dev/2020-04-26/15/G7ee191406863911eab2d17bf1171ccafa.pdf";
        $upload = new Upload();
        $upload->aliOss->get_object(env('IMG_OSS_BUCKET_NAME'), $object, $options);
        if (!file_exists($localFile)) {
            throw new Exception('文件下载失败', 20);
        }
        return $localFile;
    }

    static public function getSignUrl($ossUrl = "",$expiredInSec = 60*60*24)
    {
        if(empty($ossUrl)){
            return $ossUrl;
        }

        if(stripos($ossUrl,"?") !== false){
            $_tmp = explode("?",$ossUrl);
            $ossUrl = $_tmp[0];
        }

        $path = $ossUrl;
        if(stripos($ossUrl,"aliyuncs.com") !== false) {
            $path = substr($ossUrl,stripos($ossUrl,"aliyuncs.com")+13);
            if( empty($path) ){
                return $ossUrl;
            }
        }
        $upload = new Upload();
        return $upload->aliOss->get_sign_url(env('IMG_OSS_BUCKET_NAME'),$path,$expiredInSec);
    }

}
