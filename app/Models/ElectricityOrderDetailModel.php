<?php

namespace App\Models;

/**
 * Class ElectricityOrderDetailModel
 * @package App\Models
 * @version June 9, 2025, 11:41 am CST
 *
 * @property string order_id
 * @property string start_time
 * @property string end_time
 * @property number price
 * @property number service_price
 * @property number total_num
 * @property number total_electricity_money
 * @property number total_service_money
 * @property number total_money
 */
class ElectricityOrderDetailModel extends BaseModel
{

    public $table = 'foss_electricity_order_detail';

    public $timestamps = false;

    public $connection = 'mysql_gas';

    public $incrementing = false;

    public $primaryKey = 'id';

    public $fillable = [
        'order_id',
        'start_time',
        'end_time',
        'price',
        'service_price',
        'total_num',
        'total_electricity_money',
        'total_service_money',
        'total_money'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'order_id'                => 'integer',
        'start_time'              => 'string',
        'end_time'                => 'string',
        'price'                   => 'float',
        'service_price'           => 'float',
        'total_num'               => 'float',
        'total_electricity_money' => 'float',
        'total_service_money'     => 'float',
        'total_money'             => 'float'
    ];

    public static function insertElectricityOrderDetail(array $params): bool
    {
        return self::insert($params);
    }
}
