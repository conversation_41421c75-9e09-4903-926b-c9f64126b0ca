[program:php-eureka-1]
process_name = php-eureka-1
command=/usr/local/bin/php /data/web/artisan eureka:handler 1
autostart = true
autorestart = true
startretries = 1
priority = 99
user = nginx
redirect_stderr = true
stdout_logfile=/data/web_log/php-eureka-1.log

[program:php-eureka-2]
process_name = php-eureka-2
command=/usr/local/bin/php /data/web/artisan eureka:handler 2
autostart = true
autorestart = true
startretries = 1
priority = 99
user = nginx
redirect_stderr = true
stdout_logfile=/data/web_log/php-eureka.log