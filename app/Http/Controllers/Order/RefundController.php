<?php


namespace App\Http\Controllers\Order;


use App\Http\Controllers\Controller;
use App\Services\RefundService;

class RefundController extends Controller
{
    protected $refundService;

    public function __construct
    (
        RefundService $refundService
    )
    {
        $this->refundService = $refundService;
    }

    public function oaRefund()
    {
        $field = [
            'order_id' => '',
            'third_order_id' => '', // 退款单单号
            'remark' => '' // 当前oa无此字段,暂不考虑填写退款原因
        ];

        $rule = [
            'order_id' => 'required|numeric',
            'third_order_id' => 'string',
            'remark' => 'string'
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'third_order_id.string' => '退款单号格式错误!',
            'remark.string' => '退款原因格式错误!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->refundService->oaRefund($params);

            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }
}