<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-23
 * Time: 下午12:03
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

//use Illuminate\Database\Eloquent\SoftDeletes;
//use <PERSON><PERSON><PERSON>\InsertOnDuplicateKey;

class BaseModel extends Model
{
    //use InsertOnDuplicateKey;
    //use SoftDeletes;
    protected $dates = ['createtime', 'updatetime', 'deleted_at'];
    const UPDATED_AT='updatetime';
    const CREATED_AT = 'createtime';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * @param Builder|mixed $query
     * @param array $condition 查询条件数组
     * @param bool|mixed $filterEmpty  是否过滤空值条件
     * @param string|mixed $table 查询数据表名
     * @return Builder|mixed
     */
    public static function scopeWithCondition($query, array $condition, $filterEmpty = false, $table='')
    {

        if (! empty($table)) {
            $table .= '.';
        }

        foreach ($condition as $k => $v) {
            // 过滤空值
            if ($filterEmpty) {
                if (empty($v)) {
                    continue;
                }
            }

            // 条件拼接
            if(is_array($v)) {
                if (preg_match('/^not_(.*?)$/is', $k, $match)) {
                    $query->whereNotIn($table.$match[1], $v);
                    continue;
                }
                $query->whereIn($table.$k, $v);
            }elseif (preg_match('/^eq_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], $v);
            }elseif (preg_match('/^like_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], 'like', '%' . $v . '%');
            }elseif (preg_match('/^left_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], 'like', $v.'%');
            }elseif (preg_match('/^ne_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], '<>', $v);
            }elseif (preg_match('/^gt_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], '>', $v);
            }elseif (preg_match('/^lt_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], '<', $v);
            }elseif (preg_match('/^ge_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], '>=', $v);
            }elseif (preg_match('/^le_(.*?)$/is', $k, $match)) {
                $query->where($table.$match[1], '<=', $v);
            }else {
                $query->where($table.$k, $v);
            }
        }
        return $query;

    }
}
