<?php


namespace App\Library\Helper;


class NumberUtil
{
    /**
     * 格式化数字,小数点指定位后的数字舍去
     *
     * @param $number
     * @param $floatNum
     * @return float|int
     */
    public function formatNumber($number, $floatNum)
    {
        if (empty(explode('.', $number)[1])) {
            return $number;
        }
        return implode('.', [explode('.', $number)[0], substr(explode('.', $number)[1], 0, $floatNum)]);

        // 这样搞会丢失精度
        // return floor($number * pow(10,$floatNum)) / pow(10, $floatNum);
    }

    /**
     * 正则匹配手机号
     *
     * @param $phoneNo
     * @return array
     */
    public function pregPhoneNo($phoneNo)
    {
        /*if (preg_match('/(134[\d*]{4}\d{4})|((13[0-3|5-9]|14[1|5-9]|15[0-9]|16[2|5|6|7]|17[0-8]|18[0-9]|19[0-2|5-9])[\d*]{4}\d{4})/', $phoneNo, $match)) {
            return $match;
        }*/
        if( preg_match('/1[3-9]\d[\d*]{4}\d{4}$/', $phoneNo, $match) ){
            return $match;
        }
        return [];
    }

    /**
     * 特殊字符加密
     *
     * @param $number
     * @param int $start
     * @param int $length
     * @return string
     */
    public function encryptNumber($number, $start = 0, $length = 4)
    {
        if (empty($number)) {
            return $number;
        }
        $encrypt = '';
        $num = 0;
        $array = str_split($number);
        for ($i = 0;$i < count($array);$i++) {
            $encrypt .= $str = $i < $start ? $array[$i] : ($num < $length ? '*' : $array[$i]);
            if (!strcmp($str, '*')) {
                $num++;
            }
        }

        return $encrypt;
    }
    /**
     * 加密数字｜字符串
     *
     * @param $format
     * @param int $start
     * @param int $len
     * @param string $quote
     * @return string
     */
    public function formatPhoneNo($format, $start = 3, $len = 4, $quote = '*')
    {
        $formatArray = str_split((string)$format);
        $formatStr = '';
        $strLen = 1;
        for ($i = 0; $i < count($formatArray); $i++) {
            $str = $formatArray[$i];
            if ($i >= $start && $strLen <= $len) {
                $str = $quote;
                $strLen ++;
            }
            $formatStr .= $str;
        }

        return $formatStr;
    }
}