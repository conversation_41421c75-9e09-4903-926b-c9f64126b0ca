<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/20
 * Time: 10:05
 */
namespace App\Servitization;
use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Exception;
use Illuminate\Support\Facades\Log;
use Psy\Exception\RuntimeException;
use Throwable;

class FossStation
{

    private $config;
    private $timeOut = 15;

    public function __construct()
    {
        $this->config = config('foss_station');
    }

    /**
     * 获取站点详细信息,包含站、价、油品
     *
     * @param $station_id
     * @return mixed
     * @throws Exception
     */
    public function getStation($station_id)
    {
        $path = '/order/v1/station/getStation';
        $params = [
            'id' => $station_id
        ];

        return $this->_postRequest($path, $params);
    }

    /**
     * 获取销售价
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function getSalePrice($params)
    {
        $path = '/v1/price/getOilPriceByTime';

        return $this->_postRequest($path, $params);
    }

    /**
     * 验证卡片所属机构的消费限制(站点、商品)
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function checkCardForStationAndGoodsConsumeLimit($params)
    {
        $path = '/v1/station/checkRule';

        return $this->_postRequest($path, $params);
    }

    /**
     * 获取加油员用户信息
     * @param string $uid 加油员用户ID
     * @return mixed
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 5:08 下午
     */
    public function getOilerInfo($uid)
    {
        return $this->_postRequest('/v1/gasUser/getGasUserInfoById', [
            'id' => $uid,
        ], [
            "X-G7-Api-Uid" => $uid,
        ]);
    }

    /**
     * 获取特殊客户商品及价格
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function getOilListAndPrice($params)
    {
        $path = '/v1/station/getOilListAndPrice';

        return $this->_postRequest($path, $params);
    }

    /**
     * 获取特殊客户商品及价格
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function getStationSpecialPriceForOrg($params)
    {
        $path = '/v1/station/getStationSpecialPriceForOrg';

        return $this->_postRequest($path, $params);
    }

    /**
     * 获取加油员身份信息
     *
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    public function getGasUserInfoById($id)
    {
        $path = '/v1/gasUser/getGasUserInfoById';

        $params = [
            'id' => $id
        ];
        $headers = [
            'X-G7-Api-Uid' => $id
        ];
        return $this->_postRequest($path, $params, $headers);
    }

    /**
     * 获取今日油品挂牌价
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function getTodayListPrice($params)
    {
        $path = '/third/api/v1/api/getTodayOilPrice';

        return $this->_postRequest($path, $params);
    }

    /**
     * POST请求
     *
     * @param $path
     * @param array $params
     * @param array $headers
     * @param bool $addKafkaLog
     * @return mixed
     * @throws Exception
     */
    protected function _postRequest($path, $params = [], $headers = [], $addKafkaLog = false)
    {
        $url = $this->config['url'] . $path;
        try {
            $startTime = microtime(true)*1000;
            $result = Common::request('POST', $url, $this->timeOut, $params, $headers);
            $cost = microtime(true)*1000 - $startTime;
            $result = json_decode($result, true);
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($path, 'foss-station', $params, $result, $cost);
            }
        } catch (Exception $e) {
            KafkaLog::thirdOperationLog($path, 'foss-station', $params, $e->getMessage());
            Log::error('请求FOSS_STATION接口返回异常:' . $e->getMessage(), $params);
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
        if (array_get($result, 'success', false) == false) {
            throw new RuntimeException(
                array_get($result, 'msg', '请求FOSS_STATION接口返回逻辑false'),
                array_get($result, 'code', 403)
            );
        }

        return array_get($result, 'data', []);
    }

    /**
     * GET请求
     *
     * @param $path
     * @param array $params
     * @return mixed
     * @throws Exception
     */
    protected function _getRequest($path, $params = [])
    {
        $url = $this->config['url'].$path;

        try {
            $result = Common::request('GET', $url, $this->timeOut, $params);
            $result = json_decode($result, true);
        } catch (Exception $e) {
            Log::error('请求FOSS_STATION接口返回异常:' . $e->getMessage(), $params);
            throw new Exception($e->getMessage(), $e->getCode());
        }
        if (array_get($result, 'success', false) == false) {
            throw new Exception(
                array_get($result, 'msg', '请求FOSS_STATION接口返回逻辑false'),
                array_get($result, 'code', 403)
            );
        }

        return array_get($result, 'data', []);
    }

    /**
     *注销电子券
     * @param  $coupon_id
     * @param  $updater
     * @return mixed
     * @throws Exception
     */
    public function cancelCoupon($coupon_id, $updater)
    {
        $path = '/api/coupon/v1/revoke';
        $params = [
            'id'    => $coupon_id,
            'login' => $updater,
        ];
        return $this->_postRequest($path, $params);
    }

    /**
     * @param array $params
     * @return mixed
     * @throws Exception
     * @since 2022/6/13 18:07
     */
    public function getUseCoupon(array $params)
    {
        return $this->_postRequest('/order/v1/station/getUseCoupon', $params);
    }


    /**
     * 当前列表的加油员名下的站点关联的orgcode
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function getCooperateList($params, $uid, $token)
    {
        $path = '/v1/gasUser/getCooperateList';

        return $this->_postRequest($path, $params,[
            "X-G7-Api-Uid" => $uid,
            "X-G7-Api-Token" => $token
        ]);
    }


    /**
     * @throws Exception
     */
    public function getElectricityStation(array $params)
    {
        return $this->_postRequest('/v1/charging/getStationAndChargingPrice', $params);
    }

    /**
     * @throws Exception
     */
    public function getConnectorByThirdIdAndSupplierCode(array $params)
    {
        return $this->_postRequest('/v1/getConnectorByThirdIdAndSupplierCode', $params);
    }
}