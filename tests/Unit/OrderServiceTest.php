<?php

namespace Tests\Unit;

use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Services\CouponService;
use App\Services\OrderExtendService;
use Tests\TestCase;

class OrderServiceTest extends TestCase
{

    public function testUpdateOrderExtInfo()
    {
        $orderId = '2022060112105110013';
        $content = [[
            'order_id' => $orderId,
            'straight_down_rebate' => 15,
            'after_rebate' => 16
        ]];
        app(OrderExtendService::class)->updateOrderExtInfo("task_upstream_fanli_calculate", $content);
    }
}
