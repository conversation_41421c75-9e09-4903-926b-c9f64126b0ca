<?php


namespace App\Repositories;


use App\Models\OrderExtendModel;
use App\Models\OrderExtModel;

class OrderExtendRepository
{
    protected $orderExtendModel;

    protected $orderExtModel;

    public function __construct
    (
        OrderExtendModel $orderExtendModel,
        OrderExtModel $orderExtModel
    )
    {
        $this->orderExtendModel = $orderExtendModel;
        $this->orderExtModel = $orderExtModel;
    }

    /**
     * 新增或更新订单扩展信息
     *
     * @param $orderId
     * @param array $insertOrUpdate
     * @return mixed
     */
    public function extendInsertOrUpdate($orderId, array $insertOrUpdate)
    {
        return $this->orderExtendModel->upOrInsert($orderId, $insertOrUpdate);
    }

    /**
     * 获取一条订单扩展记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneExtendInfoByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->orderExtendModel->getOneExtendByParams($whereParams, $select, $toArray);
    }

    /**
     *
     * @param array $whereParams
     * @param string $pluck
     * @return array
     */
    public function getPluckExtendByParams(array $whereParams,$pluck)
    {
        return $this->orderExtModel->getPluckExtendByParams($whereParams, $pluck);
    }

    /**
     * 更新订单扩展表数据
     * @param $orderId
     * @param $data
     */
    public function update($orderId, $data)
    {
        $this->orderExtendModel->where('order_id', $orderId)->update($data);
    }
}