<?php


namespace App\Services;


use App\Events\RefundNotice;
use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Library\Helper\Common;
use App\Library\Helper\NumberUtil;
use App\Library\Request;
use App\Library\Upload\Upload;
use App\Models\CouponsModel;
use App\Models\GasHistoryApproveModel;
use App\Models\HistoryModel;
use App\Models\OrderModel;
use App\Repositories\CardRepository;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\StationRepository;
use App\Servitization\Adapter;
use App\Servitization\FeiShu;
use App\Servitization\Foss;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class OrderApproveService
{
    protected $errorCodeService;
    protected $historyService;
    protected $redisService;
    protected $newOrderService;
    protected $paymentService;
    protected $refundService;
    protected $newOrderRepository;
    protected $historyApproveRepository;
    protected $adapter;
    protected $fossUser;
    protected $numberUtil;
    protected $newOrderExtRepository;
    protected $couponSrv;
    protected $fossStation;
    protected $messageService;
    protected $foss;

    public function __construct
    (
        ErrorCodeService         $errorCodeService,
        HistoryService           $historyService,
        RedisService             $redisService,
        NewOrderService          $newOrderService,
        PaymentService           $paymentService,
        RefundService            $refundService,
        NewOrderRepository       $newOrdersRepository,
        HistoryApproveRepository $historyApproveRepository,
        Adapter                  $adapter,
        FossUser                 $fossUser,
        NumberUtil               $numberUtil,
        NewOrderExtRepository    $ext,
        CouponService            $couponSrv,
        FossStation              $fossStation,
        MessageService           $messageService,
        Foss                     $foss
    )
    {
        $this->errorCodeService = $errorCodeService;
        $this->historyService = $historyService;
        $this->redisService = $redisService;
        $this->newOrderService = $newOrderService;
        $this->paymentService = $paymentService;
        $this->refundService = $refundService;
        $this->newOrderRepository = $newOrdersRepository;
        $this->historyApproveRepository = $historyApproveRepository;
        $this->adapter = $adapter;
        $this->fossUser = $fossUser;
        $this->numberUtil = $numberUtil;
        $this->newOrderExtRepository = $ext;
        $this->couponSrv = $couponSrv;
        $this->fossStation = $fossStation;
        $this->messageService = $messageService;
        $this->foss = $foss;
    }

    protected function checkSettleData($params)
    {
        /**
         * 价格校验 1指定金额 2指定加油升数
         */

        $settlement = array_get($params, 'settlement_type', 1);
        if ($params['oil_unit'] == 1) {
            switch ($settlement) {
                case 2://油机金额*折扣率=结算金额
                    $realOilNum = bcdiv($params['mac_amount'], $params['mac_price'], 6);
                    $realOilMoney = round(bcmul($params['mac_amount'], bcdiv($params['discount_rate'], 100,  4), 5), 2);
                    if($realOilMoney != $params['oil_money']) {
                        throw new Exception('折扣率*枪金额≠客户实付金额,请核对', $this->errorCodeService->editApproveError(40304));
                    }
                    break;
                case 1://升数*单价=结算金额
                default:
                    // 加油升数保留2位小数,后面小数舍去
                    $realOilNum = bcdiv($params['mac_amount'], $params['mac_price'], 6);
                    $roundOilNum = $this->numberUtil->formatNumber($realOilNum, 2);
                    if (abs($roundOilNum - $params['oil_num'])) {
                        throw new Exception('[客户应付金额]指定金额加油,升数' . $params['oil_num'] . '和可加升数' . $roundOilNum . '不匹配,请核对', $this->errorCodeService->editApproveError(40303));
                    }
                    break;
            }
        } else {
            // 金额四舍五入
            $realOilNum = $params['oil_num'];
            $realMoney = round(bcmul($params['platform_price'], $realOilNum, 3), 2);
            if (abs($realMoney - $params['oil_money'])) {
                throw new Exception('[客户应付金额]指定升数加油,金额' . $params['oil_money'] . '和可加金额' . $realMoney . '不匹配,请核对', $this->errorCodeService->editApproveError(40303));
            }
        }

        $supplierMoney = round(bcmul($params['supplier_price'], $realOilNum, 3), 2);
        if (abs($supplierMoney - $params['supplier_money'])) {
            throw new Exception('站点应收金额 计算有误，请联系技术', $this->errorCodeService->editApproveError(40353));
        }
        if($settlement == 1) {
            $oilMoney = round(bcmul($params['platform_price'], $realOilNum, 3), 2);
            if (abs($oilMoney - $params['oil_money'])) {
                throw new Exception('司机应付金额 计算有误，请联系技术', $this->errorCodeService->editApproveError(40353));
            }
        }
        return $realOilNum;
    }

    /**
     * 创建｜编辑补录申请单
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function additionalAddOrUpdate($params)
    {
        list(
            $id, $card_no, $card_account_type, $driver_phone, $truck_no, $driver_name,
            $station_id, $oil_type, $oil_name, $oil_level, $tank_id, $gun_id, $oil_time,
            $oil_unit, $mac_price, $supplier_price, $platform_price, $mac_amount, $supplier_money, $oil_money, $oil_num,
            $remark, $ticket_image, $truck_image, $other_image, $force
            ) = array_values($params);
        $source = Request::get('order_channel');
        $cardAccountArray = [1 => '现金账户'];
        $operator = Request::get('user_name');
        $third_order_id = isset($params['third_order_id']) ? $params['third_order_id'] : '';
        /**
         * 图片存储
         */
        $imgHost = config('oss')['host'];
        $params['ticket_image'] = empty($params['ticket_image']) ? '' : (strstr($params['ticket_image'], $imgHost) ? $params['ticket_image'] : $imgHost . $params['ticket_image']);
        $params['truck_image'] = empty($params['truck_image']) ? '' : (strstr($params['truck_image'], $imgHost) ? $params['truck_image'] : $imgHost . $params['truck_image']);
        $params['other_image'] = empty($params['other_image']) ? '' : (strstr($params['other_image'], $imgHost) ? $params['other_image'] : $imgHost . $params['other_image']);


        /**
         * 补录时间校验
         */
        if (strtotime($oil_time) > time()) {
            throw new Exception('加油时间不得晚于当前时间,请核对', $this->errorCodeService->editApproveError(40301));
        }

        if (bccomp($oil_num, 0.00, 2) < 1) {
            throw new Exception('下单失败，单次交易升数小于0.01升', '40335');
        }
        /**
         * 价格校验 1指定金额 2指定加油升数
         */
        $realOilNum = $this->checkSettleData($params);

        /**
         * G7能源账户、司机信息的校验
         */
        $cardInfo = $this->fossUser->getCardInfoWithCurrAccount(['card_no' => $card_no, "trade_from" => 503]);
        if (empty($cardInfo)) {
            throw new Exception('验证账户失败,无效的账号:' . $card_no . ',请核对', $this->errorCodeService->editApproveError(40308));
        }
        if (!empty($truck_no) and !empty($cardInfo['truck_no']) and strcmp($cardInfo['truck_no'], $truck_no)) {
            throw new Exception('验证账户失败,G7能源账户绑定的车牌号和填写的不一致,请核对', $this->errorCodeService->editApproveError(40309));
        }

        if (!empty($cardInfo['driver_tel']) and strcmp($cardInfo['driver_tel'], $driver_phone)) {
            throw new Exception('验证账户失败,G7能源账户绑定的司机手机号和填写的不一致,请核对', $this->errorCodeService->editApproveError(40310));
        }

        if (!empty($cardInfo['driver_name']) and strcmp($cardInfo['driver_name'], $driver_name)) {
            throw new Exception('验证账户失败,G7能源账户绑定的司机和填写的不一致,请核对', $this->errorCodeService->editApproveError(40311));
        }
        // G7能源账户状态
        if (!in_array($cardInfo['card_status'], [10, 30])) {// 10正常 20锁定 30冻结
            throw new Exception('验证账户失败,G7能源账户状态无效!', $this->errorCodeService->editApproveError(40312));
        }
        // G7能源账户使用方式(用卡方式)
        $cardLevelMap = [1 => OrderModel::PERSONAL_CARD, 2 => OrderModel::CAR_CARD];
        if (!array_key_exists($cardInfo['card_level'], $cardLevelMap)) {
            throw new Exception('验证账户失败,G7能源账户使用方式失败!', $this->errorCodeService->editApproveError(40313));
        }
        // G7能源账户类型 增加发财卡补录
        $cardTypeMap = [20 => OrderModel::VALUE_CARD, 21 => OrderModel::SHARE_CARD, 30 => OrderModel::FACAI_CARD];
        if (!array_key_exists($cardInfo['card_type'], $cardTypeMap)) {
            throw new Exception('验证账户失败,无效的G7能源账户类型!', $this->errorCodeService->editApproveError(40314));
        }
        // 机构账户
        if (abs($cardInfo['account_status'] - 10)) { // 10正常 20异常
            throw new Exception('验证账户失败,扣款账户异常!', $this->errorCodeService->editApproveError(40315));
        }
        if ((empty($force)) && ($cardInfo['card_balance'] < round(bcadd($oil_money, bcmul($cardInfo['service_rate'], $oil_num, 3), 3), 2))) {
            throw new Exception('扣款账户余额:' . $cardInfo['card_balance'] . '不足以支付本次补录,是否继续提交审核?', $this->errorCodeService->editApproveError(40316));
        }
        if (in_array($cardInfo['orgcode'], explode(',', env("REPAIR_RELATED_PAYMENT_ORGCODE")))) {
            if (empty($params['pay_company_id'])) {
                throw new Exception('该客户消费补录时必须选择付款公司', $this->errorCodeService->editApproveError(40344));
            }
            $payCompanyIds = array_column($this->foss->getCompanyListByOrgCode([
                'org_code' => $cardInfo['orgcode'],
                'fields'   => ['id'],
                'status'   => 1,
            ]), 'id');
            if (!in_array($params['pay_company_id'], $payCompanyIds)) {
                throw new Exception('已选择的付款公司不在该机构下', $this->errorCodeService->editApproveError(40345));
            }
        }

        /**
         * 站、价校验
         */
        $checkStationAndGetArray = $this->newOrderService->checkStationAndGetArray($station_id, $oil_type, $oil_name, $oil_level, $platform_price, array_get($cardInfo, 'orgcode', 0), $gun_id, $oil_time);

        $recordMirror = [
            'source_name'            => Request::get('order_channel_enum')[$source],
            'oil_unit'               => $oil_unit,
            'oil_unit_name'          => HistoryModel::$OIL_UTIL[$oil_unit],
            'money'                  => $supplier_money, // 站点应收总额
            'pay_money'              => $oil_money,      // 客户应结总额
            'price'                  => $supplier_price,   // 站点应收单价
            'pay_price'              => $platform_price, // 客户应付单价
            'oil_num'                => $oil_num,
            'real_oil_num'           => $realOilNum,
            'oil_time'               => $oil_time,
            'driver_name'            => $driver_name,
            'org_code'               => $cardInfo['orgcode'],
            'org_name'               => $cardInfo['org_name'],
            'card_type'              => $cardTypeMap[$cardInfo['card_type']],
            'card_type_name'         => OrderModel::$CARD_TYPE[$cardTypeMap[$cardInfo['card_type']]],
            'card_level'             => $cardLevelMap[$cardInfo['card_level']],
            'card_level_name'        => OrderModel::$CARD_LEVEL[$cardLevelMap[$cardInfo['card_level']]],
            'account_name'           => $cardInfo['account_name'],
            'is_check_password'      => 0,
            'service_price'          => $cardInfo['service_rate'],
            'card_account_type'      => $card_account_type,
            'card_account_type_name' => $cardAccountArray[$card_account_type],
            'ticket_image'           => empty($ticket_image) ? '' : (strstr($ticket_image, $imgHost) ? $ticket_image : $imgHost . $ticket_image),
            'truck_image'            => empty($truck_image) ? '' : (strstr($truck_image, $imgHost) ? $truck_image : $imgHost . $truck_image),
            'other_image'            => empty($other_image) ? '' : (strstr($other_image, $imgHost) ? $other_image : $imgHost . $truck_image),
            'station_code'           => $checkStationAndGetArray['insert_station_array']['station_code'],
            'pcode'                  => $checkStationAndGetArray['insert_station_array']['supplier_code'],
            'province_code'          => $checkStationAndGetArray['insert_station_array']['province_code'],
            'province_name'          => $checkStationAndGetArray['insert_station_mirror']['province_name'],
            'city_code'              => $checkStationAndGetArray['insert_station_array']['city_code'],
            'city_name'              => $checkStationAndGetArray['insert_station_mirror']['city_name'],
            'station_name'           => $checkStationAndGetArray['insert_station_mirror']['station_name'],
            'remark_name'            => $checkStationAndGetArray['insert_station_mirror']['remark_name'],
            'station_address'        => $checkStationAndGetArray['insert_station_mirror']['station_address'],
            'rebate_grade'           => $checkStationAndGetArray['insert_station_mirror']['rebate_grade'],
            'oil_type'               => $oil_type,
            'oil_name'               => $oil_name,
            'oil_level'              => $oil_level,
            'supplier_name'          => $checkStationAndGetArray['insert_station_mirror']['supplier_name'],
            'oil_type_name'          => $checkStationAndGetArray['insert_station_mirror']['oil_type_name'],
            'oil_name_name'          => $checkStationAndGetArray['insert_station_mirror']['oil_name_name'],
            'oil_level_name'         => $checkStationAndGetArray['insert_station_mirror']['oil_level_name'],
            'goods'                  => $checkStationAndGetArray['insert_station_mirror']['goods'],
            'gun_id'                 => $checkStationAndGetArray['insert_station_mirror']['gun_id'],
            'gun_name'               => $checkStationAndGetArray['insert_station_mirror']['gun_name'],
            'tank_id'                => $checkStationAndGetArray['insert_station_mirror']['tank_id'],
            'tank_name'              => $checkStationAndGetArray['insert_station_mirror']['tank_name'],
            'supplier_price'         => $supplier_price,
            'platform_price'         => $platform_price,
            'price_id'               => $checkStationAndGetArray['insert_station_mirror']['price_id'],
            'list_price'             => $checkStationAndGetArray['insert_station_mirror']['list_price'],
            'discount_rate'          => $params['discount_rate'] ?? null,
            'settlement_type'        => $params['settlement_type'] ?? '',
            'oil_money'              => $params['oil_money'] ?? null,

            'app_station_id' => array_get($checkStationAndGetArray, 'insert_station_mirror.app_station_id', ''),
            'trade_type'     => array_get($checkStationAndGetArray, 'insert_station_mirror.trade_type', 1),
            'lng'            => array_get($checkStationAndGetArray, 'insert_station_mirror.lng', ''),
            'lat'            => array_get($checkStationAndGetArray, 'insert_station_mirror.lat', ''),
            'mac_price'      => $mac_price,
            'mac_amount'     => $mac_amount,
            'account_no'     => array_get($cardInfo, 'account_no', ''),
            'pay_company_id' => $params['pay_company_id'],
        ];
        $insertOrUpdateArray = [
            'history_type'   => GasHistoryApproveModel::INSERT_HISTORY,
            'approve_mirror' => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
            'approve_status' => GasHistoryApproveModel::WAIT_APPROVE,
            'source'         => $source,
            'remark'         => $remark,
            'truck_no'       => $truck_no,
            'card_no'        => $card_no,
            'driver_phone'   => $driver_phone,
            'province_code'  => $checkStationAndGetArray['insert_station_array']['province_code'],
            'city_code'      => $checkStationAndGetArray['insert_station_array']['city_code'],
            'station_id'     => $station_id,
            'creator'        => $operator,
            'updator'        => $operator,
            'third_order_id' => $third_order_id//三方订单号
        ];
        return $this->approveInsertOrUpdate($insertOrUpdateArray, $id);
    }

    /**
     * @throws Exception|Throwable
     */
    public function exceptionModificationPreCheck(array $params)
    {
        $redisConn = app("redis");
        $lockKey = __FUNCTION__ . $params['order_id'];
        if (!$redisConn->set($lockKey, $lockKey, 'nx', 'ex', 10)) {

            throw new Exception('请勿重复请求',
                $this->errorCodeService->editApproveError(40348));
        }
        DB::connection("mysql_gas")->beginTransaction();
        try {

            $orderInfo = $this->newOrderRepository->getOrderInfoByLock([
                'order_id'     => $params['order_id'],
                'order_status' => OrderModel::SUCCESS_PAY,
            ]);
            if (empty($orderInfo)) {

                throw new Exception('订单号不存在或订单状态异常,请稍后再试',
                    $this->errorCodeService->editApproveError(40340));
            }
            if (in_array($orderInfo['org_code'], explode(',', env("NOT_ALLOW_ABNORMAL_MODIFY_ORGCODE")))) {
                throw new Exception('该客户订单不可异常修改，请根据实际情况进行退款和补录', $this->errorCodeService->editApproveError(40353));
            }
            $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
                'order_id'       => $params['order_id'],
                'approve_status' => [
                    GasHistoryApproveModel::WAIT_APPROVE,
                    //GasHistoryApproveModel::FAIL_AFTER_APPROVE,//G7WALLET-5398
                ],
            ]);
            if (!empty($approveInfo)) {

                throw new Exception('该订单已经有一笔异常申请未完成！',
                    $this->errorCodeService->editApproveError(40341));
            }
            DB::commit();
            $redisConn->del([$lockKey]);
        } catch (Throwable $t) {

            DB::commit();
            $redisConn->del([$lockKey]);
            throw $t;
        }
    }

    /**
     * 创建｜编辑异常修改申请单
     *
     * @param $params
     * @return void
     * @throws Exception|Throwable
     */
    public function exceptionModificationApprove($params)
    {
        $redisConn = app("redis");
        $lockKey = __FUNCTION__ . $params['order_id'] . $params['id'];
        if (!$redisConn->set($lockKey, $lockKey, 'nx', 'ex', 10)) {

            throw new Exception('异常修改审核单正在录入中,请耐心等待',
                $this->errorCodeService->editApproveError(40348));
        }

        //$force = array_get($params, 'force', 0);


        $realOilNum = $this->checkSettleData($params);
        if (bccomp($params['oil_num'], 0.00, 2) < 1) {
            throw new Exception('下单失败，单次交易升数小于0.01升', '40335');
        }

        /*if(empty($force)) {
            $checkText = $this->foss->checkOrderIsReceipt(['order_id' => $params['order_id']]);
            Log::info('请求foss异常修改申请检测发票，order_id:' . $params['order_id'] . '返回结果:' . $checkText);
            if (!empty($checkText)) {
                throw new Exception($checkText . "\n" . '确定要发起异常修改申请吗?', $this->errorCodeService->editApproveError(40323));
            }
        }*/

        DB::connection("mysql_gas")->beginTransaction();
        try {

            if (empty($params['order_id'])) {

                $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
                    'id'             => $params['id'],
                    'approve_status' => [
                        GasHistoryApproveModel::WAIT_APPROVE,
                        GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                    ],
                ], ['*'], true, true);
                if (empty($approveInfo)) {

                    throw new Exception('审核单编号不合法', $this->errorCodeService->editApproveError(40349));
                }
                $orderInfo = json_decode($approveInfo['approve_mirror'], true)['orderInfo'];
            } else {

                $orderInfo = $this->newOrderRepository->getOrderInfoByLock([
                    'order_id'     => $params['order_id'],
                    'order_status' => OrderModel::SUCCESS_PAY,
                ]);
                if (empty($orderInfo)) {

                    throw new Exception('订单号不存在或订单状态异常,请稍后再试', $this->errorCodeService->editApproveError(40340));
                }
                $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
                    'order_id'       => $params['order_id'],
                    'approve_status' => [
                        GasHistoryApproveModel::WAIT_APPROVE,
                        //GasHistoryApproveModel::FAIL_AFTER_APPROVE,//G7WALLET-5398
                    ],
                ]);
                if (!empty($approveInfo)) {

                    throw new Exception('该订单已经有一笔异常申请未完成！', $this->errorCodeService->editApproveError(40341));
                }
            }
            if (in_array($orderInfo['org_code'], explode(',', env("NOT_ALLOW_ABNORMAL_MODIFY_ORGCODE")))) {
                throw new Exception('该客户订单不可异常修改，请根据实际情况进行退款和补录', $this->errorCodeService->editApproveError(40353));
            }
            if (app(StationRepository::class)->getOneStationBasicInfoByParams([
                'id' => $orderInfo['station_id']
            ], ['card_assign_method'])['card_assign_method'] == 4) {
                throw new Exception('代管卡自动分配站点不允许操作！', $this->errorCodeService->editApproveError(40340));
            }
            if (in_array($orderInfo['supplier_code'], CommonDefine::notAllowExceptionModification())) {
                throw new Exception('该供应商需要向上游接口发起退款，不可异常修改', $this->errorCodeService->editApproveError(40353));
            }
            if (bccomp($params['oil_money'], $orderInfo['oil_money'], 2) > 0) {
                throw new Exception('司机应付金额不能大于原订单的司机实付金额', $this->errorCodeService->editApproveError(40353));
            }

            $cardInfo = $this->fossUser->getCardInfoWithCurrAccount([
                'card_no'    => $orderInfo['card_no'],
                "trade_from" => 503
            ]);
            if (empty($cardInfo)) {

                throw new Exception('验证账户失败,无效的账号:' . $orderInfo['card_no'] . ',请核对',
                    $this->errorCodeService->editApproveError(40330));
            }
            CardRepository::checkCardAccountStatus($cardInfo['account_status'], true);
//            if ($cardInfo['card_balance'] < round(bcadd($params['oil_money'], bcmul($cardInfo['service_rate'],
//                    $params['oil_num'], 3), 3), 2)) {
//
//                throw new Exception('扣款账户余额:' . $cardInfo['card_balance'] . '不足以支付本次补录,是否继续提交审核?', $this->errorCodeService->editApproveError(40338));
//            }
            foreach ((new StationService())->getDict()->toArray() as $v) {

                if ($v['id'] == $params['oil_name']) {

                    $params['oil_name_name'] = $v['dict_data'];
                }
                if (!empty($params['oil_type']) and $v['id'] == $params['oil_type']) {

                    $params['oil_type_name'] = $v['dict_data'];
                }
                if (!empty($params['oil_level']) and $v['id'] == $params['oil_level']) {

                    $params['oil_level_name'] = $v['dict_data'];
                }
            }
            $imgHost = config('oss')['host'];
            $params['ticket_image'] = empty($params['ticket_image']) ? '' : (strstr($params['ticket_image'],
                $imgHost) ? $params['ticket_image'] : $imgHost . $params['ticket_image']);
            $params['truck_image'] = empty($params['truck_image']) ? '' : (strstr($params['truck_image'],
                $imgHost) ? $params['truck_image'] : $imgHost . $params['truck_image']);
            $params['other_image'] = empty($params['other_image']) ? '' : (strstr($params['other_image'],
                $imgHost) ? $params['other_image'] : $imgHost . $params['other_image']);

            $params['real_oil_num'] = $realOilNum;
            $params['discount_rate'] = $params['discount_rate'] ?: null;
            $recordMirror = [
                'orderInfo'    => $orderInfo,
                'approveData'  => $params,
                'station_name' => $orderInfo['ext']['station_name'],
                'driver_name'  => $orderInfo['driver_name'],
                'goods'        => ($params['oil_name_name'] ?? '') . ($params['oil_type_name'] ?? '') .
                    ($params['oil_level_name'] ?? ''),
                'oil_time'     => $orderInfo['create_time'],
                'pay_price'    => $params['platform_price'],      // 客户应付单价
                'pay_money'    => $params['oil_money'],                 // 客户应结总额
                'price'        => $params['supplier_price'],    // 站点应收单价
                'money'        => $params['supplier_money'],   // 站点应收总额
                'oil_num'      => $params['oil_num'],
                'source_name'  => Request::get('order_channel_enum')[$params['source']],
                'mac_price'    => $params['mac_price'],
                'mac_amount'   => $params['mac_amount']
            ];
            $insertOrUpdateArray = [
                'history_type'   => GasHistoryApproveModel::UPDATE_HISTORY,
                'order_id'       => $orderInfo['order_id'],
                'approve_mirror' => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
                'approve_status' => !empty($approveInfo) ? $approveInfo['approve_status'] :
                    GasHistoryApproveModel::WAIT_APPROVE,
                'source'         => $params['source'],
                'truck_no'       => $orderInfo['truck_no'],
                'card_no'        => $orderInfo['card_no'],
                'driver_phone'   => $orderInfo['driver_phone'],
                'province_code'  => $orderInfo['province_code'],
                'city_code'      => $orderInfo['city_code'],
                'station_id'     => $orderInfo['station_id'],
                'creator'        => $params['operator'],
                'updator'        => $params['operator'],
                'remark'         => $params['remark'],
            ];
            $this->approveInsertOrUpdate($insertOrUpdateArray, $params['id']);
            DB::connection("mysql_gas")->commit();
            $redisConn->del([$lockKey]);
        } catch (Throwable $t) {

            $redisConn->del([$lockKey]);
            DB::connection("mysql_gas")->rollBack();
            throw $t;
        }
    }


    /**
     * 退款申请(十分钟内支付的订单自动退款)
     *
     * @param $params
     * @return array|true
     * @throws Exception
     * @throws Throwable
     */
    public function refundApprove($params)
    {
        $orderId = array_get($params, 'order_id', 0);
        $remark = array_get($params, 'remark', '');
        $ticketImage = array_get($params, 'ticket_image', '');
        $truckImage = array_get($params, 'truck_image', '');
        $otherImage = array_get($params, 'other_image', '');
        $force = array_get($params, 'force', 0);
        $approvePass = array_get($params, 'approve_pass', 0);
        $source = Request::get('order_channel');
        $sourceName = Request::get('order_channel_enum')[$source];
        $operator = Request::get('user_name');
        // 如果退客户系统订单直接分流
        if (($params['refund_system'] ?? 1) == 2) {
            $approvePass = $this->refundCustomerApprove($params);
            return ['result' => true, 'approve_pass' => $approvePass];
        }

        //$orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $orderId]);
        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $orderId]);
        if (empty($orderInfo)) {
            throw new Exception('订单不存在,请核实后重试!', $this->errorCodeService->editApproveError(40301));
        }
        if ($orderInfo['order_status'] == OrderModel::REFUNDING) {
            throw new Exception('订单退款中,请耐心等待工作人员审核!', $this->errorCodeService->editApproveError(40303));
        }
        if ($orderInfo['order_status'] != OrderModel::SUCCESS_PAY) {
            throw new Exception('退款失败:订单状态为' . OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . '不允许退款!', $this->errorCodeService->editApproveError(40304));
        }
        if ($source == 6) {
            $operator = $orderInfo['ext']['org_name'];
        }
        // 四川壳牌询问OA
        if (empty($force) and $source != 6) {

            $checkText = $this->foss->checkOrderIsReceipt(["order_id"=>$orderId]);
            Log::info('请求foss退款申请检测发票，order_id:' . $orderId . '返回结果:' . $checkText);

            if (!empty($orderInfo['third_order_id'])) {
                throw new Exception($checkText."\n".'仅能退回客户在G7能源的预存款,回退司机的钱需要联系运营人员,确定要退款吗?', $this->errorCodeService->editApproveError(40323));
            }
            if (in_array($orderInfo['supplier_code'], explode(',', env('PCODE_OA_CHECK_BEFORE_REFUND')))) {
                $thirdOrderInfo = $this->adapter->queryOrderExchange(['order_id' => $orderId]);
                if (abs(array_get($thirdOrderInfo, 'platform_order_status', 0) - 2) > 0) {
                    throw new Exception($checkText."\n".'这笔订单为壳牌的订单,可能已被核销,请确认后再发起退款!', $this->errorCodeService->editApproveError(40323));
                }
            }
            if (in_array($orderInfo['supplier_code'], CommonDefine::linkedRefundApproveSupplier())) {
                throw new Exception($checkText."\n".'仅能退回客户在G7能源的预存款,回退司机的钱需要联系' . $orderInfo['ext']['supplier_name'] . '运营人员,确定要退款吗?', $this->errorCodeService->editApproveError(40323));
            }
            // 支付时间小于10min，立即退款
            if ((time() - strtotime($orderInfo['pay_time'])) < 600) {
                throw new Exception($checkText."\n".'这笔订单付款时间小于10分钟,系统将自动通过退款审核,确定要退款吗?', $this->errorCodeService->editApproveError(40323));
            }

            throw new Exception($checkText."\n".'确定要发起退款申请吗?', $this->errorCodeService->editApproveError(40323));
        }

        //添加河北中石油三方券状态 已核销的不能退款
        if (in_array($orderInfo['supplier_code'], CouponDefine::specialRefundSupplierCode())) {
            switch ($orderInfo['supplier_code']) {
                case CouponDefine::PCODE_ZSY_COUPON:
                case CouponDefine::PCODE_ZSY_COUPON_ONLINE:
                    $coupon_flag = array_get($orderInfo, "ext.g7_coupon_flag", "");
                    if (!$coupon_flag) {
                        throw new Exception('退款失败:G7券标识不存在!', $this->errorCodeService->editApproveError(40324));
                    }
                    $coupon = CouponsModel::getInfo(['voucher' => $coupon_flag]);
                    if (!$coupon) {
                        throw new Exception('退款失败:电子券使用记录表不存在!', $this->errorCodeService->editApproveError(40325));
                    }
                    if ($coupon['tripartite_status'] == CouponDefine::CHARGE_OFFED) {
                        throw new Exception('退款失败:该订单已核销，不能退款!', $this->errorCodeService->editApproveError(40326));
                    }
                    break;
                case CommonDefine::HB1_PROD_SUPPLIER_CODE:
                case CommonDefine::HB1_TEST_SUPPLIER_CODE:
                    $response = $this->adapter->getCouponStatus([
                        'order_id'      => $orderId,
                        'supplier_code' => $orderInfo['supplier_code'],
                    ]);
                    switch ($response['detail']['code_status']) {
                        case 'NORMAL':
                            throw new Exception(str_replace([
                                'supplier_name',
                                'status_desc',
                            ], [
                                $orderInfo['ext']['supplier_name'],
                                CouponDefine::COUPON_STATUS_TRANSLATION[$orderInfo['supplier_code']][$response['detail']['code_status']],
                            ], CommonError::$codeMsg[CommonError::HB_NOT_ALLOW_REFUND]), CommonError::HB_NOT_ALLOW_REFUND);
                        case 'CONSUMED':
                            throw new Exception(CommonError::$codeMsg[CommonError::NOT_ALLOW_REFUND_FOR_WRITTEN_OFF],
                                CommonError::NOT_ALLOW_REFUND_FOR_WRITTEN_OFF);
                    }
                    break;
                case CommonDefine::HB2_TEST_SUPPLIER_CODE:
                case CommonDefine::HB2_PROD_SUPPLIER_CODE:
                    $response = $this->adapter->getCouponStatus([
                        'order_id'      => $orderId,
                        'supplier_code' => $orderInfo['supplier_code'],
                    ]);
                    if ($response['state'] == 40) {
                        throw new Exception(CommonError::$codeMsg[CommonError::NOT_ALLOW_REFUND_FOR_WRITTEN_OFF],
                            CommonError::NOT_ALLOW_REFUND_FOR_WRITTEN_OFF);
                    }
                    break;
                case CommonDefine::SH_TEST_SUPPLIER_CODE:
                case CommonDefine::SH_PROD_SUPPLIER_CODE:
                    $coupon_flag = array_get($orderInfo, "ext.g7_coupon_flag", "");
                    $coupon = CouponsModel::getInfo(['voucher' => $coupon_flag]);
                    if (!$coupon) {
                        throw new Exception('退款失败:电子券使用记录表不存在!', $this->errorCodeService->editApproveError(40325));
                    }
                    if ($coupon['tripartite_status'] == CouponDefine::CHARGE_OFFED) {
                        throw new Exception('退款失败:该订单已核销，不能退款!', $this->errorCodeService->editApproveError(40326));
                    }
                    break;
            }
        }

        /**
         * 退款审核信息补充
         */
        if (!empty($orderInfo['mirror'])) {
            $orderMirror = json_decode($orderInfo['mirror'], true);
        } else {
            $orderMirror = $orderInfo['ext'];
        }
        $recordMirror = [
            'source_name'            => $sourceName,
            'oil_unit'               => $orderInfo['oil_unit'],
            'oil_unit_name'          => OrderModel::$OIL_UNIT[$orderInfo['oil_unit']],
            'money'                  => round(bcmul($orderMirror['supplier_price'], $orderInfo['real_oil_num'], 3), 2), //站点应收金额
            'pay_money'              => $orderInfo['oil_money'], // 客户应付金额
            'price'                  => $orderMirror['supplier_price'], //站点应收单价
            'pay_price'              => $orderInfo['oil_price'], //客户应付单价
            'oil_num'                => $orderInfo['oil_num'],
            'oil_time'               => $orderInfo['pay_time'],
            'driver_name'            => $orderInfo['driver_name'],
            'card_account_type'      => 1,
            'card_account_type_name' => '现金账户',
            'ticket_image'           => !empty($ticketImage) ? Upload::getSignUrl($ticketImage) : '',
            'truck_image'            => !empty($truckImage) ? Upload::getSignUrl($truckImage)  : '',
            'other_image'            => !empty($otherImage) ? Upload::getSignUrl($otherImage)  : '',
            'station_code'           => $orderInfo['station_code'],
            'station_name'           => $orderMirror['station_name'],
            'supplier_name'          => $orderMirror['org_name'],
            'province_code'          => $orderInfo['province_code'],
            'city_code'              => $orderInfo['city_code'],
            'province_name'          => $orderMirror['province_name'],
            'city_name'              => $orderMirror['city_name'],
            'gun_id'                 => $orderMirror['gun_id'],
            'gun_name'               => $orderMirror['gun_name'],
            'tank_id'                => $orderMirror['tank_id'],
            'tank_name'              => $orderMirror['tank_name'],
            'oil_type'               => $orderInfo['oil_type'],
            'oil_type_name'          => $orderMirror['oil_type_name'],
            'oil_name'               => $orderInfo['oil_name'],
            'oil_name_name'          => $orderMirror['oil_name_name'],
            'oil_level'              => $orderInfo['oil_level'],
            'oil_level_name'         => $orderMirror['oil_level_name'],
            'goods'                  => $orderMirror['goods'],
            'app_station_id'         => array_get($orderMirror, 'app_station_id', ''),
            'trade_type'             => array_get($orderMirror, 'trade_type', 1),
            'lng'                    => array_get($orderMirror, 'lng', ''),
            'lat'                    => array_get($orderMirror, 'lat', ''),
            'mac_price'              => array_get($orderMirror, 'mac_price', 0),
            'mac_amount'             => array_get($orderMirror, 'mac_amount', 0),
            'org_code'               => $orderInfo['org_code'],
        ];

        $insertArray = [
            'order_id'            => $orderId,
            'original_history_id' => $orderInfo['history_id'], // 退款单流水号
            'history_type'        => GasHistoryApproveModel::CANCEL_HISTORY,
            'approve_mirror'      => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
            'approve_status'      => GasHistoryApproveModel::WAIT_APPROVE,
            'source'              => $source,
            'remark'              => $remark,
            'truck_no'            => $orderInfo['truck_no'],
            'card_no'             => $orderInfo['card_no'],
            'driver_phone'        => $orderInfo['driver_phone'],
            'province_code'       => $orderInfo['province_code'],
            'city_code'           => $orderInfo['city_code'],
            'station_id'          => $orderInfo['station_id'],
            'third_order_id'      => $orderInfo['third_order_id'],
            'creator'             => $operator,
            'updator'             => $operator,
            'refund_system'       => $params['refund_system'] ?? 0,
        ];

        try {
            DB::connection('mysql_gas')->beginTransaction();
            /**
             * 审核单幂等校验
             */
            $historyApproveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
                'order_id'       => $orderId,
                'history_type'   => GasHistoryApproveModel::CANCEL_HISTORY,
                'approve_status' => [
                    GasHistoryApproveModel::WAIT_APPROVE,
                    GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                    GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                    GasHistoryApproveModel::FINISH_AFTER_APPROVE
                ],
                'refund_system'  => 1,
            ], ['*'], true, true);
            if (!empty($historyApproveInfo)) {
                if ($source == 6) {
                    throw new Exception('创建退款审核单失败,已有待审核/审核完结的单据!', $this->errorCodeService->editApproveError(40306));
                }
                return true;
            }
            // 创建审核单
            $approveId = $this->historyApproveRepository->approveInsert($insertArray);
            if (empty($approveId)) {
                throw new Exception('退款失败:创建退款审核单失败,请重试!', $this->errorCodeService->editApproveError(40306));
            }
            // 修改订单状态
            $updateArray = [
                'order_status' => OrderModel::REFUNDING,
                'updator'      => $operator,
            ];
            $result = $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
            if (empty($result)) {
                throw new Exception('退款失败:更新订单状态失败,请重试!', $this->errorCodeService->editApproveError(40307));
            }

            if (in_array($orderInfo['supplier_code'], CommonDefine::linkedRefundApproveSupplier())) {
                $this->messageService->pushData2OA([
                    'order_id'          => $orderId,
                    'approve_id'  => $approveId,
                    'order_holder_code' => $orderInfo['supplier_code'],
                    'reason' => $remark,
                ], 'REFUND');
            }
            DB::connection('mysql_gas')->commit();
        } catch (Exception $e) {
            DB::connection('mysql_gas')->rollBack();
            throw $e;
        }

        /**
         * 支付时间10min内的自动通过审核
         */
        if ((time() - strtotime($orderInfo['pay_time'])) <= 600 and
            !in_array($orderInfo['supplier_code'], CommonDefine::linkedRefundApproveSupplier()) and
            $source != 6) {
            $approvePass = 1;
        }
        if (!empty($approvePass)) {
            $this->approvePass([
                'id'         => $approveId,
                'operatorId' => '',
                'operator' => '系统',
            ]);
        }
        // 发送最后一次的审核通知
        event(new RefundNotice($approveId));
        return ['result' => true, 'approve_pass' => $approvePass];
    }

    /**
     * 审核通过
     *
     * @param $params
     * @return bool
     * @throws Exception|Throwable
     */
    public function approvePass($params)
    {
        $lockKey = "{$params['id']}_order";
        $this->redisService->addApprovePassLock($lockKey);
        $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $params['id']], ['*'], true);
        if (empty($approveInfo)) {
            throw new Exception('审核失败:无效的审核单ID,请核实!', $this->errorCodeService->approveError(40301));
        }
        // 已驳回
        if ($approveInfo['approve_status'] == GasHistoryApproveModel::REJECT_APPROVE) {
            throw new Exception('审核失败:审核单已驳回,无法通过审核,请核实!', $this->errorCodeService->approveError(40302));
        }
        // 审核通过
        if ($approveInfo['approve_status'] == GasHistoryApproveModel::SUCCESS_AFTER_APPROVE) {
            throw new Exception('审核失败:审核单已通过,请勿重复操作!', $this->errorCodeService->approveError(40303));
        }
        // 审核通过
        if ($approveInfo['approve_status'] == GasHistoryApproveModel::REFUND_SUCCESS_AFTER_APPROVE) {
            throw new Exception('审核失败:审核单已通过,请勿重复操作!', $this->errorCodeService->approveError(40303));
        }

        try {
            if ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {  // 退款
                // 订单状态
                $orderInfo = $this->newOrderRepository->getOneOrderByParams([
                    'order_id' => $approveInfo['order_id']
                ], [
                    'order_id',
                    'order_status',
                    'third_order_id',
                    'supplier_code',
                    'org_code'
                ]);
                if (in_array($orderInfo['supplier_code'], CommonDefine::linkedRefundApproveSupplier()) and (!isset($params['source']) or $params['source'] != 'oa')) {
                    throw new Exception('请联系' . $orderInfo['ext']['supplier_name'] . '运营人员进行审核', $this->errorCodeService->approveError(40342));
                }
                //G7WALLET-2215
                if ($orderInfo['order_status'] == OrderModel::SUCCESS_PAY) {
                    $this->newOrderRepository->updateOrderByOrderId($orderInfo['order_id'], [
                        'order_status' => OrderModel::REFUNDING,
                        'updator'      => '系统',
                    ]);
                    $orderInfo['order_status'] = OrderModel::REFUNDING;
                }
                if (!in_array($orderInfo['order_status'], [OrderModel::SUCCESS_PAY, OrderModel::REFUNDING])) {
                    throw new Exception('审核失败:订单状态为' . OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . '不允许退款!', $this->errorCodeService->approveError(40304));
                }
                if (isset(CommonDefine::linkedRefundSupplier()[$orderInfo['supplier_code']])) {
                    try {
                        $this->messageService->pushData2OA([
                            'order_id'          => $orderInfo['order_id'],
                            'order_holder_code' => $orderInfo['supplier_code'],
                        ], 'REFUND');
                    } catch (Throwable $throwable) {
                        if (isset(
                                CommonDefine::linkedRefundSupplier()[$orderInfo['supplier_code']]['wait_refund_result']
                            ) and CommonDefine::linkedRefundSupplier(
                            )[$orderInfo['supplier_code']]['wait_refund_result']) {
                            throw $throwable;
                        }
                    }
                }
                // 退款
                $needArray = [
                    'original_history_id' => $approveInfo['original_history_id'],
                    'order_id'            => $approveInfo['order_id'],
                ];
                $historyId = $this->refundService->fossRefund($needArray, $params['operator'],
                    true, true);

                $update = [
                    'history_id'              => $historyId,
                    'approve_status'          => GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                    'approve_abnormal_reason' => $params['approval_reason'] ?? '',
                    'updator'                 => Request::get('user_name', '系统'),
                ];

                // 修改审核单状态
                $result = $this->historyApproveRepository->approveUpdateById($params['id'], $update);
                if (empty($result)) {
                    throw new Exception('退款失败:修改审核单状态失败!', $this->errorCodeService->editApproveError(40306));
                }
                if ($approveInfo['source'] == 6) {
                    $this->messageService->pushData2OA([
                        'order_id'     => $orderInfo['order_id'],
                        'org_code'     => $orderInfo['org_code'],
                        'audit_status' => 1,
                        'audit_reason' => $params['approval_reason'] ?? '',
                    ], 'REFUND_APPLY_AUDIT');
                }
            } elseif ($approveInfo['history_type'] == GasHistoryApproveModel::INSERT_HISTORY) {  // 补录

                DB::connection('mysql_gas')->beginTransaction();
                try {
                    $approveMirror = json_decode($approveInfo['approve_mirror'], true);
                    $channel = CardTradeConf::AFTERWARDS_ADD;
                    // 创建补录订单
                    $orderId = $this->redisService->makeOrderId();
                    $makeOrder = [
                        'order_id'      => $orderId,
                        'order_sn'      => 'gms-patch' . time(),
                        'trade_no'      => $this->redisService->makeSerialNum(),
                        'order_type'    => OrderModel::CHARGE_BY_ADMIN, // 补录
                        'order_channel' => $channel,
                        'trade_mode'    => CardTradeConf::TRADE_MODE_ZYZ_ZYS_BL,
                        'order_status'  => OrderModel::WAIT_PAY,
                        'station_id'    => $approveInfo['station_id'],
                        'station_code'  => $approveMirror['station_code'],
                        'province_code' => $approveMirror['province_code'],
                        'city_code'     => $approveMirror['city_code'],
                        'supplier_code' => $approveMirror['pcode'],
                        'org_code'      => $approveMirror['org_code'],
                        'card_no'       => $approveInfo['card_no'],
                        'card_type'     => $approveMirror['card_type'],
                        'card_level'    => $approveMirror['card_level'],
                        'driver_name'   => $approveMirror['driver_name'],
                        'driver_source' => OrderModel::OWN_DRIVER, // 三方司机不允许补录
                        'driver_phone'  => $approveInfo['driver_phone'],
                        'truck_no'      => $approveInfo['truck_no'],
                        'oil_type'      => $approveMirror['oil_type'],
                        'oil_name'      => $approveMirror['oil_name'],
                        'oil_level'     => $approveMirror['oil_level'],
                        'oil_unit'      => $approveMirror['oil_unit'],
                        'oil_num'       => $approveMirror['oil_num'],
                        'oil_price'     => $approveMirror['platform_price'],
                        'oil_money'     => $approveMirror['oil_money'] ?? round(bcmul((string)$approveMirror['platform_price'], (string)$approveMirror['real_oil_num'], 3), 2),
                        'real_oil_num'  => $approveMirror['real_oil_num'],
                        'service_price' => $approveMirror['service_price'],
                        'service_money' => round(bcmul((string)$approveMirror['service_price'], (string)$approveMirror['real_oil_num'], 3), 2),
                        'mirror'        => '',
                        'creator'       => $approveInfo['creator'],
                        'updator'       => $approveInfo['creator'],
                        'order_sale_type' => OrderModel::ORDER_SALE_TYPE_REFUEL,
                    ];
                    $mirror = [
                        'order_id'           => $orderId,
                        'order_type_name'    => OrderModel::$ORDER_TYPE[OrderModel::CHARGE_BY_ADMIN],
                        #'order_channel_name' => Request::get('order_channel_enum')[Request::get('order_channel')],
                        'order_channel_name' => CardTradeConf::getFrom($channel),
                        'org_name'           => $approveMirror['org_name'],
                        'driver_source_name' => OrderModel::$DRIVER_SOURCE[OrderModel::OWN_DRIVER],
                        'card_type_name'     => $approveMirror['card_type_name'],
                        'card_level_name'    => $approveMirror['card_level_name'],
                        'account_type_name'  => $approveMirror['card_account_type_name'],
                        'account_name'       => $approveMirror['account_name'],
                        'account_no'         => $approveMirror['account_no'],
                        'station_name'       => $approveMirror['station_name'],
                        'remark_name'        => $approveMirror['remark_name'],
                        'station_address'    => $approveMirror['station_address'],
                        'rebate_grade'       => $approveMirror['rebate_grade'],
                        'supplier_name'      => $approveMirror['supplier_name'],
                        'province_name'      => $approveMirror['province_name'],
                        'city_name'          => $approveMirror['city_name'],
                        'oil_type_name'      => $approveMirror['oil_type_name'],
                        'oil_name_name'      => $approveMirror['oil_name_name'],
                        'oil_level_name'     => $approveMirror['oil_level_name'],
                        'goods'              => $approveMirror['goods'],
                        'gun_id'             => $approveMirror['gun_id'],
                        'gun_name'           => $approveMirror['gun_name'],
                        'tank_id'            => $approveMirror['tank_id'],
                        'tank_name'          => $approveMirror['tank_name'],
                        'supplier_price'     => $approveMirror['supplier_price'],
                        'platform_price'     => $approveMirror['platform_price'],
                        'price_id'           => $approveMirror['price_id'],
                        'list_price'         => $approveMirror['list_price'],
                        'is_check_password'  => $approveMirror['is_check_password'],
                        'order_token'        => '', // 补录无websocket标识
                        'operator_id'        => $params['operatorId'],
                        'app_station_id'     => array_get($approveMirror, 'app_station_id', ''),
                        'trade_type'         => array_get($approveMirror, 'trade_type', ''),
                        'lng'                => array_get($approveMirror, 'lng', ''),
                        'lat'                => array_get($approveMirror, 'lat', ''),
                        'mac_price'          => array_get($approveMirror, 'mac_price', 0),
                        'mac_amount'         => array_get($approveMirror, 'mac_amount', 0),
                        'original_order_id'  => $orderId,
                        'discount_rate'      => array_get($approveMirror['approveData'] ?? $approveMirror, 'discount_rate') ?: null
                    ];
                    //todo 入库extend
                    #$makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
                    $makeOrder['third_actual_fee'] = $makeOrder['oil_money'];
                    $makeOrder['third_actual_price'] = $makeOrder['oil_price'];
                    //补录三方订单号
                    if(isset($approveInfo['third_order_id']) && $approveInfo['third_order_id']){
                        $makeOrder['third_order_id'] = $approveInfo['third_order_id'];
                    }
                    $result = $this->newOrderRepository->insertOrder($makeOrder);
                    if (empty($result)) {
                        throw new \Exception('创建订单失败!', $this->errorCodeService->editApproveError($this->errorCodeService->approveError(40305)));
                    }

                    $oilTime = $approveMirror['oil_time'] ?? date('Y-m-d H:i:s'); // 补录增加加油时间
                    $mirror['oil_time'] = $oilTime;
                    unset($mirror['card_level_name']);
                    unset($mirror['is_check_password']);
                    unset($mirror['card_type_name']);
                    unset($mirror['list_price']);
                    unset($mirror['order_channel_name']);
                    unset($mirror['order_type_name']);
                    unset($mirror['driver_source_name']);
                    $mirror['update_time'] = date("Y-m-d H:i:s", time());
                    $extRes = $this->newOrderExtRepository->insertOrderExt($mirror);
                    if (!$extRes) {
                        throw new \Exception('下单失败,请重试!', $this->errorCodeService->editApproveError($this->errorCodeService->approveError(40305)));
                    }

                    // foss扣费
                    $makeOrder['card_password'] = '';
                    $makeOrder['trade_from'] = $channel; //增加交易来源
                    $makeOrder['oil_time'] = $oilTime;
                    $makeOrder['force_pay_time'] = $oilTime;
                    $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
                    $makeOrder['original_order_id'] = $orderId;
                    if (in_array(
                        $makeOrder['org_code'],
                        explode(
                            ',',
                            env('REPAIR_RELATED_PAYMENT_ORGCODE')
                        )
                    )) {
                        $makeOrder['pay_extend_info'] = [
                            [
                                'company_id' => $approveMirror['pay_company_id'],
                                'money'      => $makeOrder['oil_money'],
                                'gun_money'  => array_get($approveMirror, 'mac_amount', 0),
                            ]
                        ];
                    }
                    $payInfo = $this->paymentService->fossPay($makeOrder, HistoryModel::COST_SUCCESS_ADD, '系统', 0);
                    $update = [
                        'order_id'                => $payInfo['order_id'],
                        'history_id'              => $payInfo['history_id'],
                        'approve_status'          => GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                        'approve_abnormal_reason' => $params['approval_reason'] ?? '',
                    ];

                    // 修改审核单状态
                    $result = $this->historyApproveRepository->approveUpdateById($params['id'], $update);
                    if (empty($result)) {
                        throw new \Exception('退款失败:修改审核单状态失败!', $this->errorCodeService->editApproveError(40306));
                    }

                    DB::connection('mysql_gas')->commit();

                } catch (\Exception $e) {
                    DB::connection('mysql_gas')->rollBack();
                    throw $e;
                }
            } elseif ($approveInfo['history_type'] == GasHistoryApproveModel::UPDATE_HISTORY) {  // 异常修改
                //原订单退款、流水作废
                DB::connection('mysql_gas')->beginTransaction();
                try {
                    $orderInfo = $this->newOrderRepository->getOrderInfoByLock([
                        'order_id' => $approveInfo['order_id']
                    ], [
                        'order_id',
                        'order_status',
                        'third_order_id',
                        'driver_phone',
                        'card_no',
                        'oil_money',
                        'supplier_code',
                        'order_sale_type'
                    ], true);

                    if (CouponDefine::isCouponPCode($orderInfo['supplier_code'])) {
                        throw new \Exception('审核失败:电子券订单不允许退款！', $this->errorCodeService->approveError(40306));
                    }

                    //G7WALLET-2215
                    /*if ($orderInfo['order_status'] == OrderModel::SUCCESS_PAY) {
                        $this->newOrderRepository->updateOrderByOrderId($orderInfo['order_id'], [
                            'order_status' => OrderModel::REFUNDING,
                            'updator'      => '系统',
                        ]);
                        $orderInfo['order_status'] = OrderModel::REFUNDING;
                    }*/

                    if (!in_array($orderInfo['order_status'], [OrderModel::SUCCESS_PAY, OrderModel::REFUNDING])) {
                        throw new Exception('审核失败:订单状态为' .
                            OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . '不允许退款!',
                            $this->errorCodeService->approveError(40304));
                    }
                    $approveMirror = json_decode($approveInfo['approve_mirror'], true);

                    /*try {
                        //G7WALLET-5119
                        $limitCondition = $checkPayLimit = [];
                        $limitCondition['oil_time'] = $approveMirror['orderInfo']['ext']['oil_time'];
                        $limitCondition['orgcode'] = $approveMirror['orderInfo']['org_code'];
                        $limitCondition['card_no'] = $approveMirror['orderInfo']['card_no'];
                        $limitCondition['money'] = $approveMirror['approveData']['pay_money'] ?? $approveMirror['approveData']['oil_money'];
                        $limitCondition['supplier_code'] = $approveMirror['orderInfo']['supplier_code'];
                        $checkPayLimit = $this->foss->checkPayLimit($limitCondition);
                        if (!isset($checkPayLimit['pay_limit'])) {
                            throw new \Exception('Foss退款失败，超过日限次数或单次限额！！！', 40504);
                        }
                        if ($checkPayLimit['pay_limit'] != 1) {
                            throw new \Exception('Foss退款失败，超过日限次数或单次限额！！！', 40505);
                        }
                    }catch (\Exception $exception){
                        throw new \Exception($exception->getMessage(), $exception->getCode());
                    }*/

                    /*$this->refundService->fossRefund([
                        'original_history_id'     => $approveMirror['orderInfo']['history_id'],
                        'order_id'                => $approveInfo['order_id'],
                        'update_history_log_type' => HistoryModel::COST_INVALID_2,
                    ], $params['operator'], true, false);
                    $this->historyApproveRepository->approveUpdateById($params['id'], [
                        'approve_status'          => GasHistoryApproveModel::REFUND_SUCCESS_AFTER_APPROVE,
                        'approve_abnormal_reason' => $params['approval_reason'] ?? '',
                    ]);
                    DB::connection('mysql_gas')->commit();
                    } catch (Throwable $t) {

                        (new FeiShu())->sendRefundFailed([
                            'category'      => '异常修改审核',
                            'order_id'      => $approveInfo['order_id'],
                            'station_name'  => $orderInfo['ext']['station_name'] ?? '',
                            'goods'         => $orderInfo['ext']['goods'] ?? '',
                            'pay_money'     => $orderInfo['oil_money'] ?? '',
                            'org_name'      => $orderInfo['ext']['org_name'] ?? '',
                            'card_no'       => $orderInfo['card_no'] ?? '',
                            'driver_phone'  => $orderInfo['driver_phone'] ?? '',
                            'failed_reason' => $t->getMessage(),
                        ]);
                        DB::connection('mysql_gas')->commit();
                        throw $t;
                    }*/

                    $logType = HistoryModel::COST_INVALID_2;
                    if ($orderInfo['order_sale_type'] == OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL) {
                        $logType = HistoryModel::COST_RESERVATION_REFUEL_REFUND;
                    }
                    // 修改、添加交易流水
                    $historyId = $this->historyService->updateHistoryAddRefund(
                        $approveMirror['orderInfo']['history_id'],
                        $logType
                    );
                    if (empty($historyId)) {
                        throw new Exception('退款失败,添加交易流水失败!', $this->errorCodeService->gotoRefund(40304));
                    }
                    // 修改订单状态
                    $result = $this->newOrderRepository->updateOrderByOrderId($approveInfo['order_id'], [
                        'order_status' => OrderModel::REFUND,
                        'updator'      => $params['operator'],
                    ]);
                    if (empty($result)) {
                        throw new Exception('退款失败,修改订单状态失败!', $this->errorCodeService->gotoRefund(40305));
                    }

                    // 补录新订单及流水
                    $orderId = $this->redisService->makeOrderId();
                    $tradeNo = $this->redisService->makeSerialNum();
                    /*DB::connection('mysql_gas')->beginTransaction();
                    try {*/

                    $makeOrder = [
                        'order_id'           => $orderId,
                        'order_sn'           => 'gms-patch' . time(),
                        'trade_no'           => $tradeNo,
                        'order_type'         => OrderModel::CHARGE_BY_ADMIN,
                        'order_channel'      => CardTradeConf::EXCEPTION_MODIFICATION,
                        'trade_mode'         => CardTradeConf::TRADE_MODE_ZYZ_ZYS_BL,
                        'order_status'       => OrderModel::WAIT_PAY,
                        'station_id'         => $approveMirror['orderInfo']['station_id'],
                        'station_code'       => $approveMirror['orderInfo']['station_code'],
                        'province_code'      => $approveMirror['orderInfo']['province_code'],
                        'city_code'          => $approveMirror['orderInfo']['city_code'],
                        'supplier_code'      => $approveMirror['orderInfo']['supplier_code'],
                        'org_code'           => $approveMirror['orderInfo']['org_code'],
                        'card_no'            => $approveMirror['orderInfo']['card_no'],
                        'card_type'          => $approveMirror['orderInfo']['card_type'],
                        'card_level'         => $approveMirror['orderInfo']['card_level'],
                        'driver_name'        => $approveMirror['orderInfo']['driver_name'],
                        'driver_source'      => $approveMirror['orderInfo']['driver_source'],
                        'driver_phone'       => $approveMirror['orderInfo']['driver_phone'],
                        'truck_no'           => $approveMirror['orderInfo']['truck_no'],
                        'oil_type'           => $approveMirror['approveData']['oil_type'],
                        'oil_name'           => $approveMirror['approveData']['oil_name'],
                        'oil_level'          => $approveMirror['approveData']['oil_level'],
                        'oil_unit'           => $approveMirror['approveData']['oil_unit'],
                        'oil_num'            => $approveMirror['approveData']['oil_num'],
                        'oil_price'          => $approveMirror['approveData']['pay_price'] ?? $approveMirror['approveData']['platform_price'],
                        'oil_money'          => $approveMirror['approveData']['pay_money'] ?? $approveMirror['approveData']['oil_money'],
                        'third_actual_price' => $approveMirror['approveData']['pay_price'] ?? $approveMirror['approveData']['platform_price'],
                        'third_actual_fee'   => $approveMirror['approveData']['pay_money'] ?? $approveMirror['approveData']['oil_money'],
                        'real_oil_num'       => $approveMirror['approveData']['real_oil_num'],
                        'service_price'      => $approveMirror['orderInfo']['service_price'],
                        'service_money'      => round(bcmul($approveMirror['orderInfo']['service_price'],
                            $approveMirror['approveData']['oil_num'], 3), 2),
                        'mirror'             => '',
                        'creator'            => $approveInfo['creator'],
                        'updator'            => $approveInfo['creator'],
                        'create_time'        => date("Y-m-d H:i:s"),
                        'pay_time'           => $approveMirror['orderInfo']['pay_time'],
                        'order_sale_type'    => OrderModel::ORDER_SALE_TYPE_REFUEL,
                    ];
                    $mirror = [
                        'order_id'          => $orderId,
                        'org_name'          => $approveMirror['orderInfo']['ext']['org_name'],
                        'account_type_name' => $approveMirror['orderInfo']['ext']['account_type_name'],
                        'account_name'      => $approveMirror['orderInfo']['ext']['account_name'],
                        'account_no'        => $approveMirror['orderInfo']['ext']['account_no'],
                        'station_name'      => $approveMirror['orderInfo']['ext']['station_name'],
                        'remark_name'       => $approveMirror['orderInfo']['ext']['remark_name'],
                        'station_address'   => $approveMirror['orderInfo']['ext']['station_address'],
                        'rebate_grade'      => $approveMirror['orderInfo']['ext']['rebate_grade'],
                        'supplier_name'     => $approveMirror['orderInfo']['ext']['supplier_name'],
                        'province_name'     => $approveMirror['orderInfo']['ext']['province_name'],
                        'city_name'         => $approveMirror['orderInfo']['ext']['city_name'],
                        'oil_type_name'     => $approveMirror['approveData']['oil_type_name'] ?? '',
                        'oil_name_name'     => $approveMirror['approveData']['oil_name_name'],
                        'oil_level_name'    => $approveMirror['approveData']['oil_level_name'] ?? '',
                        'goods'             => ($approveMirror['approveData']['oil_type_name'] ?? '') .
                            $approveMirror['approveData']['oil_name_name'] .
                            ($approveMirror['approveData']['oil_level_name'] ?? ''),
                        'gun_id'            => '',
                        'gun_name'          => '',
                        'tank_id'           => '',
                        'tank_name'         => '',
                        'supplier_price'    => $approveMirror['approveData']['price'] ?? $approveMirror['approveData']['supplier_price'],
                        'platform_price'    => $approveMirror['approveData']['pay_price'] ?? $approveMirror['approveData']['platform_price'],
                        'price_id'          => "",
                        'order_token'       => '', // 补录无websocket标识
                        'operator_id'       => $approveMirror['orderInfo']['ext']['operator_id'],
                        'app_station_id'    => $approveMirror['orderInfo']['ext']['app_station_id'],
                        'trade_type'        => $approveMirror['orderInfo']['ext']['trade_type'],
                        'lng'               => $approveMirror['orderInfo']['ext']['lng'],
                        'lat'               => $approveMirror['orderInfo']['ext']['lat'],
                        'mac_price'         => $approveMirror['approveData']['mac_price'] ?? 0.00,
                        'mac_amount'        => $approveMirror['approveData']['mac_amount'] ?? 0.00,
                        'oil_time'          => $approveMirror['orderInfo']['ext']['oil_time'],
                        'original_order_id' => $approveMirror['orderInfo']['order_id'],
                        'discount_rate' => $approveMirror['approveData']['discount_rate'] ?? null,
                        'ocr_truck_no_url'    => $approveMirror['orderInfo']['ext']['ocr_truck_no_url'] ?? '',
                    ];
                    $result = $this->newOrderRepository->insertOrder($makeOrder);
                    if (empty($result)) {

                        throw new Exception('创建订单失败!', $this->errorCodeService->editApproveError($this->errorCodeService->approveError(40305)));
                    }
                    $extRes = $this->newOrderExtRepository->insertOrderExt($mirror);
                    if (!$extRes) {

                        throw new Exception('下单失败,请重试!', $this->errorCodeService->editApproveError($this->errorCodeService->approveError(40305)));
                    }
                    $makeOrder['card_password'] = '';
                    $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
                    $makeOrder['force_pay_time'] = $makeOrder['pay_time'];
                    $makeOrder['oil_time'] = $approveMirror['orderInfo']['ext']['oil_time'];
                    $makeOrder['original_order_id'] = $approveMirror['orderInfo']['order_id']; //换签异常流程，需记录原始订单Id
                    $makeOrder['cancel_order_id'] = $approveMirror['orderInfo']['order_id'];
                    $payInfo = $this->paymentService->fossCancelPay($makeOrder, HistoryModel::COST_ABNORMAL, $approveInfo['creator']);

                    $this->historyApproveRepository->approveUpdateById($params['id'], [
                        'history_id'     => $payInfo['history_id'],
                        'approve_status' => GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                    ]);
                    $this->newOrderExtRepository->updateOrderExtByOrderId($orderId, [
                        'original_order_id' => $approveMirror['orderInfo']['order_id'],
                        'approve_remark'    => $approveInfo['remark'],
                    ]);
                    DB::connection('mysql_gas')->commit();
            } catch (Throwable $t) {
                    DB::connection('mysql_gas')->rollBack();
                    (new FeiShu())->sendMakeUpFailed([
                        'category'      => '异常修改审核',
                        'order_id'      => $approveInfo['order_id'],
                        'station_name'  => $orderInfo['ext']['station_name'] ?? '',
                        'goods'         => $orderInfo['ext']['goods'] ?? '',
                        'pay_money'     => $orderInfo['oil_money'] ?? '',
                        'org_name'      => $orderInfo['ext']['org_name'] ?? '',
                        'card_no'       => $orderInfo['card_no'] ?? '',
                        'driver_phone'  => $orderInfo['driver_phone'] ?? '',
                        'failed_reason' => $t->getMessage(),
                    ]);
                    throw new Exception("审核通过且补录失败,原因:" . $t->getMessage(),
                    $this->errorCodeService->approveError(40401));
                }
            }

            $this->redisService->delApprovePassLock($lockKey);
            // 退款审核通过发送通知
            if ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {
                event(new RefundNotice($params['id']));
            }
            return true;
        } catch (Exception $e) {
            $this->redisService->delApprovePassLock($lockKey);
            $updateArray = [
                'approve_status'          => GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                'approve_abnormal_reason' => $e->getMessage(),
                'updator'                 => Request::get('user_name', '系统'),
            ];
            $this->historyApproveRepository->approveUpdateById($params['id'], $updateArray);
            if ($approveInfo['history_type'] == GasHistoryApproveModel::UPDATE_HISTORY or
                (!empty($e->getCode()) and !empty($e->getMessage()))) {

                throw $e;
            }
            throw new Exception('审核通过且失败,原因:' . $e->getMessage(),
                $this->errorCodeService->approveError(40312));
        }
    }

    /**
     * 创建｜更新审核单
     *
     * @param $insertOrUpdateArray
     * @param string $id
     * @return mixed
     * @throws Exception
     */
    public function approveInsertOrUpdate($insertOrUpdateArray, $id = '')
    {
        if ($id) {
            $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $id], ['id', 'approve_status'], true);
            if (empty($approveInfo)) {
                throw new Exception('无效的审核单ID:' . $id . ',请核对', $this->errorCodeService->selectApproveError(40310));
            }
            if (in_array($approveInfo['approve_status'], [GasHistoryApproveModel::SUCCESS_AFTER_APPROVE, GasHistoryApproveModel::FINISH_AFTER_APPROVE, GasHistoryApproveModel::REJECT_APPROVE])) {
                throw new Exception('审核单已完成,不支持修改,请核实', $this->errorCodeService->selectApproveError(40311));
            }
            return $this->historyApproveRepository->approveUpdateById($id, $insertOrUpdateArray);
        } else {
            return $this->historyApproveRepository->approveInsert($insertOrUpdateArray);
        }
    }


    /**
     * 创建｜更新审核单
     *
     * @param $insertOrUpdateArray
     * @param string $id
     * @return mixed
     * @throws Exception
     */
    public function addApproveThirdOrder($params)
    {
        $id = $params['id'];
        $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $id], ['id', 'history_type','order_id','approve_status'], true);
        if (empty($approveInfo)) {
            throw new Exception('无效的审核单ID:' . $id . ',请核对', $this->errorCodeService->selectApproveError(40310));
        }
        if ($approveInfo['history_type'] != 1) {
            throw new Exception('只支持修改--异常订单类型为【补录】订单，请核实', $this->errorCodeService->selectApproveError(40311));
        }
        //审核通过的也同步
        if($approveInfo['order_id'] && $approveInfo['approve_status'] == GasHistoryApproveModel::SUCCESS_AFTER_APPROVE){
            // 修改订单状态
            $updateArray = [
                'third_order_id' => $params['third_order_id'],
                'update_time'=>date('Y-m-d H:i:s'),
                'updator'=>Request::get('user_name')
            ];
            $this->newOrderRepository->updateOrderByOrderId($approveInfo['order_id'], $updateArray);
        }

        return $this->historyApproveRepository->approveUpdateById($id, [
            'third_order_id'=>$params['third_order_id'],
            'update_time'=>date('Y-m-d H:i:s'),
            'updator'=>Request::get('user_name')
        ]);
    }

    public function checkOrderIsReceipt($params)
    {
       $checkText = $this->foss->checkOrderIsReceipt($params);
        Log::info('请求foss异常修改申请检测发票单接口，order_id:' . $params['order_id'] . '返回结果:' . $checkText);
        return $checkText;
    }

    /**
     * @throws Throwable
     */
    public function refundCustomerApprove($params): bool
    {
        $source = Request::get('order_channel');
        $sourceName = Request::get('order_channel_enum')[$source];
        $operator = Request::get('user_name');
        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $params['order_id']]);
        if (empty($orderInfo)) {
            throw new Exception('订单不存在,请核实后重试!', $this->errorCodeService->editApproveError(40301));
        }
        $insertArray = [
            'order_id'            => $params['order_id'],
            'original_history_id' => $params['order_id'], // 退款单流水号
            'history_type'        => GasHistoryApproveModel::CANCEL_HISTORY,
            'approve_mirror'      => json_encode([
                'source_name'            => $sourceName,
                'oil_unit'               => $orderInfo['oil_unit'],
                'oil_unit_name'          => OrderModel::$OIL_UNIT[$orderInfo['oil_unit']],
                'money'                  => round(
                    bcmul($orderInfo['ext']['supplier_price'], $orderInfo['real_oil_num'], 3),
                    2
                ),
                //站点应收金额
                'pay_money'              => $orderInfo['oil_money'],
                // 客户应付金额
                'price'                  => $orderInfo['ext']['supplier_price'],
                //站点应收单价
                'pay_price'              => $orderInfo['oil_price'],
                //客户应付单价
                'oil_num'                => $orderInfo['oil_num'],
                'oil_time'               => $orderInfo['pay_time'],
                'driver_name'            => $orderInfo['driver_name'],
                'card_account_type'      => 1,
                'card_account_type_name' => '现金账户',
                'ticket_image'           => !empty($params['ticket_image']) ? Upload::getSignUrl(
                    $params['ticket_image']
                ) : '',
                'truck_image'            => !empty($params['truck_image']) ? Upload::getSignUrl(
                    $params['truck_image']
                ) : '',
                'other_image'            => !empty($params['other_image']) ? Upload::getSignUrl(
                    $params['other_image']
                ) : '',
                'station_code'           => $orderInfo['station_code'],
                'station_name'           => $orderInfo['ext']['station_name'],
                'supplier_name'          => $orderInfo['ext']['org_name'],
                'province_code'          => $orderInfo['province_code'],
                'city_code'              => $orderInfo['city_code'],
                'province_name'          => $orderInfo['ext']['province_name'],
                'city_name'              => $orderInfo['ext']['city_name'],
                'gun_id'                 => $orderInfo['ext']['gun_id'],
                'gun_name'               => $orderInfo['ext']['gun_name'],
                'tank_id'                => $orderInfo['ext']['tank_id'],
                'tank_name'              => $orderInfo['ext']['tank_name'],
                'oil_type'               => $orderInfo['oil_type'],
                'oil_type_name'          => $orderInfo['ext']['oil_type_name'],
                'oil_name'               => $orderInfo['oil_name'],
                'oil_name_name'          => $orderInfo['ext']['oil_name_name'],
                'oil_level'              => $orderInfo['oil_level'],
                'oil_level_name'         => $orderInfo['ext']['oil_level_name'],
                'goods'                  => $orderInfo['ext']['goods'],
                'app_station_id'         => $orderInfo['ext']['app_station_id'] ?? '',
                'trade_type'             => $orderInfo['ext']['trade_type'] ?? 1,
                'lng'                    => $orderInfo['ext']['lng'] ?? '',
                'lat'                    => $orderInfo['ext']['lat'] ?? '',
                'mac_price'              => $orderInfo['ext']['mac_price'] ?? 0,
                'mac_amount'             => $orderInfo['ext']['mac_amount'] ?? 0,
                'org_code'               => $orderInfo['org_code'],
            ], JSON_UNESCAPED_UNICODE),
            'approve_status'      => GasHistoryApproveModel::WAIT_APPROVE,
            'source'              => $source,
            'remark'              => $params['remark'] ?? '',
            'truck_no'            => $orderInfo['truck_no'],
            'card_no'             => $orderInfo['card_no'],
            'driver_phone'        => $orderInfo['driver_phone'],
            'province_code'       => $orderInfo['province_code'],
            'city_code'           => $orderInfo['city_code'],
            'station_id'          => $orderInfo['station_id'],
            'third_order_id'      => $orderInfo['third_order_id'],
            'creator'             => $operator,
            'updator'             => $operator,
            'refund_system'       => 2,
        ];
        $approveId = $this->historyApproveRepository->approveInsert($insertArray);
        if (empty($approveId)) {
            throw new Exception(
                '退款失败:创建退款审核单失败,请重试!', $this->errorCodeService->editApproveError(40306)
            );
        }
        try {
            $this->adapter->refundCustomer([
                'order_id' => $params['order_id'],
                'org_code' => $orderInfo['org_code'],
                'reason'   => $params['remark'] ?? '',
            ]);
            $this->historyApproveRepository->approveUpdateById($approveId, [
                'approve_status' => GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                'updator'        => '系统',
            ]);
            return true;
        } catch (Throwable $throwable) {
            $this->historyApproveRepository->approveUpdateById($approveId, [
                'approve_status' => GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                'updator'        => '系统',
                'approve_abnormal_reason' => $throwable->getMessage(),
            ]);
            throw $throwable;
        }
    }

    public function checkIfRefundRecordExists(array $params): bool
    {
        return (bool)$this->historyApproveRepository->getOneApproveInfoByParams([
            'third_order_id' => $params['third_order_id'],
            'refund_system'  => 2,
        ], ['id'], false);
    }
}