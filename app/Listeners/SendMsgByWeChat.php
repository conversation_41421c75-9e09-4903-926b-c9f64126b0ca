<?php

namespace App\Listeners;

use App\Events\OrderPaySuccess;
use App\Jobs\SendWxTemplateMessage;
use App\Library\Helper\Common;
use App\Repositories\CardRepository;
use App\Repositories\GasUserWeChatRepository;
use App\Repositories\StationRepository;
use App\Repositories\YunPrintRepository;
use App\Services\HistoryService;
use App\Services\MessageService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class SendMsgByWeChat implements ShouldQueue
{
    public $queue = 'message-queue';

    protected $gasUserWeChatRepository;
    protected $gasCard;
    protected $stationRepository;
    protected $historyService;
    protected $printRepository;
    protected $messageService;


    /**
     * Create the event listener.
     *
     * @param GasUserWeChatRepository $gasUserWeChatRepository
     */
    public function __construct
    (
        GasUserWeChatRepository $gasUserWeChatRepository,
        CardRepository $gascard,
        StationRepository $station,
        YunPrintRepository $print,
        MessageService $messageService,
        HistoryService $historyService
    )
    {
        $this->gasUserWeChatRepository = $gasUserWeChatRepository;
        $this->gasCard = $gascard;
        $this->stationRepository = $station;
        $this->printRepository = $print;
        $this->messageService = $messageService;
        $this->historyService = $historyService;
    }

    /**
     * Handle the event
     *
     * @param OrderPaySuccess $event
     * @return void
     */
    public function handle(OrderPaySuccess $event)
    {
        // 获取订单信息
        $orderInfo = $event->orderInfo;
        $orderMirror = json_decode($orderInfo['mirror'], true);
        // 获取加油员ID
        $doperId = $event->doperId;
        // 获取openId
        $result = $this->gasUserWeChatRepository->getOpenIdsByParams(['gas_user_id' => $doperId]);


        Log::info("getOpenId".var_export($result,true)."doperId:".$doperId);

        //小票机站点，调用云打印
        $this->sendPrint($orderInfo['order_id'],$orderInfo);

        Log::info("secord".var_export($result,true)."doperId:".$doperId);

        // 管理员GMS后台支付成功,不发送通知
        if ( empty($result) ) {
            return;
        }

        $openIdBatch = array_column($result, 'openid');
        $msg = [
            'keyword1' => [
                'value' => $orderInfo['order_id'],
                'color' => '#173177'
            ],
            'keyword2' => [
                'value' => sprintf('%.2f', round(bcadd($orderInfo['oil_money'], $orderInfo['service_money'], 3), 2)) . '元',
                'color' => '',
            ],
            'keyword3' => [
                'value' => empty($orderMirror['remark_name']) ? $orderMirror['station_name'] : $orderMirror['remark_name'],
                'color' => '',
            ],
            'keyword4' => [
                'value' => $orderInfo['pay_time'],
                'color' => ''
            ]
        ];
        if( in_array(App()->environment() ,['pro','pre','prod']) ) {
            $msg = [
                'character_string13' => [
                    'value' => $orderInfo['order_id'],
                    'color' => ''
                ],
                'amount8' => [
                    'value' => sprintf('%.2f', round(bcadd($orderInfo['oil_money'], $orderInfo['service_money'], 3), 2)) . '元',
                    'color' => '',
                ],
                'thing3' => [
                    'value' => empty($orderMirror['remark_name']) ? $orderMirror['station_name'] : $orderMirror['remark_name'],
                    'color' => ''
                ],
                'time12' => [
                    'value' => $orderInfo['pay_time'],
                    'color' => ''
                ]
            ];
        }
        foreach ($openIdBatch as $openId) {
            dispatch((new SendWxTemplateMessage($openId, config('wechat')['gzh_msg_template']['successful_collection'], $msg))->onQueue('send-wx-template-message'));
        }

        return;
    }

    /**
     * 小票机站点调用飞鹅云打印机
     */
    public function sendPrint($history_id = '',$orderInfo = [])
    {
        /*$history = $this->historyService->getHistoryInfo(['id'=>$history_id]);
        if(!$history){
            return false;
        }*/
        $unit = $this->getUnit(array_get($orderInfo,"ext.goods",''));

        $station_id = $orderInfo['station_id'];
        $station = $this->stationRepository->getOneStationBasicInfoByParams(['id'=>$station_id]);
        if( !$station ){
            return false;
        }

        $print_sn = array_get($station,"print_sn","");
        if(empty($print_sn)){
            return false;
        }
        $card_no = array_get($orderInfo,'card_no','');
        if(empty($card_no)){
            return false;
        }
        $cardInfo = $this->gasCard->getOneCardByParams(['card_no'=>$card_no]);
        if(!$cardInfo){
            return false;
        }
        // 客户名称显示
        if ($cardInfo['orgname_show'] == 2) {
            $result['orgname_show'] = true;
            $result['orgname'] = $orderInfo['ext']['org_name'];
        } else {
            $result['orgname_show'] = false;
        }
        // 显示价格
        $result['price_show'] =  true;

        if($cardInfo['card_no'] > 0) {
            $result['price_show']  = $cardInfo['price_show'] == 1 ? true : false;
        }
        $result['serial_num'] = array_get($orderInfo,'trade_no','');
        $result['station_name'] = $station['station_name'];
        $result['card_no'] = $card_no;
        $result['truck_no'] = array_get($orderInfo,'truck_no','');
        $result['oil_name'] = array_get($orderInfo,'ext.goods','');
        $result['mac_amount'] = array_get($orderInfo,'ext.mac_amount','');
        $result['mac_price'] = array_get($orderInfo,'ext.mac_price','');
        $result['oil_num'] =  array_get($orderInfo,'oil_num','').$unit;
        $result['price'] = array_get($orderInfo,'oil_price','').'元/'.$unit;
        $result['pay_money'] = array_get($orderInfo,'oil_money','').'元';
        $result['createtime'] = array_get($orderInfo,'ext.oil_time','');

        $station_ids_arr = [
            'b6b3cb2c77c311edb42f3a62f857655c',// 生产环境 市内-中国石化壳牌勤丰加油站
            '59420cd0a5ca11edb5401aeb101d88b2',// 生产环境 市内-中国石化壳牌陆家加油站
            '7d1ff28aa5c911edaa9a86ffff50d874',// 生产环境 市内-中国石化壳牌龙马加油站
            '845cb5e8a60411edbe9c1aeb101d88b2',// 生产环境 市内-中国石化壳牌四通加油站
            '34eda624a5c911ed9f7086ffff50d874',// 生产环境 市内-中国石化壳牌昆杨加油站
            '98016cf4c51111ebbe78ce78129a5690',// 生产环境 测试站 线上回归测试站testA
            'e137eab8ede711ee9a8e06e5a4c5600d',//生产环境 市内-中海油星城星祥站
            'deffb452ed7d11eeb3676a680e3fded9',//生产环境 G15沈海-朱桥服务区加油站
            'b8ae6510a13211ee885676dc4363b9cb',//生产环境 中石油嘉定第四加油站-中油高速通
            '7a7c907cd77011eebb674220afdf6291',// 生产环境 市内-江苏南京江宁蓝天路加油站
            '08c522f6cc0b11eabea9f274353b6cab',//测试环境 test001
        ];
        if(in_array($station_id,$station_ids_arr)) {
            $print_style = 'style-1';
        } else {
            $print_style = 'style-2';
        }
        $res = $this->messageService->pushFeiEYunPrint($result,$print_sn,$print_style);

        $code = array_get($res,"code",-1);
        if($code != 0){
            $msg = $code.":".array_get($res,"msg","");
            $code = 127;
        }else{
            $msg = array_get($res,"msg","");
        }
        $logArr = [
            'id'           => md5(Common::uuid()),
            'history_id'   => $history_id,
            'station_id'   => $station['id'],
            'card_no'      => $card_no,
            'truck_no'     => array_get($orderInfo,'truck_no',''),
            'station_name' => $station['station_name'].'-foss加注',
            'print_sn'     => $print_sn,
            'order_info'   => $res['printInfo'],
            'api_code'     => $code,
            'api_msg'      => $msg,
            'api_data'     => $res['data'],
            'pay_money'    => array_get($orderInfo,'oil_money',''),
            'createtime'   => date("Y-m-d H:i:s",time())
        ];
        $this->printRepository->insertOrder($logArr);
    }


    public function getUnit($oil_name)
    {
        if (stripos($oil_name, "天然气") !== false) {
            $unit = 'Kg';
            if (stripos($oil_name, "压缩天然气") !== false || stripos($oil_name, "立方米") !== false) {
                $unit = '立方';
            }
            if (stripos($oil_name, "液化天然气") !== false) {
                $unit = 'Kg';
            }
        } else {
            $unit = "升";
        }
        return $unit;
    }
}
