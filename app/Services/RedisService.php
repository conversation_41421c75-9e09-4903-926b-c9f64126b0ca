<?php


namespace App\Services;


use App\Models\ElectricityOrderModel;
use App\Models\OrderModel;
use App\Repositories\NewOrderRepository;

class RedisService
{
    protected $redis;

    public function __construct
    (
    )
    {
        $this->redis = app('redis');
    }

    /**
     * 补录、退款审核加锁
     *
     * @param $approve_id
     * @param $expire = 300
     * @throws \Exception
     */
    public function addApprovePassLock($approve_id,$expire = 3000)
    {
        $isLock=$this->setLock($approve_id,$expire);
        if(!$isLock) {
            throw new \Exception('当前审核单正在处理中,请稍后!',2);
        }
        /*        if ($this->redis->get($key)) {
            throw new \Exception('当前审核单正在处理中,请稍后!');
        }
        // 默认生存时间300秒
        $this->redis->setex($key,300,$approve_id);*/
    }

    public function setLock($approve_id,$expire = 3000)
    {
        $key = 'approve_'.(string)$approve_id;
        $isLock = $this->redis->setnx( $key, 1 );
        if( !$isLock ){
            //本来这里没必要处理了，但是为了防止特殊情况，锁设置成功，但是Redis::expire设置缓存时间失败，这样会导致死锁
            if( $this->redis->ttl( $key ) == -1  ){
                $this->redis->del( $key );
                $isLock = $this->redis->setnx( $key, 1 );
                if( $isLock ){
                    $this->redis->expire( $key , $expire );
                }
            }
        }else{
            //获取锁成功，设置生存时间
            $this->redis->expire( $key , $expire );
        }
        return $isLock ? true : false;
    }


    /**
     * 补录、退款审核解锁  Redis::del( self::REDIS_LOCK.  $key );
     *
     * @param $approve_id
     */
    public function delApprovePassLock($approve_id)
    {
        $key = 'approve_'.(string)$approve_id;
        $this->redis->del( $key );
    }

    /**
     * 创建订单时查验token
     *
     * @param $token
     * @return mixed
     * @throws \Exception
     */
    public function parseCreateOrderToken($token)
    {
        if ($parseInfo = $this->redis->get('order_token_'.$token)) {
            $token = json_decode($parseInfo, true);
            if (empty($token)) {
                throw new \Exception('下单token数据格式异常!',2);
            }
            return $token;
        }

        throw new \Exception('二维码已过期，请重新扫码!',2);
    }

    /**
     * 删除创建订单时验证token
     *
     * @param $token
     */
    public function delCreateOrderToken($token)
    {
        if ($parseInfo = $this->redis->get('order_token_'.$token)) {
            $this->redis->del('order_token_'.$token);
        }
    }

    /**
     * 生成订单号
     *
     * @return string
     */
    public function makeOrderId()
    {
        /*
         * 20200825110632 10000
         */
        $key = 'make_order_id_incr_' . date("Ymd");
        if ($this->redis->get($key)) {
            $incr = $this->redis->incr($key);
        } else {
            $incr = 10000;
            $orderInfo = (new OrderModel())->select(['order_id'])
                                           ->where('order_id', 'like', date("Ymd") . '%')
                                           ->orderBy('create_time', 'desc')
                                           ->limit(1)
                                           ->first();
            if ($orderInfo and !empty($orderInfo->order_id)) {
                $incr = substr($orderInfo->order_id, 14, 5) + 1;
            }
            // 由于get时key存在，incr时key可能已不存在而incr机制当key不存在时默认设置key过期时间为永不过期，为保证incr时key一定存在，故对key延长至两天防止时区异常
            $this->redis->setex($key, 172800, $incr);
        }

        return substr(date('YmdHis') . $incr, 0, 19);
    }

    /**
     * 生成gas_history表serial_num
     *
     * @return string
     */
    public function makeSerialNum()
    {
        /*
         * 20090110000
         */
        $key = 'make_history_serial_num_incr_' . date('Ymd');
        if ($this->redis->get($key)) {
            $incr = $this->redis->incr($key);
        } else {
            $incr = 10000;
            // 由于get时key存在，incr时key可能已不存在而incr机制当key不存在时默认设置key过期时间为永不过期，为保证incr时key一定存在，故对key延长至两天防止时区异常
            $this->redis->setex($key, 172800, $incr);
        }

        return substr(date('Ymd'), 2) . $incr;
    }

    /**
     * 设置缓存key的值加一
     */
    public function addKeyNum($key = '')
    {
        $incr = $this->redis->incr($key);
        return $incr;
    }

    public function getValByKey($key = '')
    {
        return $this->redis->get($key);
    }

    public function delValByKey($key = '')
    {
        return $this->redis->del($key);
    }


    /**
     * 创建OA下单锁
     *
     * @param $lockCondition
     * @throws \Exception
     */
    public function setOaCreateOrderLock(string $lockCondition)
    {
        if ($this->redis->get('oa_create_order_' . $lockCondition)) {
            throw new \Exception('订单创建中,请稍后',2);
        }

        $this->redis->setex('oa_create_oder_' . $lockCondition, 300, $lockCondition);
    }

    /**
     * 删除OA下单锁
     *
     * @param string $lockCondition
     */
    public function delOaCreateOrderLock(string $lockCondition)
    {
        if ($this->redis->get('oa_create_order_' . $lockCondition)) {
            $this->redis->del(['oa_create_order_' . $lockCondition]);
        }
    }

    const LOCK_SUCCESS = 'OK';
    const IF_NOT_EXIST = 'NX';
    const MILLISECONDS_EXPIRE_TIME = 'PX';//PX millisecond ：设置键的过期时间为 millisecond 毫秒

    const RELEASE_SUCCESS = 1;
    /**
     * 尝试获取锁
     * @param String $key               锁
     * @param String $value         请求id
     * @param int $expireTime           过期时间(单位秒)
     * @return bool                     是否获取成功
     */
    public function tryGetLock($key, $value, $expireTime)
    {
        $expireTime = $expireTime * 1000;
        $result = $this->redis->set($key, $value, self::MILLISECONDS_EXPIRE_TIME, $expireTime, self::IF_NOT_EXIST);
        return self::LOCK_SUCCESS === (string)$result ? true : false;
    }

    /**
     * 使用 Lua 脚本原子性地释放锁.
     * @param String $key
     * @param String $value
     * @return bool
     * 客户端 A 加锁成功后一段时间再来解锁，在执行删除 del 操作的时候锁过期了，而且这时候又有其他客户端
     * B 来加锁 (这时候加锁是肯定成功的，因为客户端 A 的锁过期了), 这是客户端 A 再执行删除 del 操作，会把客户端 B 的锁给清了.
     */
    public function releaseLock( $key, $value)
    {
        $lua = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

        $result = $this->redis->eval($lua, 1, $key, $value);
        return self::RELEASE_SUCCESS === $result;
    }

    public function makeOrderIdForElectricity()
    {
        /*
         * 20200825110632 10000
         */
        $key = 'make_electricity_order_id_incr_' . date("Ymd");
        if ($this->redis->get($key)) {
            $incr = $this->redis->incr($key);
        } else {
            $incr = 10000;
            $orderInfo = (new ElectricityOrderModel())->select(['order_id'])
                                                      ->where('order_id', 'like', date("Ymd") . '%')
                                                      ->orderBy('create_time', 'desc')
                                                      ->limit(1)
                                                      ->first();
            if ($orderInfo and !empty($orderInfo->order_id)) {
                $incr = substr($orderInfo->order_id, 14, 5) + 1;
            }
            // 由于get时key存在，incr时key可能已不存在而incr机制当key不存在时默认设置key过期时间为永不过期，为保证incr时key一定存在，故对key延长至两天防止时区异常
            $this->redis->setex($key, 172800, $incr);
        }
        return substr(date('YmdHis') . $incr, 0, 19);
    }
}