<?php


namespace App\Repositories;


use App\Http\Defines\CommonError;
use App\Servitization\FossStation;
use RuntimeException;
use Throwable;


class OilerRepository extends EloquentRepository
{
    /**
     * 获取加油员用户信息
     * @param string $uid 加油员用户ID
     * @param array $checkData 需要验证字段的rules及messages,例：['rules' => ['xxx' => 'in'], 'messages' => ['xxx.in' => '']]
     * @param bool $throwException 验证失败时是否直接抛出异常
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 5:20 下午
     */
    public static function getOilerInfo(string $uid, array $checkData = [], bool $throwException = true): array
    {
        try {

            $responseData = (new FossStation())->getOilerInfo($uid);
            self::checkArrayField($responseData, $checkData, $throwException);
        } catch (Throwable $exception) {

            throw new RuntimeException("", CommonError::SYSTEM_ERROR);
        }

        return $responseData;
    }
}