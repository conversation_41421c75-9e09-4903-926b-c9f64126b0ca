<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Http\Controllers;

use App\Exceptions\ParamInvalidException;
use App\Http\Defines\FossRuleDefine;
use App\Library\Request;
use App\Services\H5Service;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Throwable;

class H5Controller extends Controller
{
    private $h5Service;

    public function __construct(H5Service $h5)
    {
        $this->h5Service = $h5;
    }

    //检查司机是否有待支付订单
    public function waitPayment()
    {
        $params = Request::all();
        $header = Request::httpHeaders();
        $params['user_id'] = $header['x-g7-api-uid'][0];
        $data = $this->h5Service->getWaitPayment($params);
        return $this->success($data, true, "success");
    }

    //获取订单详情
    public function getOrderDetail()
    {
        $params = Request::all();
        $rule = [
            'order_no' => 'required',
            'org_code' => 'required|alpha_num',
        ];
        $messages = [
            'order_no.required'  => "订单号不能为空",
            'org_code.required'  => "机构代码不能为空",
            'org_code.alpha_num' => "机构代码格式不正确",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->h5Service->orderDetail($params);
        return $this->success($data, true, "success");
    }

    /**
     * 获取订单列表信息
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/2 5:18 下午
     */
    public function getOrderList(): JsonResponse
    {
        $params = Request::all();
        $rule = [
            'order_nos'   => 'required|array|between:1,100',
            'order_nos.*' => 'required|numeric',
            'org_code'    => 'required|alpha_num',
        ];
        $messages = [
            'order_nos.required'   => "订单号数组不能为空",
            'order_nos.array'      => "订单号数组类型不正确",
            'order_nos.between'    => "订单号数组长度需在1,100之间",
            'order_nos.*.required' => "订单号数组内子元素不能为空",
            'order_nos.*.numeric'  => "订单号数组内子元素不正确",
            'org_code.required'    => "机构代码不能为空",
            'org_code.alpha_num'   => "机构代码格式不正确",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->h5Service->orderList($params);
        return $this->success($data, true, "success");
    }

    //刷新订单状态
    public function refreshOrder()
    {
        $params = Request::all();
        $rule = [
            'order_no' => 'required',
        ];
        $messages = [
            'order_no.required' => "订单号不能为空",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->h5Service->reloadOrder($params);
        return $this->success($data, true, "success");
    }

    //取消订单
    public function cancelOrder()
    {
        $params = Request::all();
        $rule = [
            'order_no' => 'required',
        ];
        $messages = [
            'order_no.required' => "订单号不能为空",
        ];
        $this->validator($params, $rule, $messages);
        //todo 请求卡车订单状态接口
        $data = $this->h5Service->cancelOrder($params);
        return $this->success($data, true, "success");
    }

    //三方支付回调
    public function payCallBack()
    {
        $params = Request::all();
        $rule = [
            'order_no'       => 'required',
            'money'          => 'required',
            'user_phone'     => 'required',
            'third_order_id' => 'required'
        ];
        $messages = [
            'order_no.required'       => "订单号不能为空",
            'money.required'          => "支付金额不能为空",
            'user_phone.required'     => "手机号不能为空",
            'third_order_id.required' => "三方订单号不能为空",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->h5Service->payCallBack($params);
        return $this->success($data, true, "success");
    }

    //向三方推送订单
    public function pushOrder2Third()
    {
        $params = Request::all();
        $rule = [
            'order_no' => 'required',
        ];
        $messages = [
            'order_no.required' => "订单号不能为空",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->h5Service->pushOrder2Third($params);
        return $this->success($data, true, "success");
    }

    /**
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:52 下午
     */
    public function addDriverRefundApplicationRecord(): JsonResponse
    {
        $params = Request::all();
        $rule = [
            'order_no'           => 'required|numeric',
            'platform_order_no'  => 'required|alpha_dash|min:1|max:50',
            'applicant'          => 'required|min:1|max:85',
            'application_reason' => 'required|min:1|max:85',
            'orgcode'            => 'required',
        ];
        $messages = [
            'order_no.required'            => "订单号不能为空",
            'order_no.numeric'             => "订单号非法",
            'platform_order_no.required'   => '接入平台订单号不能为空',
            'platform_order_no.alpha_dash' => '接入平台订单号非法',
            'platform_order_no.min'        => '接入平台订单号非法',
            'platform_order_no.max'        => '接入平台订单号非法',
            'applicant.required'           => '申请人不能为空',
            'applicant.min'                => '申请人非法',
            'applicant.max'                => '申请人非法',
            'application_reason.required'  => '申请原因不能为空',
            'application_reason.min'       => '申请原因非法',
            'application_reason.max'       => '申请原因非法',
            'orgcode.required'             => '司机所属机构不能为空',
        ];
        $this->validator($params, $rule, $messages);
        $this->h5Service->addDriverRefundApplicationRecord($params);
        return $this->success([], true, "成功");
    }

    /**
     * 开放平台客户取消订单
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/6 3:47 下午
     */
    public function cancelOrderForPlatform(): JsonResponse
    {
        $params = Request::all();
        $rule = [
            'order_no' => 'required|numeric',
            'org_code' => 'required|alpha_num',
        ];
        $messages = [
            'order_no.required'  => "订单号不能为空",
            'order_no.numeric'   => "订单号格式不正确",
            'org_code.required'  => "机构代码不能为空",
            'org_code.alpha_num' => "机构代码格式不正确",
        ];
        $this->validator($params, $rule, $messages);
        $this->h5Service->cancelOrderForPlatform($params);
        return $this->success([], true, "success");
    }

    /**
     * 开放平台用户下单
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/8 3:01 下午
     */
    public function createOrder(Request $request): JsonResponse
    {
        $params = $request->all();
        $rule = [
            'third_order_id'  => 'required',
            'card_no'         => 'required|numeric',
            'station_id'      => 'required|string',
            'oil_name'        => 'required|string',
            'oil_type'        => 'nullable|string',
            'oil_level'       => 'nullable|string',
            'oil_num'         => 'required|numeric',
            'oil_price'       => 'required|numeric|range_price',
            'oil_money'       => 'required|numeric',
            'oil_time'        => 'nullable|string|date',
            'amount_gun'      => 'numeric',
            'gun_number'      => 'numeric',
            'price_gun'       => 'numeric',
            'driver_name'     => 'nullable|string|max:20',
            'driver_phone'    => 'nullable|string|max:11',
            'truck_no'        => 'nullable|string|max:20',
            'access_id'       => 'required',
            'third_price'     => 'nullable|numeric',
            'third_pay_money' => 'nullable|numeric',
            'org_code'        => 'required|alpha_num',
        ];
        $messages = [
            'card_no.required'        => '账户不能为空',
            'card_no.numeric'         => '账户格式不正确',
            'third_order_id.required' => '三方单号不能为空',
            'station_id.required'     => '请选择油站',
            'oil_name.required'       => '请选择油品',
            'oil_price.required'      => '油品单价不能为空',
            'oil_price.min'           => '油品单价最低'.FossRuleDefine::MIN_PRICE.'元',
            'oil_price.max'           => '油品单价最高'.FossRuleDefine::MAX_PRICE.'元',
            'oil_num.required'        => '加油数量必传',
            'oil_num.numeric'         => '加油数量不正确',
            'oil_money.required'      => '加油金额必传',
            'oil_money.numeric'       => '加油金额不正确',
            'access_id.required'      => '开放平台用户ID必传',
            'third_price.numeric'     => '司机实付单价不正确',
            'third_pay_money.numeric' => '司机实付金额不正确',
            'org_code.required'       => "机构代码不能为空",
            'org_code.alpha_num'      => "机构代码格式不正确",
            'price_gun.numeric'       => '油机单价格式不正确',
            'gun_number.numeric'      => '油枪号格式不正确',
            'amount_gun.numeric'      => '油机金额格式不正确',
        ];
        $this->validator($params, $rule, $messages);
        $result = $this->h5Service->createOrder($params);
        return $this->success($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/8 4:36 下午
     */
    public function payOrder(Request $request): JsonResponse
    {
        $params = $request->all();
        $rule = [
            'order_no' => 'required|numeric',
            'password' => 'nullable|string',
            'org_code' => 'required|alpha_num',
        ];
        $message = [
            'order_no.required'  => '订单号必传!',
            'order_no.numeric'   => '订单号格式错误!',
            'password.string'    => '账户密码格式不正确',
            'org_code.required'  => "机构代码不能为空",
            'org_code.alpha_num' => "机构代码格式不正确",
        ];
        $this->validator($params, $rule, $message);
        $data = $this->h5Service->payOrder($params);
        return $this->success($data);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/8 4:36 下午
     */
    public function refreshVerificationCertificate(Request $request): JsonResponse
    {
        $params = $request->all();
        $rule = [
            'order_no' => 'required|numeric',
            'org_code' => 'required|alpha_num',
        ];
        $message = [
            'order_no.required'  => '订单号必传!',
            'order_no.numeric'   => '订单号格式错误!',
            'org_code.required'  => "机构代码不能为空",
            'org_code.alpha_num' => "机构代码格式不正确",
        ];
        $this->validator($params, $rule, $message);
        $data = $this->h5Service->refreshVerificationCertificate($params);
        return $this->success($data);
    }
}