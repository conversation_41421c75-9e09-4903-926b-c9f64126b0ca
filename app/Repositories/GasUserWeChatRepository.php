<?php


namespace App\Repositories;


use App\Models\GasUserWeChatModel;

class GasUserWeChatRepository
{
    protected $userWeChatModel;

    public function __construct
    (
        GasUserWeChatModel $userWeChatModel
    )
    {
        $this->userWeChatModel = $userWeChatModel;
    }

    public function getOpenIdsByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->userWeChatModel->getOpenIdsByParams($whereParams, $select, $toArray);
    }
}