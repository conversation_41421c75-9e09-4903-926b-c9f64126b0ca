<?php


namespace App\Http\Controllers\History;


use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Services\HistoryApproveService;

class HistoryApproveController extends Controller
{
    protected $historyApproveService;

    public function __construct
    (
        HistoryApproveService $historyApproveService
    )
    {
        $this->historyApproveService = $historyApproveService;
    }

    /**
     * 分页获取审核结果
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getApprovePaginate()
    {
        $field = [
            'history_type'      => '', // 异常订单类型
            'approve_status'    => '', // 审核单状态
            'station_id'        => '', // 站点ID
            'card_no'           => '', // 账号 原卡号
            'truck_no'          => '', // 车牌号
            'driver_phone'      => '', //司机手机号
            'order_id'          => '', // 订单号
            'creator'           => '', //创建人
            'ge_createtime' => '', // 创建开始时间
            'le_createtime' => '', // 创建结束时间
            'third_order_id' => '', // 三方订单号
            'order_sale_type' => '', //订单类型
            'page'              => 1,
            'limit'             => 10,
            'refund_system'    => '',
        ];

        $params = $this->getParamAll($field);

        try {
            $result = $this->historyApproveService->getApprovePaginate($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 手机管站获取退款审核单列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMtApprovePaginate()
    {
        $field = [
            'station_id' => '',
            'source' => 5, // PDA加油员端
            'status' => '',
            'province_code' => '',
            'city_code' => '',
            'history_type' => 3, // 仅查看退款审核
            'ge_create_time' => '',
            'le_create_time' => '',
            'page' => 1,
            'limit' => 10,
        ];

        $rule = [
            'status' => 'in:1,2,3',
            'ge_create_time' => 'date_format:Y-m-d',
            'le_create_time' => 'date_format:Y-m-d',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule);
            $result = $this->historyApproveService->getMtApprovePaginate($params);

            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 审核单详情
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getApproveDetail()
    {
        $field = [
            'id' => null,
        ];

        $rule = [
            'id' => 'required|integer|min:1',
        ];

        $message = [
            'id.required' => '审核单ID必传',
            'id.integer' => '审核单格式错误',
            'id.min' => '审核单ID无效',
        ];
        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);
            $result = $this->historyApproveService->getApproveDetail($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 创建补录申请单
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function additionalAddOrUpdate()
    {
        $field = [
            'id' => 0,
            'card_no' => null, // 16位卡号
            'card_account_type' => null, //司机扣款账户类型 1现金账户
            'driver_phone' => '', // 司机手机号
            'truck_no' => '', // 车牌号
            'driver_name' => '', // 司机姓名
            'station_id' => '', // 油站ID
            'oil_type' => '', // 油品类型
            'oil_name' => '', // 油品名称
            'oil_level' => '', // 油品等级
            'tank_id' => '', // 油罐ID
            'gun_id' => '', // 油枪ID
            'oil_time' => '', // 实际加油时间
            'oil_unit' => null, // 加油方式 1按金额 2按升
            'pay_money' => null, // 司机应付金额,
            'pay_price' => null, // 司机应付单价
            'oil_num' => null, // 加油数量
            'remark' => '', // 备注
            'ticket_image' => '', // 加油凭证
            'truck_image' => '', // 加油车辆
            'other_image' => '', // 其他信息
            'source' => 0, // 来源
            'force' => 0, // 在卡余额不足时是否创建补录申请单
        ];

        $rule = [
            'id'                => 'integer',
            'card_no'           => 'required|numeric',
            'card_account_type' => 'required|in:1',
            'driver_phone'      => 'required|phone_no',
            'truck_no'          => 'string|max:20',
            'driver_name'       => 'string|max:10',
            'station_id'        => 'required|string',
            'oil_type'          => 'string',
            'oil_name'          => 'required|string',
            'oil_level'         => 'string',
            'tank_id'           => 'required|string',
            'gun_id'            => 'required|string',
            'oil_time'          => 'required|date_format:Y-m-d H:i:s',
            'oil_unit'          => 'required|integer|in:1,2',
            'pay_money'         => 'required|money',
            'pay_price'         => 'required|money',
            'oil_num'           => 'required|money',
            'remark'            => 'required|string|max:200',
            'ticket_image'      => 'string',
            'truck_image'       => 'string',
            'other_image'       => 'string',
            'source'            => 'required|integer|in:1',
            'force'             => 'required|in:0,1',
        ];

        $message = [
            'card_account_type.in'  => '无效的扣款账户 1现金账户',
            'truck_no.max'          => '车牌号长度不得超过20字符',
            'remark.required'       => '申请原因不能为空',
            'remark.max'            => '备注不得超过200个字符',
            'oil_unit.in'           => '无效的加油类型 1指定金额加油 2指定升数加油',
            'driver_phone.phone_no' => '手机号格式错误',
            'source.in'             => '补录来源 1GMS',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyApproveService->additionalAddOrUpdate($params);
            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 创建退款申请单
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refundAdd()
    {
        $field = [
            'history_id' => '',
            'remark' => '',
            'ticket_image' => '', // 加油凭证
            'truck_image' => '', // 加油车辆
            'other_image' => '', // 其他信息
            'force' => 0,
            'approve_pass' => 0,
            'refund_system' => 1,
        ];

        $rule = [
            'history_id' => 'required|string|max:32',
            'remark' => 'string|max:256',
            'ticket_image' => 'string|max:200',
            'truck_image' => 'string|max:200',
            'other_image' => 'string|max:200',
            'force' => 'required|integer|in:0,1',
            'approve_pass' => 'required|integer|in:0,1'
        ];

        $message = [
            'remark.max' => '退款详细原因不能超过256个字符'
        ];
        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyApproveService->refundAdd($params);
            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * G7能源账户审核单审核通过
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvePass()
    {
        $field = [
            'id'              => 0,
            'approval_reason' => '',
        ];

        $rule = [
            'id'              => 'required|integer',
            'approval_reason' => 'string|max:200',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule);
            $params['operator'] = Request::get('user_name', '');
            $params['operatorId'] = Request::get('uid', '');
            $result = $this->historyApproveService->approvePass($params);
            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * G7能源账户审核单驳回
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveRefuse()
    {
        $field = [
            'id'              => 0,
            'approval_reason' => '',
        ];

        $rule = [
            'id'              => 'required|integer',
            'approval_reason' => 'string|max:200',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule);
            $result = $this->historyApproveService->approveRefuse($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }
    /**
     * 列表导出
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportApprovePaginate()
    {
        $field = [
            'history_type'      => '', // 异常订单类型
            'approve_status'    => '', // 审核单状态
            'station_id'        => '', // 站点ID
            'card_no'           => '', // 账号 原卡号
            'truck_no'          => '', // 车牌号
            'driver_phone'      => '', //司机手机号
            'ge_createtime' => '', // 创建开始时间
            'le_createtime' => '', // 创建结束时间
            'order_sale_type' => '', //订单类型
            'order_id'          => '',
            'creator'           => '', //创建人
        ];
        $params = $this->getParamAll($field);
        try {
            $this->historyApproveService->exportApprovePaginate($params);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }
}