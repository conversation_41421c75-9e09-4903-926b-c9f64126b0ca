<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;
/**
 * App\Models\StationExt
 *
 * @property int $id
 * @property string|null $station_id 油站id
 * @property int|null $station_classify 返利性质 1:普通自营站点，2:九鼎指定站点, 3:一客一站
 * @property int|null $station_owner_id 油站负责人id
 * @property string|null $dockor_name 结算对接人
 * @property int|null $is_has_policy 是否维护返利政策 10：未维护,20:部分维护,30：已维护
 * @property int|null $is_has_trade 是否已产生消费 1:有,0:无
 * @property int $supplier_id 油站供应商ID
 * @property int $service_area_id 油站所属服务区ID
 * @property string $master_card_no 主卡号
 * @property string $supplementary_card_no 副卡号
 * @property string $card_user_id 卡分配用户ID
 * @property string|null $createtime 创建时间
 * @property string|null $updatetime 更新时间
 */
class StationExtModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'foss_station_ext';

    protected $guarded = ["id"];

    protected $fillable = [];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 获取一个站点的扩展信息
     *
     * @param array $whereParams
     * @param array $select
     * @return mixed|StationExtModel
     */
    public function getOneStationExtByParams(array $whereParams, array $select = ['*'])
    {
        return BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();
    }
}