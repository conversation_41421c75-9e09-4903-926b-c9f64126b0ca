<?php


namespace App\Library\Export;


use Illuminate\Support\Facades\Storage;

class ExportCsv
{
    public function __construct()
    {
    }

    /**
     * 输出excel文件头
     *
     * @param $fileName
     */
    public function getCsvHeader($fileName)
    {
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header('Content-Description: File Transfer');
        header('Content-Encoding: GBK');
        header("Content-Type: application/vnd.ms-excel; charset=GBK");
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header("Content-Disposition: attachment;filename={$fileName}");
        header("Content-Transfer-Encoding: binary ");
        /*
         * windows office 导出的excel bom头,记得添加 "\xEF\xBB\xBF",
         */
    }
}
