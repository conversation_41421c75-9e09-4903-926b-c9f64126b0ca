<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use Illuminate\Support\Collection;

class OrgStationModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_org_station_list';

    protected $guarded = [];

    public function getData(array $params, array $select = ['*']): Collection
    {
        return self::scopeWithCondition(self::select($select), $params)->get();
    }
}