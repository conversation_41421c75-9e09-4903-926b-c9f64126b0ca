<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Http\Controllers;
use App\Http\Defines\CommonDefine;
use App\Library\Helper\Common;
use App\Library\Request;
use App\Services\OrderService;
use App\Servitization\Foss;

class OrderController extends Controller
{
    protected $params = null;
    public function __construct(Request $request)
    {
        $postParams = $request->all();

        Common::log('error',"PDA下单参数：".var_export($postParams,true));
        //$this->params = $this->formatParams($postParams);
    }

    //PDA下单接口
    public function createForPAD()
    {
        $this->params['data_from'] = 1;
        (new OrderService())->addOrder($this->params);
        echo  'PDA';exit;
    }

    //自助付款下单接口
    public function createForSelf(Request $request)
    {
        $this->params['data_from'] = 2;
        echo 'self';
    }

    //三方下单接口
    public function createForOA(Request $request)
    {
        //没有枪号,只有油品,需要根据油品,查到枪号
        $this->params['data_from'] = 3;
       echo 'OA';
    }

    public function getOperatorFromDb()
    {
        $payMentPcode = CommonDefine::getPayMentPcode();
        echo json_encode(['code'=>0,"data"=>$payMentPcode]);
        exit;
    }

    //小程序下单接口
    public function createForMiniProgram()
    {
        $this->params['data_from'] = 4;
        (new OrderService())->addOrder($this->params);
        echo  '小程序';exit;
    }

    //获取运营商
    public function testCurlPcode()
    {
        $list = (new Foss())->getPcodeConf(['is_second_check'=>1,'status'=>1,"is_show"=>1]);
        return $this->success($list);
    }

    public function formatParams($postParams)
    {
        $rule = [
            'sn' => 'required|string|min:3',
            'station_id' => 'required|string|min:5',
            'gun_id' => 'required|string|min:5',
            'money' => 'required|numeric',
            'card_no' => 'required|string|min:5',
        ];
        $messages = [
            'pay_money.numeric' => '结算金额必须是数字',
        ];
        $this->validator($postParams,$rule,$messages);
        //todo 请求油站服务,验证油站是否可用及返回油站信息
        //todo 请求油站服务,验证油枪及返回油枪信息
        $stationInfo = [
            'station_id' => 'd2af73376bedc4d49f27dd6e567a164d', //油站id
            'pcode' => '2000G4ND', //服务商编码
            //油枪信息
            'gunInfo' => [
                'gun_id' => 'eeb2ef83c494837382420c119a9353f5', //油枪id
                'oil_name' => '', //油品名称 id
                'oil_type' => '', //油品标号 id
                'oil_level' => '',//油品级别 id
                'tank_id' => '', //油罐id
                'pay_money' =>'4.42',//结算价
                'pay_price' => '4.42',//销售价
                'list_price' => '',//挂牌价
                'price_id' => '',//价格 id
                'xpcode_price_id' => '', //非自营价格ID
            ],
            'provice_code' => '', //油站所属省份code
            'city_code' => '', //油站所属市 code
        ];
        //todo 请求油卡服务,验证油卡及返回卡信息
        $cardInfo = [
            'card_no' => '6111094358698275', //账号 原卡号
            'truck_no' => '汇W01628', //车牌号
            'drivername' => '草原小狼', //司机姓名
            'driverphone' => '13663249973', //司机手机号
            'orgcode' => '200133', //G7能源账户所属机构
            'orgname' => '', //G7能源账户所属机构的机构名称
            'card_id' => '', //G7能源账户id
            'xpcode' => '', //G7能源账户运营商
            'card_remain' => '', //G7能源账户的扣款账户余额
            'every_fuelnum' => '',//单次加油限制

        ];
        //todo 组装返回的数据与参数
        $newParams = array_merge($postParams,$stationInfo,$stationInfo['gunInfo'],$cardInfo);
        $newParams['oiltime'] = isset($postParams['oiltime']) && $postParams['oiltime'] ? $postParams['oiltime'] : Common::nowTime();
        $newParams['creator'] = isset($postParams['creator']) && $postParams['creator'] ? $postParams['creator'] : null;
        $newParams['truename'] = $newParams['creator'];
        $newParams['price'] = $newParams['pay_price'];
        $user_money = $postParams['money'];
        $newParams['third_payment_string'] = json_encode($postParams);
        $newParams['id'] = Common::getOnlyId();
        $newParams['order_id'] = $postParams['sn'];
        unset($postParams['sn']);
        //todo 计算加油升数,是否需要包括服务费
        $newParams['oil_num'] = $user_money / $newParams['pay_money'];
        unset($newParams['gunInfo']);
        return $newParams;
    }

}