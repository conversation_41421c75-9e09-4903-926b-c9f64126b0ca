<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OrdersModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_history_pay';

    protected $guarded = ["id"];

    public $incrementing = false;

    protected $fillable = ['id','pcode','xpcode','card_no','driverphone','drivername','truck_no','station_id','pay_status','orgcode','oil_num','price',
        'money','pay_price','pay_money','xpcode_pay_money','oil_type','oil_name','oil_level','tank_id','gun_id','oil_time','order_id','oil_starttime',
        'history_id','imgData','gpsno','truename','creator','updatetime','createtime','log_type','third_payment_string','history_params','is_post_history','imgurl'];

    public function getFillAble()
    {
        return $this->fillable;
    }

}