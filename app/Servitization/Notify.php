<?php


namespace App\Servitization;


use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Illuminate\Support\Facades\Log;

class Notify
{
    protected $notify;

    public function __construct()
    {
        $this->notify = config('notify_service');
    }

    /**
     * 推送websocket
     *
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    public function pushMessage(array $params)
    {
        /*
         * {
         *     "group" : "pdah5",
         *     "msg"   : "",
         *     "token" : "用户token"
         * }
         */
        return $this->_postRequest('/api/notify/msgSend', $params);
    }

    /**
     * POST请求
     *
     * @param $path
     * @param array $params
     * @param array $headers
     * @param bool $addKafkaLog
     * @return mixed
     * @throws \Exception
     */
    protected function _postRequest($path, $params = [], $headers = [], $addKafkaLog = false)
    {
        $url = $this->notify['url'] . $path;
        try {
            $startTime = microtime(true)*1000;
            $result = Common::requestJson($url, $params, $headers);
            $cost = microtime(true)*1000 - $startTime;

            $result = json_decode($result, true);
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($path, 'service_notify', $params, $result, $cost);
            }
        } catch (\Exception $e) {
            KafkaLog::thirdOperationLog($path, 'service_notify', $params, $e->getMessage());
            Log::error('请求FOSS_STATION接口返回异常:' . $e->getMessage(), $params);
            throw new \Exception($e->getMessage(), $e->getCode());
        }

        return $result['data'] ?? $result;
    }
}