<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class HistoryPayModel extends BaseModel
{
    /**
     * 连接名
     */
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_history_pay';

    protected $guarded = ["id"];

    /*
     * 非自增主键
     */
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * 根据指定条件批量获取订单信息
     *
     * @param $params
     * @param string $select
     * @param bool $toArray
     * @return array
     */
    public function getHistoryPayInfoByParamsBatch($params, $select = '*', $toArray = false)
    {
        $query = self::select($select);
        $query = self::scopeWithCondition($query, $params, true);
        $result = $query->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 根据指定条件获取1条订单信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getHistoryPayInfoByParams($whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}