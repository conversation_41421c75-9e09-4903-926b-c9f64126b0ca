<?php

namespace App\Listeners;

use App\Events\RefundNotice;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Library\Monitor\Falcon;

class RefundNoticeToAdmin implements ShouldQueue
{
    public $queue = 'message-queue';

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param RefundNotice $event
     * @return void
     */
    public function handle(RefundNotice $event)
    {
        $approveId = $event->approveId;
        $approveInfo = app(HistoryApproveRepository::class)->getOneApproveInfoByParams(['id' => $approveId]);
        if (!empty($approveInfo) && !empty($orderStatusName = $this->getOrderStatus($approveInfo['approve_status']))) {
            $approveMirror = json_decode($approveInfo['approve_mirror'], true);

            $text = $orderStatusName."\n"."\n";
            $text .= '原因:'.$approveInfo['remark']."\n";
            if (!empty($approveInfo['order_id'])) {
                $orderInfo = app(NewOrderRepository::class)->getOneOrderByParams(['order_id' => $approveInfo['order_id']]);
                $text .= '订单号:'.$approveInfo['order_id']."\n";
                $text .= '加油时间:'.$orderInfo['pay_time']."\n";
            } else {
                $historyInfo = app(HistoryRepository::class)->getOneHistoryByParams(['id' => $approveInfo['original_history_id']]);
                $text .= '交易流水号:'.$approveInfo['original_history_id']."\n";
                $text .= '加油时间:'.$historyInfo['oil_time']."\n";
            }
            $text .= '站点:'.$approveMirror['station_name']."\n";
            $text .= '油品:'.$approveMirror['goods']."\n";
            $text .= '站点应收单价:'.sprintf('%.2f', $approveMirror['price'])."\n";
            $text .= '客户应付单价:'.sprintf('%.2f', $approveMirror['pay_price'])."\n";
            $text .= '数量:'.sprintf('%.2f', $approveMirror['oil_num'])."\n";
            $text .= '站点应收金额:'.sprintf('%.2f', $approveMirror['money'])."\n";
            $text .= '客户应付金额:'.sprintf('%.2f', $approveMirror['pay_money'])."\n";
            $text .= '账号:'.$approveInfo['card_no']."\n";
            $text .= '车牌号:'.$approveInfo['truck_no']."\n";

            Falcon::feishu(config('feishu')['trade_refund'], $text);
        }

        return;
    }

    /**
     * 获取退款状态中文名
     *
     * @param $approveStatus
     * @return string
     */
    protected function getOrderStatus($approveStatus)
    {
        $statusName = '';
        switch ($approveStatus) {
            case 1:
                $statusName = '发起退款申请';
                break;
            case 3:
            case 4:
                $statusName = '已退款';
                break;
            case 5:
                $statusName = '已驳回';
                break;
        }

        return $statusName;
    }
}
