<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/20
 * Time: 19:54
 */

return [
    'assign_total' => '总金额',
    'assign_num' => '数量',
    'details.*.vice_no' => '账号', // 原卡号
    'details.*.assign_amount' => '金额',
    'details.*.remark_work' => '备注',
    'account_no' => '账号',
    'sn' => '流水号',
    'oil_com' => '账户类型',// 原油卡类型
    'fanli_orgcode' => '返利机构编码',
    'details.*.unique_id' => '唯一id',
    'details.*.truck_no' => '车牌号',
    'details.*.driver_name' => '司机姓名',
    'details.*.driver_tel' => '司机电话',
    'details.*.vice_password' => '卡密',
    'details.*.paylimit' => '支付限制',
    'details.*.oil_top' => '加油上限',
    'details.*.day_top' => '日限额',
    'details.*.month_top' => '月限额',
    'details.*.remark' => '备注',
    'card_remain' => '账户余额', // 原卡余额
    'orgcode' => '机构编码',
    'page'    => '页码',
    'limit'    => '分页大小',
    'per_page' => '分页大小',
    'oil_name' => '油品名称',
    'pageNo' => '页码',
    'pageSize' => '分页大小',
    'vice_no' => '账号', // 原卡号
    'unique_id' => '唯一ID',
    'card_no' => '账号', // 原卡号
    'bind_status' => '车卡绑定状态',
    'card_status' => '账户状态', // 原卡状态
    'card_level' => '账户类型', // 原卡片类型
    'day_top' => '日限额',
    'oil_top' => '次限额',
    'month_top' => '月限额',
    'truck_no' => '车牌号',
    'driver_tel' => '司机手机号',
    'driver_name' => '司机姓名',
    'paylimit' => '支付限制',
    'vice_password' => '账户密码', // 原卡密
    'details.*.ischeck' => '是否验密',
    'ischeck' => '验密'

];