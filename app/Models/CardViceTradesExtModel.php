<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class CardViceTradesExtModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_card_vice_trades_ext';

    protected $guarded = ["id"];

    protected $fillable = ['id','trades_id','data_from'];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

}