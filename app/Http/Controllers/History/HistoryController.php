<?php

namespace App\Http\Controllers\History;

use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Repositories\DictRepository;
use App\Services\CommonService;
use App\Services\HistoryService;
use Illuminate\Http\JsonResponse;
use Throwable;

class HistoryController extends Controller
{
    protected $historyService;

    public function __construct
    (
        HistoryService $historyService
    )
    {
        $this->historyService = $historyService;
    }

    /**
     * 查询交易流水
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistoryPaginate()
    {
        $rule = [
            'business_log_id' => 'nullable',              // ID
            'serial_num' => 'nullable|alpha_num',         // G7流水ID
            'stream_no' => 'nullable',                    // 三方订单号
            'need_relevant_business_log' => 'nullable',   // 查看关联流水 0不需要 1需要[暂时不加]
            'card_no' => 'nullable',                      // 账号 原16位卡号
            'ge_create_time' => 'nullable',               // 流水创建时间(开始)
            'le_create_time' => 'nullable',               // 流水创建时间(截止)
            'ge_oil_time' => 'nullable',                  // 订单支付时间(开始)
            'le_oil_time' => 'nullable',                  // 订单支付时间(截止)
            'station_id' => 'nullable',                   // 站点ID,逗号隔开
            'station_code' => 'nullable',                 // 站点编码
            'log_type' => 'nullable',                     // 单据类型
            'driver_name' => 'nullable',                  // 司机姓名
            'driver_phone' => 'nullable',                 // 手机号
            'truck_no' => 'nullable',                     // 车牌号
            'org_code' => 'nullable',                     //司机机构
            'need_relevant_org_order' => 'nullable|in:0,1', // 查看机构子订单 0否 1是
            'province_code' => 'nullable',                // 所属省
            'city_code' => 'nullable',                    // 所属市
            'data_type' => 'nullable',                    // 订单来源
            'pcode' => 'nullable',                        // 站点供应商
            'oil_type' => 'nullable|alpha_num',           // 油品类型
            'oil_name' => 'nullable|alpha_num',           // 油品名称
            'oil_level' => 'nullable|alpha_num',          // 油品级别
            'channel_id' => 'nullable|integer',           // 采购渠道ID
            'limit' => 'nullable|numeric|between:1,100',
            'page' => 'nullable|numeric',
            'type' => 'nullable',                         //平台还是对外
        ];

        $params = $this->trimNull(array_keys($rule));

        try {
            //lp-begin增加用户绑定组织下的站点/运营商的判断
            $params=(new CommonService())->getStationOrSupplierOfAdminUserRule($params);
            //end
            $this->validator($params, $rule);

            $maxDay = $this->historyService->getMaxDay();
            $operatorUid = Request::get('uid');
            //G7WALLET-6827【GMS】合作伙伴道科查看站点交易流水的时间范围增加到近6个月
            if(in_array($operatorUid, ['469', '68'])) {
                $maxDay = 180;
            }
            if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
                // 不能超过180天
                if(strtotime(date('Y-m-d 00:00:00',time())) - strtotime($params['ge_create_time']) > $maxDay*86400){
                    throw new \Exception('无法查询出该时间段的结果!');
                }
            }

            if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
                if (strtotime(date('Y-m-d 00:00:00', time())) - strtotime($params['ge_oil_time']) > $maxDay * 86400) {
                    throw new \Exception('无法查询出该时间段的结果!');
                }
            }

            $result = $this->historyService->getHistoryPaginate($params);

            //lp-begin对输出的数据做权限过滤校验
            $result=$this->historyService->filterHistoryList($params,$result);
            //end
            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 手机管站交易流水列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMtHistoryPaginate()
    {
        $field = [
            'station_id'     => '',
            'history_status' => '',
            'history_id'     => '',
            'ge_oil_time'    => date('Y-m-d', (time() - 30 * 86400)),
            'le_oil_time'    => date('Y-m-d', time()),
            'page'           => 1,
            'limit'          => 10
        ];

        $rule = [
            'station_id' => 'string',
            'history_status' => 'in:1,2,3',
            'history_id' => 'string',
            'ge_oil_time' => 'date_format:Y-m-d',
            'le_oil_time' => 'date_format:Y-m-d',
            'page' => 'required|integer',
            'limit' => 'required|integer'
        ];

        $message = [
            'station_id.string' => '站点ID格式错误',
            'history_status.in' => '订单状态错误,参考1扣费成功2退款中3已退款',
            'history_id.string' => '订单号格式错误',
            'ge_oil_time.date_format' => '加油开始时间参数格式错误',
            'le_oil_time.date_format' => '加油截止时间参数格式错误',
            'le_oil_time.after' => '截止时间不得早于开始时间',
        ];

        $params = $this->getParamAll($field);
        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyService->getMtHistoryPaginate($params);
            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 获取加油小票
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicket()
    {
        $field = [
            'history_id' => ''
        ];
        $rule = [
            'history_id' => 'required|string'
        ];
        $message = [
            'history_id.numeric' => '订单号格式错误'
        ];
        $params = $this->getParamAll($field);
        try {
            $this->validator($params, $rule, $message);
            $result = $this->historyService->getTicket($params);
            return $this->success($result, true);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    public function getOperateHistoryReason(): JsonResponse
    {
        try {

            $data = app(DictRepository::class)->getOperateOrderReason();
        } catch (Throwable $throwable) {

            return $this->fail($throwable->getCode() == 0 ? 500 : $throwable->getCode(),
                $throwable->getMessage());
        }
        return $this->success($data);
    }
}
