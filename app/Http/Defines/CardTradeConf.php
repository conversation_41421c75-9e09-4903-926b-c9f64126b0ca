<?php
/**
 *  CardTradeConf.php
 * $Author: 刘培俊 (l<PERSON><PERSON><PERSON><PERSON>@g7.com.cn) $
 * $Date: 2020/9/2 16:24 $
 * CreateBy Phpstorm
 */

namespace App\Http\Defines;

use App\Models\HistoryModel;

class CardTradeConf
{
    /**
     * 交易来源
     */
    const WECHAT_PAY_CODE = 101;
    const WECHAT_ON_LINE = 102;
    const WECHAT_TICKET = 103;
    const WMP_PAY_CODE = 201;
    const WMP_ON_LINE = 202;
    const WMP_TICKET = 203;
    const H5_PAY_CODE = 301;
    const H5_ON_LINE = 302;
    const H5_TICKET = 303;
    const EXCEPTION_MODIFICATION = 501;
    const AFTERWARDS_ADD = 503;
    const CROSS_PLATFORM = 601;
    const THIRD_DOWN_PAY_CODE = 701;
    const OPEN_API = 801;
    const CASHIER_ENTER = 901;
    const ELECTRICITY_ORDER  = 902;

    static public $trade_from = [
        self::WECHAT_PAY_CODE        => 'WeChat-付款码',
        self::WECHAT_ON_LINE         => 'WeChat-主动付款',
        self::WECHAT_TICKET          => 'WeChat-小票机',
        self::WMP_PAY_CODE           => 'WMP-付款码',
        self::WMP_ON_LINE            => 'WMP-主动付款',
        self::WMP_TICKET             => 'WMP-小票机',
        self::H5_PAY_CODE            => 'H5-付款码',
        self::H5_ON_LINE             => 'H5-主动付款',
        self::H5_TICKET              => 'H5-小票机',
        self::EXCEPTION_MODIFICATION => '异常修改',
        self::AFTERWARDS_ADD         => '补录',
        self::CROSS_PLATFORM         => '跨平台',
        self::THIRD_DOWN_PAY_CODE    => 'PDA扫下游付款码',
        self::OPEN_API               => '开放平台',
        self::CASHIER_ENTER          => '合作油站',
        self::ELECTRICITY_ORDER      => '充电订单',
    ];

    /**
     * 付款码模式(包含扫下游码)
     * @var array
     */
    public static $pay_code_channel = [
        self::WECHAT_PAY_CODE,
        self::WMP_PAY_CODE,
        self::H5_PAY_CODE,
        self::THIRD_DOWN_PAY_CODE
    ];

    /**
     * 在线付款模式（包括小票机）
     * @var array
     */
    public static $on_line_pay_channel = [
        self::WECHAT_ON_LINE,
        self::WECHAT_TICKET,
        self::WMP_ON_LINE,
        self::WMP_TICKET,
        self::H5_ON_LINE,
        self::H5_TICKET,
    ];

    public static $order_channel_h5 = [
        self::H5_ON_LINE,
        self::H5_TICKET,
        self::H5_PAY_CODE
    ];


    static function getFrom($id)
    {
        return isset(self::$trade_from[$id]) && self::$trade_from[$id] ? self::$trade_from[$id] : "";
    }

    /**
     * 常规支付channel
     * @var array
     */
    public static $normal_pay_channel = [
        self::WMP_PAY_CODE,
        self::WMP_ON_LINE,
        self::WMP_TICKET,
        self::H5_ON_LINE,
        self::H5_PAY_CODE,
        self::H5_TICKET,
        self::OPEN_API,
    ];

    /**
     * 卡包支付channel
     * @var array
     */
    public static $card_box_pay_channel = [
        self::WECHAT_ON_LINE,
        self::WECHAT_TICKET,
        self::WECHAT_PAY_CODE
    ];

    /**
     * OA支付channel
     * @var array
     */
    public static $oa_pay_channel = [
        self::CROSS_PLATFORM
    ];

    /**
     * 交易模式  注意：配置值不要超过255 tinyint(3)
     *    ZYZ|SYZ         ZYS|XYS             ZDP|SMP|BL
     * 站(自营|上游) + 司机(自营|下游) + 付款方式(主动付款|扫码|补录)
     */
    const TRADE_MODE_ZYZ_ZYS_ZDP = 1; // 自营站 + 自营司机 + 主动付款
    const TRADE_MODE_ZYZ_ZYS_SMP = 5; // 自营站 + 自营司机 + 扫码付款
    const TRADE_MODE_ZYZ_XYS_ZDP = 10; // 自营站 + 下游司机 + 主动付款
    const TRADE_MODE_ZYZ_XYS_ZDP_HN = 12; // 自营站 + 下游司机 + 主动付款(黄牛票需)
    const TRADE_MODE_ZYZ_XYS_SMP = 15; // 自营站 + 下游司机 + 扫码付款
    const TRADE_MODE_SYZ_ZYS_ZDP = 20; // 上游站 + 自营司机 + 主动付款（车主帮模式）
    const TRADE_MODE_SYZ_ZYS_ZDP_YJF = 22; // 上游站 + 自营司机 + 主动付款（一键付模式）
    const TRADE_MODE_SYZ_ZYS_ZDP_KL = 23; // 上游站 + 自营司机 + 主动付款（昆仑润滑油模式）
    const TRADE_MODE_SYZ_ZYS_SMP = 25; // 上游站 + 自营司机 + 扫码付款
    const TRADE_MODE_SYZ_XYS_ZDP = 30; // 上游站 + 下游司机 + 主动付款（车主帮模式）
    const TRADE_MODE_SYZ_XYS_ZDP_YJF = 32; // 上游站 + 下游司机 + 主动付款（一键付模式）
    const TRADE_MODE_SYZ_XYS_SMP = 40; // 上游站 + 下游司机 + 扫码付款
    const TRADE_MODE_SYZ_ZYS_BL = 50; // 上游站 + 自营司机 + 补录（下单流程类似上游PDA扫码加油，上游发起）
//    const TRADE_MODE_SYZ_XYS_BL = 55; // 上游站 + 下游司机 + 补录（暂不允许）
    const TRADE_MODE_ZYZ_ZYS_BL = 60; // 自营站 + 自营司机 + 补录（Gms后台补录）
    const TRADE_MODE_ZYZ_XYS_BL = 65; // 自营站 + 下游司机 + 补录（Gms客户后台下单）
    const TRADE_MODE_ZYZ_ZYS_ENTER = 70; // 自营站 + 自营司机 + 人工录入（加油员自主录入）
    public static $trade_mode = [
        self::TRADE_MODE_ZYZ_ZYS_ZDP     => '自营站|自营司机|主动付款',
        self::TRADE_MODE_ZYZ_ZYS_SMP     => '自营站|自营司机|扫码付款',
        self::TRADE_MODE_ZYZ_XYS_ZDP     => '自营站|下游司机|主动付款',
        self::TRADE_MODE_ZYZ_XYS_ZDP_HN  => '自营站|下游司机|主动付款(票需业务)',
        self::TRADE_MODE_ZYZ_XYS_SMP     => '自营站|下游司机|扫码付款',
        self::TRADE_MODE_SYZ_ZYS_ZDP     => '上游站|自营司机|主动付款(车主帮模式)',
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF => '上游站|自营司机|主动付款(一键付模式)',
        self::TRADE_MODE_SYZ_ZYS_ZDP_KL  => '上游站|自营司机|主动付款(昆仑润滑油模式)',
        self::TRADE_MODE_SYZ_ZYS_SMP     => '上游站|自营司机|扫码付款',
        self::TRADE_MODE_SYZ_XYS_ZDP     => '上游站|下游司机|主动付款(车主帮模式)',
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF => '上游站|下游司机|主动付款(一键付模式)',
        self::TRADE_MODE_SYZ_XYS_SMP     => '上游站|下游司机|扫码付款',
        self::TRADE_MODE_SYZ_ZYS_BL      => '上游站|自营司机|补录',
        self::TRADE_MODE_ZYZ_ZYS_BL      => '自营站|自营司机|补录',
        self::TRADE_MODE_ZYZ_XYS_BL      => '自营站|下游司机|补录',
        self::TRADE_MODE_ZYZ_ZYS_ENTER   => '自营站|自营司机|人工录入',
    ];

    //推送订单id到oa
    public static $callBack2OA = [
        self::TRADE_MODE_SYZ_ZYS_SMP,
        self::TRADE_MODE_SYZ_XYS_SMP,
        self::TRADE_MODE_SYZ_ZYS_ZDP,
        self::TRADE_MODE_SYZ_XYS_ZDP,
    ];

    //一键付推送OA
    public static $push2OA = [
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF,
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF
    ];


    public static $ownner_station_pcode = [
        '2000Z2GV', '2000VSVC', '20008QON', '20008QONTN2E', '20008QONNMXC', '20008QONF3UV', '20008QON5WKM', '20008QON7TE1',
        '20008QON3AE9', '20008QON4A06', '20004S4M', '200023V3', '2000BJ43', '2000V4A5', '2000SUAL','20008QOND2L3AD','20008QONKSKCC4',
        "20008QON2SKCUT","20008QON5CUJ", "20008QONA3L5KV", "2000BTD24B", "20003UKU2C",
    ];

    public static $qrCodeSourceFlagToEnumMapping = [
        'wmp' => self::WMP_PAY_CODE,
        'h5'  => self::H5_PAY_CODE,
        'cp'  => self::WECHAT_PAY_CODE,
        'oa'  => self::OPEN_API,
    ];

    /**
     * 各交易模式职责
     *
     * 'make_order' => [
     * 'verify_card'    => 1, // 验卡
     * 'direct_pay'     => 0, // 直接支付
     * 'push_order2oa'  => 0, // 给OA推单
     * 'ws_msg_type'    => 1, // 需要结合端判断 1:给pda发消息，订单维持 2:给小程序、H5或卡包发验密消息，密码弹框
     * ],
     * 'pay_order'  => [
     * 'verify_pwd'     => 1, // 验密
     * 'call_back2oa'   => 1, // 回调oa
     * 'ws_msg_type'    => 1, // 需要结合端判断 1:给pda发消息，订单维持 2:给小程序、H5或卡包发验密消息，密码弹框
     * ],
     * @var array
     */
    public static $trade_mode_scope = [
        // 自营站|自营司机|主动付款
        self::TRADE_MODE_ZYZ_ZYS_ZDP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 1, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ],
        ],
        //'自营站|自营司机|扫码付款'
        self::TRADE_MODE_ZYZ_ZYS_SMP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 1, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => ['CHECK_PWD', 'PDA'], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '自营站|下游司机|主动付款'
        self::TRADE_MODE_ZYZ_XYS_ZDP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '自营站|下游司机|主动付款(黄牛)'
        self::TRADE_MODE_ZYZ_XYS_ZDP_HN  => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 1, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => [], // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '自营站|下游司机|扫码付款'
        self::TRADE_MODE_ZYZ_XYS_SMP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => 'PAY_LOG', // 给OA推单
                'ws_type'     => ['PDA'], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|自营司机|主动付款(车主帮模式)'
        self::TRADE_MODE_SYZ_ZYS_ZDP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 1, // 是否需要密码
                'oa_msg_type' => 'ONLINE_PAY_ORDER', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|自营司机|主动付款(一键付模式)'
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 1, // 是否需要密码
                'oa_msg_type' => 'ONLINE_PAY_ORDER', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|自营司机|主动付款(昆仑润滑油模式)'
        self::TRADE_MODE_SYZ_ZYS_ZDP_KL  => [
            'make_order' => [
                'verify_card' => 0, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|自营司机|扫码付款'
        self::TRADE_MODE_SYZ_ZYS_SMP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 1, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => ['CHECK_PWD'], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|下游司机|主动付款(车主帮模式)'
        self::TRADE_MODE_SYZ_XYS_ZDP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => 'ONLINE_PAY_ORDER', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|下游司机|主动付款(一键付模式)'
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => 'ONLINE_PAY_ORDER', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|下游司机|扫码付款'
        self::TRADE_MODE_SYZ_XYS_SMP     => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '上游站|自营司机|补录'
        self::TRADE_MODE_SYZ_ZYS_BL      => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 1, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '自营站|自营司机|补录'
        self::TRADE_MODE_ZYZ_ZYS_BL      => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 1, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
        // '自营站|下游司机|补录'
        self::TRADE_MODE_ZYZ_XYS_BL      => [
            'make_order' => [
                'verify_card' => 1, // 验卡
                'direct_pay'  => 0, // 直接支付
                'need_pwd'    => 0, // 是否需要密码
                'oa_msg_type' => '', // 给OA推单
                'ws_type'     => [], // 长连接类型
            ],
            'pay_order'  => [
                'verify_pwd'   => 1, // 验密
                'call_back2oa' => 1, // 回调oa
                'ws_msg_type'  => 1, // 需要结合端判断
            ]
        ],
    ];

    /**
     * 走oa下单的交易模式
     * @var array
     */
    public static $trade_mode_oa = [
        self::TRADE_MODE_ZYZ_XYS_ZDP,
        self::TRADE_MODE_ZYZ_XYS_ZDP_HN,
        self::TRADE_MODE_SYZ_ZYS_SMP,
        self::TRADE_MODE_SYZ_XYS_SMP,
        self::TRADE_MODE_SYZ_ZYS_BL,
        self::TRADE_MODE_ZYZ_XYS_BL,
        self::TRADE_MODE_SYZ_XYS_ZDP,
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF,
    ];

    /**
     * 下单方式
     */
    const TRUCKFLAG = "KC";
    const G7FLAG = "G7";
    const OPEN_API_FLAG = "OPEN_API";

    /**
     * 需验证站和渠道
     * @var array
     */
    public static $trade_mode_need_compare = [
        self::TRADE_MODE_ZYZ_ZYS_ZDP,
        self::TRADE_MODE_ZYZ_ZYS_SMP,
        self::TRADE_MODE_ZYZ_XYS_SMP,
        self::TRADE_MODE_SYZ_ZYS_ZDP,
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF,
    ];

    public static $trade_mode_bl = [
        self::TRADE_MODE_SYZ_ZYS_BL,
        self::TRADE_MODE_ZYZ_ZYS_BL,
        self::TRADE_MODE_ZYZ_XYS_BL,
    ];

    /**
     * 无需超时关单交易模式
     * @var array
     */
    public static $trade_mode_no_auto_cancel_order = [
        self::TRADE_MODE_ZYZ_XYS_BL
    ];

    /**
     * 强制使用主动付款交易模式下单的订单来源
     * @var string[]
     */
    public static $force_active_payment_order_flag = [
        self::TRUCKFLAG,
        self::OPEN_API_FLAG,
    ];
    /**
     * 下单前需要验证通用销价的订单来源
     * @var string[]
     */
    public static $check_common_sale_price_order_flag = [
        self::TRUCKFLAG,
    ];
    /**
     * 扫码付款的支付模式
     * @var int[]
     */
    public static $trade_mode_smp = [
        self::TRADE_MODE_ZYZ_ZYS_SMP,
        self::TRADE_MODE_ZYZ_XYS_SMP,
        self::TRADE_MODE_SYZ_ZYS_SMP,
        self::TRADE_MODE_SYZ_XYS_SMP,
    ];
    /**
     * 开放平台or卡车一类H5接入客户需要参与二次核销的支付模式
     * @var string[]
     */
    public static $trade_mode_need_write_off = [
        self::TRADE_MODE_ZYZ_ZYS_SMP,
        self::TRADE_MODE_ZYZ_XYS_SMP,
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF,
        self::TRADE_MODE_SYZ_ZYS_SMP,
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF,
        self::TRADE_MODE_SYZ_XYS_SMP,
    ];
    public static $no_need_notify_driver_pay_result = [
        self::OPEN_API_FLAG,
    ];
    
    /**
     * 所有主动付款交易模式
     * @var array
     */
    public static $trade_mode_zdp = [
        self::TRADE_MODE_ZYZ_ZYS_ZDP,
        self::TRADE_MODE_ZYZ_XYS_ZDP,
        self::TRADE_MODE_ZYZ_XYS_ZDP_HN,
        self::TRADE_MODE_SYZ_ZYS_ZDP,
        self::TRADE_MODE_SYZ_ZYS_ZDP_YJF,
        self::TRADE_MODE_SYZ_ZYS_ZDP_KL,
        self::TRADE_MODE_SYZ_XYS_ZDP,
        self::TRADE_MODE_SYZ_XYS_ZDP_YJF,
    ];

    public static $history_type_to_foss_mapping = [
        HistoryModel::COST_SUCCESS                   => 10,
        HistoryModel::COST_ELECTRICITY_ORDER         => 10,
        HistoryModel::COST_SUCCESS_ADD               => 20,
        HistoryModel::COST_ABNORMAL                  => 30,
        HistoryModel::COST_INVALID                   => 40,
        HistoryModel::COST_INVALID_2                 => 50,
        HistoryModel::COST_RESERVATION_REFUEL        => 201,
        HistoryModel::COST_RESERVATION_REFUEL_REFUND => 202,
    ];
}