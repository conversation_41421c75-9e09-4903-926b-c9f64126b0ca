<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationTankModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_tank';

    protected $guarded = ["id"];

    protected $fillable = ['id','oil_name',"oil_type","oil_level","station_id"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function gun()
    {
        return $this->hasMany(StationGunModel::class, 'tank_id', 'id');
    }

    /**
     * 获取1条罐信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneTankByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}