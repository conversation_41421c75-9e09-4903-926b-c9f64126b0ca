<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\StationPayQrcodesModel;

class StationPayQrcodesRepository extends \App\Repositories\EloquentRepository
{
    protected $stationPayQrcodesModel;
    public function __construct(StationPayQrcodesModel $stationPayQrcodesModel)
    {
        $this->stationPayQrcodesModel = $stationPayQrcodesModel;
    }

    //查询
    public function getStationId(array $condition)
    {
        $data = $this->stationPayQrcodesModel->select("id","station_id","qr_link")->withCondition($condition)->first();
        return $data;
    }
}