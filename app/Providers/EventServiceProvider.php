<?php

namespace App\Providers;

use App\Events\OrderPaySuccess;
use App\Events\RefundNotice;
use App\Events\UnifiedOrder;
use App\Listeners\PayOrder;
use App\Listeners\RefundNoticeToAdmin;
use App\Listeners\RefundNoticeToDoper;
use App\Listeners\SendMsgByWeChat;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        OrderPaySuccess::class => [
            SendMsgByWeChat::class
        ],
        RefundNotice::class => [
            RefundNoticeToDoper::class,
            RefundNoticeToAdmin::class
        ],
        UnifiedOrder::class => [
            PayOrder::class,
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
