<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

/**
 * Class CardModel
 * @package App\Models
 * @property $card_level
 * @property $left_seconds
 */
class StationLimitConfigModel extends BaseModel
{
    protected $connection = 'mysql_gos';
    /**
     * 表名
     */
    protected $table = 'gos_station_limit_config';

    protected $guarded = ["id"];

    public $incrementing = false;

    protected $fillable = ['id', 'config_type', 'config_value', 'updater', 'updated_at'];

    public function getFillAble()
    {
        return $this->fillable;
    }
}