<?php


namespace App\Models;


class AdditionalOrderItem extends BaseModel
{
    protected $connection = 'mysql_gas';

    public $table = 'gas_additional_order_item';

    protected $fillable = [
        'id', 'record_id', 'order_id', 'order_status', 'order_msg', 'third_order_id', 'third_order_status',
        'third_order_msg', 'create_time', 'update_time'
    ];

    public $timestamps = false;

    CONST ORDER_STATUS_WAIT = 1;
    CONST ORDER_STATUS_SUCCESS = 2;
    CONST ORDER_STATUS_FAIL = 3;

    public static $ORDER_STATUS = [
        self::ORDER_STATUS_WAIT => '待支付',
        self::ORDER_STATUS_SUCCESS => '已支付',
        self::ORDER_STATUS_FAIL => '支付失败',
    ];

    /**
     * 创建一条补单记录
     *
     * @param $insertArray
     * @return mixed
     */
    public function insertOneItem($insertArray)
    {
        return self::insert($insertArray);
    }

    /**
     * 更新一条补单记录
     *
     * @param $order_id
     * @param $updateArray
     * @return mixed
     */
    public function updateOneItemByOrderId($order_id, $updateArray)
    {
        return self::where(['order_id' => $order_id])->update($updateArray);
    }

    /**
     * 查询一条补单记录
     *
     * @param array $params
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneItemByParams(array $params, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $params)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}