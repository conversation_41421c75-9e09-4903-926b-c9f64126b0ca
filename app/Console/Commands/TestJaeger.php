<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/7
 * Time: 3:18 PM
 */

namespace App\Console\Commands;

use App\Services\ConsumerService;
use App\Services\OrderExtendService;
use G7\Tracing\TraceContext;
use G7\Tracing\TracerBuilder;
use Illuminate\Console\Command;
use App\Library\Helper\Common;

class TestJaeger extends Command
{
    const LOG = 'queue_order_operation';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:jaeger';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'test:jaeger';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 创建Tracer
        TracerBuilder::create()
                     ->setServiceName('foss-order-cron')
                     ->setAgentHostPort('127.0.0.1:6831')
                     ->build();

        // 获取Tracer
        $tracer = TraceContext::instance()->getTracer();
        $tracer->setTag('service.name', 'foss-order-cron');
        // 创建Span
        $rootSpan = $tracer->startSpan('/test/jaeger', microtime(true));
        $rootSpan->finish();
        $tracer->flush();
        return 1;
    }
}