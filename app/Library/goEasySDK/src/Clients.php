<?php


namespace GoEasySDK;


class Clients extends Base
{
	/**
	 * 此功能目前goEasy上未开通
	 * @param $channel
	 */
	public function getOnlineClientsByChannel($channel)
	{
		$apiParams = [
			'channel'      => 'uid1',         //必须项，可以包含一个或多个channel
			'includeUsers' => true,    //可选项，是否返回用户列表，默认false
			'distinct'     => 1,        //可选项，相同userId的客户端，列表中只保留一个，默认false
		];
		
		$result = $this->request('GET', 'herenow', $apiParams);
		
	}
}