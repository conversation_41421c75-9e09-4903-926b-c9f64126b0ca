<?php


namespace App\Models;


use App\Http\Defines\CommonDefine;
use App\Http\Defines\CouponDefine;
use Illuminate\Support\Facades\DB;

class OrderModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_order_new';

    protected $fillable = [
        'order_id',
        'trade_no',
        'trade_mode',
        'order_type',
        'order_channel',
        'order_status',
        'station_id',
        'station_code',
        'province_code',
        'city_code',
        'supplier_code',
        'org_code',
        'card_no',
        'card_type',
        'card_level',
        'driver_name',
        'driver_source',
        'driver_phone',
        'truck_no',
        'oil_type',
        'oil_name',
        'oil_level',
        'oil_unit',
        'oil_num',
        'oil_price',
        'oil_money',
        'service_price',
        'service_money',
        'mirror',
        'creator',
        'updator',
        'history_id',
        'third_order_id',
        'payment_id',
        'order_sn',
        'order_flag',
        'third_actual_fee',
        'third_actual_price',
        'order_sale_type',
    ];


    const ORDER_SALE_TYPE = [
        '10' => '消费订单',
        '20' => '预售订单'
    ];

    public $timestamps = false;

    public function ext()
    {
        return $this->hasOne(OrderExtModel::class, 'order_id', 'order_id');
    }

    /**
     * order_type enum
     */
    const CHARGE_BY_DRIVER = 1;
    const CHARGE_BY_DOPER  = 2;
    const CHARGE_BY_ADMIN  = 3;

    public static $ORDER_TYPE = [
        self::CHARGE_BY_DRIVER => '司机主动付款',
        self::CHARGE_BY_DOPER  => '加油员PDA扫码',
        self::CHARGE_BY_ADMIN  => '管理员补录'
    ];

    /**
     * order_status enum
     */
    const WAIT_PAY    = 1;
    const SUCCESS_PAY = 2;
    const FAIL_PAY    = 3;
    const CANCEL      = 4;
    const REFUNDING   = 5;
    const REFUND      = 6;

    public static $ORDER_STATUS = [
        self::WAIT_PAY    => '待支付',
        self::SUCCESS_PAY => '支付成功',
        self::FAIL_PAY    => '支付失败',
        self::CANCEL      => '订单取消',
        self::REFUNDING   => '退款中',
        self::REFUND      => '已退款',
    ];

    /**
     * card_type enum
     */
    const VALUE_CARD = 1;
    const SHARE_CARD = 2;
    const FACAI_CARD = 3;

    public static $CARD_TYPE = [
        self::VALUE_CARD => '充值账户',
        self::SHARE_CARD => '共享账户',
        self::FACAI_CARD => '发财账户'
    ];

    /**
     * card_level enum
     */
    const PERSONAL_CARD = 1;
    const CAR_CARD      = 2;

    public static $CARD_LEVEL = [
        self::PERSONAL_CARD => 'G7能源普通账户',
        self::CAR_CARD      => 'G7能源车辆账户',
    ];

    /**
     * driver_source enum
     */
    const UN_KNOWN     = 0;
    const OWN_DRIVER   = 1;
    const THIRD_DRIVER = 2;

    public static $DRIVER_SOURCE = [
        self::UN_KNOWN     => '未知',
        self::OWN_DRIVER   => '自营司机',
        self::THIRD_DRIVER => '平台司机',
    ];

    /**
     * oil_unit enum
     */
    const FIXED_MONEY = 1;
    const FIXED_LITRE = 2;

    public static $OIL_UNIT = [
        self::FIXED_MONEY => '定金额加油',
        self::FIXED_LITRE => '定升数加油',
    ];

    /**
     * order_sale_type enum
     */
    const ORDER_SALE_TYPE_REFUEL             = 10;
    const ORDER_SALE_TYPE_RESERVATION_REFUEL = 20;
    public static $ORDER_SALE_TYPE = [
        self::ORDER_SALE_TYPE_REFUEL             => '消费订单',
        self::ORDER_SALE_TYPE_RESERVATION_REFUEL => '预售订单',
    ];

    /**
     * 插入记录
     * @param array $insertArray
     * @return mixed
     */
    public function insertOrder(array $insertArray)
    {
        return self::insert($insertArray);
    }

    /**
     * 更新订单信息
     *
     * @param $orderId
     * @param $updateArray
     * @return mixed
     */
    public function updateOrderByOrderId($orderId, $updateArray)
    {
        return self::from($this->table)->where(['order_id' => $orderId])->update($updateArray);
    }

    /**
     * 获取订单信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $withExt
     * @return array
     */
    public function getOneOrderInfo(array $whereParams, $select = ['*'], $withExt = true)
    {
        $sqlObj = BaseModel::scopeWithCondition(self::select($select), $whereParams);
        if ($withExt) {
            $sqlObj->with('ext');
        }
        #$result = $sqlObj->lockForUpdate()->first();
        $result = $sqlObj->first();

        return !$result ? [] : $result->toArray();
    }

    /**
     * 获取订单信息For Lock
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $withExt
     * @return array
     */
    public function getOneOrderForLock(array $whereParams, $select = ['*'], $withExt = true)
    {
        $sqlObj = BaseModel::scopeWithCondition(self::select($select), $whereParams);

        if ($withExt) {
            $sqlObj->with('ext');
        }
        $result = $sqlObj->lockForUpdate()->first();

        return !$result ? [] : $result->toArray();
    }

    /**
     * 批量获取订单信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $withExt
     * @return array
     */
    public function getBatchOrderInfo(array $whereParams, $select = ['*'], $withExt = true)
    {
        $otherOrderIds = $whereParams['other_order_ids'] ?? [];
        unset($whereParams['other_order_ids']);
        $sqlObj = BaseModel::scopeWithCondition(self::select($select), $whereParams);

        // 这个条件必须放到最后
        if (!empty($otherOrderIds)) {
            $sqlObj->orWhereIn('order_id', $otherOrderIds);
        }
        if ($withExt) {
            $sqlObj->with('ext');
        }
        $result = $sqlObj->get();
        return empty($result) ? [] : $result->toArray();
    }

    /**
     * 分页获取订单信息
     *
     * @param array $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @param array $extParam
     * @return array
     */
    public function getOrderPaginate(array $whereParams = [], $select = ['*'], $page = 1, $limit = 10, $toArray = false)
    {
        $source = array_get($whereParams, 'client_source', '');
        unset($whereParams['client_source']);

        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams);
        // 这里为了支持中物流一类存在直降结算模式的上游运营商按原始应付金额统计数据调整sql语句
        $aggre = BaseModel::scopeWithCondition(
            self::selectRaw('SUM(if(gas_order_new.supplier_code in (\'' . implode("','",
                    CommonDefine::showOriginalSupplierMoney())  .'\'),gas_order_new_extend.original_supplier_money,
                    ROUND(supplier_price * real_oil_num, 2))) AS total_supplier_money'),
            $whereParams,
            false,
            $this->table
        );

        //针对pda上不能看到卡车的待支付订单
        if ($source == '3085766248') {
            $result->whereRaw("if (order_flag = 'G7', 1 = 1, order_status not in (1,4)) ");
        }

        $count = $result->count();
        $totalOilNum = $result->sum('oil_num');
        $totalOilMoney = $result->sum('oil_money');
        $totalServiceMoney = $result->sum('service_money');

        $supplier = $aggre->join('gas_order_new_extend', 'gas_order_new_extend.order_id', '=', 'gas_order_new.order_id')
            ->get()->pop();

        //lp
        $data = $result->orderBy('create_time', 'DESC')->with('ext.coupons')->offset(($page - 1) * $limit)->limit(
            $limit
        )->get()->map(function ($item){
            $item['order_sale_type_name'] = $item->order_sale_type_name ?? '--';
            return $item;
        });

        if ($toArray) {
            $data = empty($data) ? [] : $data->toArray();
        }

        return [
            'count'                => $count,
            'total_oil_num'        => $totalOilNum,
            'total_oil_money'      => $totalOilMoney,
            'total_service_money'  => $totalServiceMoney,
            'total_supplier_money' => $supplier->total_supplier_money,
            'data'                 => $data,
            'page'                 => $page,
            'limit'                => $limit
        ];
    }


    /**
     * 统计订单条数
     *
     * @param array $whereParams
     * @return mixed
     */
    public function getOrderCount(array $whereParams)
    {
        return BaseModel::scopeWithCondition(self::select(['*']), $whereParams)->count();
    }

    /**
     * 分页获取订单导出的信息
     *
     * @param array $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @return array
     */
    public function getOrderExportPaginate(
        array $whereParams = [],
        $select = ['*'],
        $page = 1,
        $limit = 10,
        $toArray = false
    ) {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)
                           ->with('ext')
                           ->offset(($page - 1) * $limit)
                           ->limit($limit)
                           ->get()->map(function ($item){
                                $item['order_sale_type_name'] = $item->order_sale_type_name ?? '--';
                                return $item;
                            });
        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 获取最新一条订单
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getLatestOrderByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->orderBy(
            'create_time',
            'DESC'
        )->first();
        if ($result) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    public function infoWithExt(array $whereParams, $select = ['*'], $toArray = false)
    {
        //print_r($whereParams);exit;
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->with('ext.coupons')->first();
        if ($result) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 删除订单信息
     * @param $orderId
     * @return int|mixed
     */
    public function deleteByOrderId($orderId)
    {
        if (!$orderId) {
            return 0;
        }
        return self::query()->where('order_id', '=', $orderId)->delete();
    }

    public static function getExpiredSoonTripartiteCoupons(array $driverPhones = []): array
    {
        $orderModel = new OrderModel();
        $orderExtModel = new OrderExtModel();
        $couponModel = new CouponsModel();
        $orderWHereParams = [
            'supplier_code' => [
                CouponDefine::getHuBeiZSYPCode(),
            ]
        ];
        if ($driverPhones) {
            $orderWHereParams['driver_phone'] = $driverPhones;
        }
        $model = BaseModel::scopeWithCondition(
            $orderModel->leftJoin(
                $orderExtModel->getTable(),
                $orderExtModel->getTable() . '.order_id',
                '=',
                $orderModel->getTable() . '.order_id'
            )->leftJoin(
                $couponModel->getTable(),
                $couponModel->getTable() . '.voucher',
                '=',
                $orderExtModel->getTable() . '.g7_coupon_flag'
            ),
            $orderWHereParams,
            true,
            $orderModel->getTable()
        )->where(
            $couponModel->getTable() . ".tripartite_status",
            '=',
            CouponDefine::NO_CHARGE_OFF
        )->where(
            $couponModel->getTable() . ".status",
            '=',
            CouponDefine::NO_CHARGE_OFF
        )->whereRaw(
            "unix_timestamp(" . $couponModel->getTable() . ".end_time) - " . time() . " > 0"
        );
        return [
            'total' => $model
                ->whereRaw(
                    $couponModel->getTable() . ".order_id = " . $orderModel->getTable() . ".order_id"
                )->count(),
            'list'  => $model
                ->whereRaw(
                    $couponModel->getTable() . ".order_id = " . $orderModel->getTable() . ".order_id"
                )
                ->orderBy(
                    DB::raw("unix_timestamp(" . $couponModel->getTable() . ".end_time) - " . time()),
                    'asc'
                )
                ->offset(0)
                ->limit(2)
                ->select([
                    $orderModel->getTable() . '.order_id',
                    $orderExtModel->getTable() . '.mac_amount',
                    $orderExtModel->getTable() . '.supplier_name',
                    $orderExtModel->getTable() . '.station_name',
                    $couponModel->getTable() . '.end_time',
                ])->get()
        ];
    }

    public function getOrderSaleTypeNameAttribute()
    {
        if(isset($this->order_sale_type)) {
            return self::ORDER_SALE_TYPE[$this->order_sale_type] ?? '';
        }
    }

    /**
     * 批量获取订单及其扩展信息
     *
     * 该方法用于根据给定的条件参数获取订单信息及其相关的扩展信息它允许选择性地指定订单信息和扩展信息的具体字段
     *
     * @param array $whereParams 查询条件数组，用于指定查询订单的条件
     * @param array $orderSelect 默认为 ['*']，用于指定查询的订单信息字段
     * @param array $extSelect 默认为 ['*']，用于指定查询的扩展信息字段
     *
     * @return \Illuminate\Database\Eloquent\Collection|static[] 返回查询到的订单及其扩展信息的集合
     */
    public function getBatchOrderAndExtInfo(array $whereParams, array $orderSelect = ['*'], array $extSelect = ['*'])
    {
        return BaseModel::scopeWithCondition(self::select($orderSelect), $whereParams)->with([
            'ext' => function ($query) use ($extSelect) {
                $query->select($extSelect);
            }
        ])->get();
    }
}