PHPENV: FOSS-ORDER-TEST
APPLICATION__NAME: foss-order
APPLICATION__DEBUG: true

DB__CONNECTION: mysql
DB__HOST: ************
DB__PORT: 3306
DB__DATABASE: fuel_gateway
DB__USERNAME: cat
DB__PASSWORD: cathyr

APP__NAME: foss-order
APP__ENV: demo
APP__KEY: base64:OCHJi6Gpj+tf72lo73pulyDc+HRDZXrnMRXZkW1E6FI=
APP__DEBUG: true
APP__LOG__LEVEL: debug
APP__URL: ''

BROADCAST__DRIVER: log
CACHE__DRIVER: redis
SESSION__DRIVER: redis
SESSION__LIFETIME: 120
QUEUE__DRIVER: redis
QUEUE_CONNECTION: redis

CAT__AGENT__TYPE: Socket
CAT__AGENT__HOST: host-cat
CAT__AGENT__PORT: 2280

REDIS__HOST: redis
REDIS__PASSWORD: cae0f7fcf1
REDIS__PORT: 6379

RUN__MODE: remote

CRONTAB__ENABLE: 'on'
RUN_SCRIPTS: 1
SUPERVISOR__ENABLE: 'on'