<?php

namespace App\Listeners;

use App\Events\RefundNotice;
use App\Jobs\SendWxTemplateMessage;
use App\Repositories\GasUserWeChatRepository;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\NewOrderRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class RefundNoticeToDoper implements ShouldQueue
{
    public $queue = 'message-queue';
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param RefundNotice $event
     * @return void
     */
    public function handle(RefundNotice $event)
    {
        $approveId = $event->approveId;
        $approveInfo = app(HistoryApproveRepository::class)->getOneApproveInfoByParams(['id' => $approveId]);
        if (!empty($approveInfo) && !empty($approveInfo['order_id'])) {
            // 获取加油员微信open_id
            $orderInfo = app(NewOrderRepository::class)->getOneOrderByParams(['order_id' => $approveInfo['order_id']]);
            if (empty($orderInfo)) {
                return;
            }
            if (!empty($orderInfo['mirror'])) {
                $orderMirror = json_decode($orderInfo['mirror'], true);
            } else {
                $orderMirror = $orderInfo['ext'];
            }
            $operatorId = array_get($orderMirror, 'operator_id', '');
            if (empty($operatorId)) {
                return;
            }

            // 获取openId
            $result = app(GasUserWeChatRepository::class)->getOpenIdsByParams(['gas_user_id' => $operatorId]);
            // 管理员GMS后台支付成功,不发送通知
            if (empty($result)) {
                return;
            }

            $openIdBatch = array_column($result, 'openid');
            if (!empty($openIdBatch)) {
                $msg = [
                    'first'    => [
                        'value' => $this->getNotice($approveInfo['approve_status']),
                        'color' => '#173177'
                    ],
                    'keyword1' => [
                        'value' => $approveInfo['order_id'],
                        'color' => ''
                    ],
                    'keyword2' => [
                        'value' => $orderInfo['creator'],
                        'color' => '',
                    ],
                    'keyword3' => [
                        'value' => $approveInfo['create_time'], // 王新要求改成申请时间
                        'color' => ''
                    ],
                    'keyword4' => [
                        'value' => sprintf('%.2f', round(bcadd($orderInfo['oil_money'], $orderInfo['service_money'], 3), 2)) . '元',
                        'color' => '',
                    ],
                    'keyword5' => [
                        'value' =>
                            '站点名称:' . empty($orderMirror['remark_name']) ? $orderMirror['station_name'] : $orderMirror['remark_name'] . "\n"
                                . '商品:' . $orderMirror['goods'] . "\n"
                                . '司机卡号:' . $orderInfo['card_no'] . "\n"
                                . '退款原因:' . $approveInfo['remark'],
                        'color' => '',
                    ],
                    'keyword6' => [
                        'value' => '如有疑问，请联系G7工作人员',
                        'color' => '',
                    ],
                ];
                foreach ($openIdBatch as $openId) {
                    dispatch((new SendWxTemplateMessage($openId, config('wechat')['gzh_msg_template']['refund'], $msg))->onQueue('send-wx-template-message'));
                }

                return;
            }
        }
    }

    protected function getNotice($approveStatus)
    {
        $statusName = '';
        switch ($approveStatus) {
            case 1:
                $statusName = '您已成功发起一笔退款申请，等待G7工作人员审核中';
                break;
            case 3:
            case 4:
                $statusName .= '您的退款申请已审批完成，资金将很快退还到司机账户中';
                break;
            case 5:
                $statusName .= '您的退款申请被驳回，如有疑问请联系G7工作人员';
                break;
        }

        return $statusName;
    }
}
