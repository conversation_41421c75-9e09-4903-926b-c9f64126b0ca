{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.1.3", "ext-bcmath": "*", "ext-json": "*", "ext-pdo": "*", "ext-rdkafka": "*", "fideloper/proxy": "^4.0", "laravel/framework": "5.7.*", "laravel/tinker": "^1.0", "library/components": "^1.0", "library/monitor": "^1.31", "php-spec/php-tracing": "^0.2.0", "predis/predis": "^1.1", "ext-curl": "*"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.8", "beyondcode/laravel-dump-server": "^1.0", "filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^2.0", "phpunit/phpunit": "^7.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "secure-http": false, "allow-plugins": {"kylekatarnls/update-helper": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Library\\GoEasySDK\\": "app/Library/goEasySDK"}, "classmap": ["database/seeds", "database/factories", "app/Library/goEasySDK"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Library\\GoEasySDK\\": "app/Library/goEasySDK"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.aliyun.com/composer"}, "library/monitor": {"type": "composer", "url": "https://packagelist.chinawayltd.com"}, "satis": {"type": "composer", "url": "http://satis.ews.chinawayltd.com"}}}