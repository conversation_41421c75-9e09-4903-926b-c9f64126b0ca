<?php
/**
 * 河北中石油
 */

namespace App\Repositories\GetCanUseCoupon;

use App\Http\Defines\CouponDefine;
use App\Models\CouponsModel;
use App\Repositories\EloquentRepository;

class SH extends EloquentRepository implements GetCanUseCouponInterface
{
    protected $couponModel;

    public function __construct(CouponsModel $couponModel)
    {
        parent::__construct();
        $this->couponModel = $couponModel;
    }

    public function handle($couponTypeId): ?CouponsModel
    {
        if (empty($couponTypeId)) {
            return null;
        }
        return $this->couponModel->lockForUpdate()
                                 ->where('coupon_type_id', $couponTypeId)
                                 ->where('status', CouponDefine::NO_USE)
                                 ->orderBy('id', 'asc')->limit(1)->first();
    }
}