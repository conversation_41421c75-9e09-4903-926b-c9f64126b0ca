<?php

namespace App\Http\Controllers;

use App\Exceptions\ParamInvalidException;
use Exception;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Request;
use RuntimeException;


class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * 封装数据合法性校验
     * @param $param
     * @param $rule
     * @throws ValidationException
     */
    protected function validator($param, $rule, $message = [])
    {
        $attribute = config('attribute');
        $validator = Validator::make($param, $rule, $message, $attribute);

        if ($validator->fails()) {
            throw new ParamInvalidException($validator->getMessageBag()->first());
        }
    }

    /**
     * success
     * @param null $data
     * @return JsonResponse
     */
    protected function success($data = null,$isReturnData = false, $msg = '')
    {
        $returnData = [
            'code'    => config('errorcode.STATUSCODE_SUCCESS'),
            'msg'     => empty($msg) ? config('errormsg.MSG_OK') : $msg,
            'success' => true
        ];
        if ($isReturnData) {
            $returnData['data'] = $data;
        } else {
            $data && $returnData['data'] = $data;
        }

        return response()->json($returnData);
    }

    /**
     * fail
     * @param $code
     * @param $msg
     * @return JsonResponse
     */
    protected function fail($code, $msg, $data = null)
    {
        $returnData['statusCode'] = $code;
        $returnData['msg'] = $msg;

        $returnData = [
            'code'    => $code,
            'msg'     => $msg,
            'success' => false
        ];
        $data && $returnData['data'] = $data;

        return response()->json($returnData);
    }

    protected function getParamAll($rule, $trimNull = true)
    {
        $paramAll = Request::all();
        $paramAll = collect($paramAll)->filter(function ($value, $key) use($trimNull){
            if ($trimNull && is_null($value)) {
                return false;
            }
            return true;
        })->toArray();

        return array_merge($rule, array_intersect_key($paramAll, $rule));
    }

    /**
     * 判断开始时间和结束时间
     * <AUTHOR>
     * @date   2019-02-28
     */
    protected function compareTimeValidate($startTime,$endTime)
    {
        if ($endTime <= $startTime) {
            return false;
        }
        return true;
    }

    public function trimNull(array $keys)
    {
        $params = Request::all();
        $params = json_encode($params);
        $params = json_decode(str_replace('null', '""', $params), true);
        $params = array_intersect_key($params,  array_flip($keys));
        $data = $this->array_filter_recursive($params);
        return $data;
    }

    private function array_filter_recursive(array &$arr)
    {
        if (count($arr) < 1) {
            return [];
        }
        foreach ($arr as $k => $v) {
            if (is_array($v)) {
                $arr[$k] = self::array_filter_recursive($v);
            }
            if (is_null($arr[$k]) || $arr[$k] == '' || (is_array($arr[$k]) && empty($arr[$k]))) {
                unset($arr[$k]);
            }
        }
        return $arr;
    }

    /**
     * @param $param
     * @param $rule
     * @param array $message
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/15 10:35 上午
     */
    protected function validatorThrowRuntimeException($param, $rule, $message = [])
    {
        $validator = Validator::make($param, $rule, $message);

        if ($validator->fails()) {

            throw new RuntimeException("", $validator->getMessageBag()->first());
        }
    }
}
