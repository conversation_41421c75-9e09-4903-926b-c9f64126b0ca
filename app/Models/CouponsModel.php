<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 * Class CouponsModel
 * @package App\Models
 * @property $voucher
 * @property $tripartite_voucher
 * @property $coupon_type_id
 * @property $status
 * @property $pcode
 * @property $start_time
 * @property $end_time
 * @property $coupon_type_alias
 */
class CouponsModel extends Model
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'foss_coupons';

    protected $guarded = ["id"];

    protected $fillable = ['batch_id',"batch_no","voucher","coupon_type_id", 'coupon_type_name', 'coupon_type_alias', 'refuel_type',
        'face_value', 'start_time', 'end_time', 'tripartite_img', 'instructions', 'status', 'distribute_batch_no', 'tripartite_voucher',
        'distribute_time', 'charge_off_time', 'tripartite_status', 'creator','modifier','createtime','updatetime', 'sync_foss', 'pcode'];

    public $incrementing = false;

    public $timestamps = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 与订单扩展表一对一
     */
    public function ext(){
        return $this->belongsTo(OrderExtModel::class,'voucher','g7_coupon_flag');
    }

    /**
     * 获取详情
     * @param array $condition
     * @param mixed $select
     * @param bool $withLock
     * @return array
     */
    static public function getInfo(array $condition, $select = ['*'], bool $withLock = false)
    {
        $sqlObj = BaseModel::scopeWithCondition(self::select($select), $condition);
        if ($withLock) {
            $sqlObj = $sqlObj->lockForUpdate();
        }
        $result = $sqlObj->first();
        return !$result ? [] : $result->toArray();
    }

    /**
     * @param array $condition
     * @param string $pluck
     * @return array
     */
    public function getCouponsPluckByCondition(array  $condition,$pluck){
        $sqlObj = BaseModel::scopeWithCondition(self::select(),$condition);

        $result = $sqlObj->pluck($pluck);

        return !$result ? [] : $result->toArray();
    }
}