<?php


namespace GoEasySDK;

use Guzzle\Http\Client;

class Message extends Base
{
	public function __construct()
	{
		parent::__construct();
	}
	
	/**
	 * 发布消息
	 * @param $channel
	 * @param $message
	 * @return false|string
	 */
	public function publish($channel, $message)
	{
		$postData = [
			'channel' => $channel,
			'content' => json_encode($message)
		];
		
		$result = $this->request('POST', 'publish', $postData);
		
		return $result;
	}
}