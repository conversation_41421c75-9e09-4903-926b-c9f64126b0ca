<?php


namespace App\Services;


class ErrorCodeService
{
    protected $errCodeConfig;

    public function __construct()
    {
        $this->errCodeConfig = config('errorcode');
    }

    /**
     * 创建｜编辑补单信息的错误码
     *
     * @param $errorCode
     * @return string
     */
    public function handleAdditionalError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ADDITIONAL_RECORD'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['HANDLE_ADDITIONAL_RECORD'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 创建｜编辑补单详情的错误码
     *
     * @param $errorCode
     * @return string
     */
    public function handleAdditionalItemError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ADDITIONAL_RECORD'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['HANDLE_ITEM_RECORD'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * G7能源账户审核单查询错误码
     *
     * @param $errorCode
     * @return string
     */
    public function selectApproveError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['BUSINESS_LOG'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['APPROVE_SELECT'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * G7能源账户审核单编辑错误码
     *
     * @param $errorCode
     * @return string
     */
    public function editApproveError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['BUSINESS_LOG'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['APPROVE_INSERT_UPDATE'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * G7能源账户审核单审核错误码
     *
     * @param $errorCode
     * @return string
     */
    public function approveError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['BUSINESS_LOG'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['APPROVE'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 创建订单的错误码
     *
     * @param $errorCode
     * @return string
     */
    public function createOrderError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ORDER'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['CREATE_ORDER'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 关单错误码
     *
     * @param $errorCode
     * @return string
     */
    public function closeOrderError($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ORDER'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['CLOSE_ORDER'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 支付
     *
     * @param $errorCode
     * @return string
     */
    public function gotoPay($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ORDER'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['GOTO_PAY'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 退款
     *
     * @param $errorCode
     * @return string
     */
    public function gotoRefund($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ORDER'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['GOTO_REFUND'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * @param $errorCode
     * @return string
     */
    public function checkGoods($errorCode)
    {
        $serviceCode = $this->errCodeConfig['SERVICE_CODE'];
        $businessCode = $this->errCodeConfig['BUSINESS_CODE']['ORDER'];
        $childCode = $this->errCodeConfig['CHILD_CODE'][$businessCode]['GET_GOODS'];

        return (string)$serviceCode.(string)$businessCode.(string)$errorCode.(string)$childCode;
    }

    /**
     * 根据订单服务错误码前缀拼接传入错误代码得到完整错误码
     * @param $errorCode
     * @return string
     */
    public function getOrderErrorCode($errorCode)
    {
        return $this->errCodeConfig['SERVICE_CODE'] .
               $this->errCodeConfig['BUSINESS_CODE']['ORDER'] .
               $errorCode .
               $this->errCodeConfig['CHILD_CODE'][$this->errCodeConfig['BUSINESS_CODE']['ORDER']]['GET_GOODS'];
    }
}