<?php

namespace App\Jobs;

use App\Library\Helper\Common;
use App\Models\ElectricityOrderModel;
use App\Repositories\ElectricityOrderProgressRepository;
use App\Repositories\ElectricityOrderRepository;
use App\Repositories\NewOrderRepository;
use App\Services\MessageService;
use App\Services\NewOrderService;
use App\Servitization\Adapter;
use App\Servitization\FeiShu;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class MonitorElectricityOrderBalance extends Basic
{
    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @param $orderId
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('monitorElectricityOrderBalance', [
                'order_id' => $this->orderId,
            ]);
            $orderInfo = app(ElectricityOrderRepository::class)->getOneElectricityOrderByParams([
                'order_id' => $this->orderId,
            ], [
                'order_id',
                'order_status',
                'supplier_code',
            ]);
            if (empty($orderInfo) or !in_array($orderInfo->order_status, [
                    ElectricityOrderModel::WAIT_START,
                    ElectricityOrderModel::CHARGING,
                ])) {
                return;
            }
            $orderProgressInfo = app(ElectricityOrderProgressRepository::class)->getElectricityOrderProgressByOrderId(
                $this->orderId,
                [
                    'order_id',
                    'expected_end_time',
                ]
            );
            if (bccomp(time(), strtotime($orderProgressInfo->expected_end_time), 0) >= 0) {
                app(Adapter::class)->sendCreateOrderMsg([
                    'message_type' => 'STOP_ELECTRICITY',
                    'data'         => [
                        'order_id' => $this->orderId,
                        'supplier_code' => $orderInfo->supplier_code,
                    ],
                ]);
                return;
            }
            Queue::later(60, new self($this->orderId));
        } catch (Exception $e) {
            app(FeiShu::class)->sendElectricityOrderStopFailed([
                'order_id' => $this->orderId,
                'msg'      => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
                'code'     => $e->getCode(),
            ]);
            Queue::later(60, new self($this->orderId));
        }
    }
}
