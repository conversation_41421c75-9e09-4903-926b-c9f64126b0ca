<?php

/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Services;

use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Library\GoEasySDK\Config;
use App\Library\Helper\Common;
use App\Models\OilOrgModel;
use App\Models\OrderModel;
use App\Models\OrgTruckLimitModel;
use App\Repositories\CardRepository;
use App\Repositories\CardViceBillRepository;
use App\Repositories\CardViceTradesRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationGunRepository;
use App\Repositories\StationPayQrcodesRepository;
use App\Repositories\StationRepository;
use App\Repositories\StationRuleRepository;
use App\Repositories\StationTankRepository;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use App\Servitization\FossUser;
use App\Servitization\Gas;
use Carbon\Carbon;
use GoEasySDK\Message;
use Illuminate\Support\Facades\Log;

class CardService
{
    private $orgReponsitory;
    private $cardViceTradesReponsitory;
    private $stationRuleReponsitory;
    private $historyReponsitory;
    private $cardViceBillReponsitory;
    private $cardReponsitory;
    protected $fossUser;
    protected $truckCardService;
    protected $errorCodeService;

    public function __construct()
    {
        $this->cardViceTradesReponsitory = app(CardViceTradesRepository::class);
        $this->stationRuleReponsitory = app(StationRuleRepository::class);
        $this->historyReponsitory = app(HistoryRepository::class);
        $this->orgReponsitory = app(OrgRepository::class);
        $this->cardViceBillReponsitory = app(CardViceBillRepository::class);
        $this->cardReponsitory = app(CardRepository::class);
        $this->fossUser = app(FossUser::class);
        $this->truckCardService = app(TruckCardService::class);
        $this->errorCodeService = app(ErrorCodeService::class);
    }

    public function getTradeById(array $params)
    {
        if( strlen($params['history_id']) > 30) {
            return $this->cardViceTradesReponsitory->getTradeById(['api_id' => $params['history_id']]);
        }else{
            return $this->cardViceTradesReponsitory->getTradeById(['id' => $params['history_id']]);
        }
    }

    public function getCardStream(array $params)
    {
        return $this->cardViceBillReponsitory->getBillList($params);
    }

    public function getTransferDetail(array $params)
    {
        return $this->cardViceBillReponsitory->getTransferDetails($params);
    }

    public function getAssignDetail(array $params)
    {
        return $this->cardViceBillReponsitory->getAssignDetails($params);
    }

    public function getOilOrgInfoById(array $params)
    {
        return $this->orgReponsitory->getOilOrgById(['id'=>$params['id']]);
    }

    public function getTradeList(array $params)
    {
        return $this->cardViceTradesReponsitory->getTradeList($params);
    }

    public function getCardInfoByPhone(array $params)
    {
        $card_info = $this->cardReponsitory->getCardInfo(['driver_tel'=>$params['phone'],"is_use"=>2]);
        return $card_info;
    }

    public function getCardInfoFromUser(array $params)
    {
        return (new FossUser())->getCardInfoWithCurrAccount($params);
    }

    public function getCardInfo(array $params,$checkPwd = true)
    {
        $card_info = $this->cardReponsitory->getInfoByCardNo(['card_no'=>$params['card_no']]);
        if(!$card_info){
            throw new \RuntimeException('', CommonError::NO_CARD);
        }
        if($card_info->Org->pcode != CommonDefine::CARD_NUMBER_ONE){
            throw new \RuntimeException('', CommonError::ERROR_CARD_PCODE);
        }
        if(!in_array($card_info->ischeck, ['2', '4']) && $checkPwd){
            if(empty($params['password'])){
                throw new \RuntimeException('', CommonError::CARD_PASSWORD);
            }else{
                $this->checkCardPwd($card_info,$params['password'],true);
            }
        }
        return $card_info;
    }

    public function isMustTruckNo(array $params)
    {
        $is_limit = false;
        Common::log('error',"params：".var_export($params,true));
        $card_info = $this->cardReponsitory->getCardInfo(['vice_no'=>$params['vice_no']]);
        Common::log('error',"card_info".var_export($card_info,true));
        if(!$card_info){
            throw new \RuntimeException('', CommonError::NO_CARD);
        }
        //查询机构信息
        $orgInfo = $this->getOilOrgInfoById(['id' => $card_info->org_id]);
        if(!$orgInfo){
            throw new \RuntimeException("机构信息不存在", 2001);
        }

        if(!$card_info->truck_type){
            Common::log('error',"未设置truck_type值");
            return $is_limit; // 无车辆类型
        }

        //自有，外协转换
        $truck_type = $card_info->truck_type == 1 ? 10 : 20;

        //是否平台
        if($orgInfo->is_system_orgcode == 2){
            Common::log('error',"是平台机构");
            return $is_limit;
        }

        //取机构限制配置逐级向上找机构
        $topOrgcode = substr($orgInfo->orgcode,0,6);
        $size = $topOrgcode == '201XW3' ? 10 : 6;
        $orgcodeList = $this->findOrgCode($orgInfo->orgcode,$size);
        Common::log('error',"orgcodeList".var_export($orgcodeList,true));
        $limitInfo = OrgTruckLimitModel::whereIn('orgcode',$orgcodeList)
            ->where('is_open',2)
            ->orderBy('orgcode','desc')
            ->first();
        if(!$limitInfo){
            Common::log('error',"未配置车牌加油限制");
            return $is_limit;
        }

        //自有卡，外协卡
        if($limitInfo->is_open == 1){
            Common::log('error',"is_open值是关闭");
            return $is_limit;
        }elseif ($limitInfo->is_open == 2) {
            //限制开启
            $limit_type_list = json_decode($limitInfo->car_limit_type,true);
            Common::log('error',"limit_type_list".var_export($limit_type_list,true));
            if(in_array($truck_type,$limit_type_list)){
                $is_limit = true;
            }
        }else{
            $is_limit = false;
        }
        Common::log('error',"res".$is_limit);
        return $is_limit;
    }

    public function findOrgCode($orgcode,$size)
    {
        $len       = strlen($orgcode);
        $start     = $size;
        $orgRoot[] = substr($orgcode, 0, $start);
        $splitArr = [];
        if ($len > $size) {
            $arr = str_split(substr($orgcode, -($len - $size)), 2);
            if ($arr) {
                foreach ($arr as $v) {
                    $start       += 2;
                    $splitArr[] = substr($orgcode, 0, $start);
                }
            }
        }

        return array_merge($orgRoot, $splitArr);
    }

    //验证G7能源账户密码
    public function checkCardPwd($cardInfo,$post_pwd,$isThrow = false)
    {
        $error_num = $cardInfo->pwd_errornum;
        if($error_num >= 5){
            if($isThrow){
                throw new \RuntimeException('密码已连续输错5次，您的账户将被锁定', CommonError::OVER_CARD_PWD);
            }else {
                return ['s' => -1, 'm' => '密码已连续输错5次，您的账户将被锁定'];
            }
        }elseif ($cardInfo->password != $post_pwd && $cardInfo->ischeck != 4) {
            $num = $error_num + 1;
            $setarr['pwd_errornum'] = $num;
            if ($num >= 5) {
                $setarr['islock'] = 1;
            }
            $this->cardReponsitory->editCardInfo(['id'=>$cardInfo->id],$setarr);
            if($num >= 5){
                //同步FossG7能源账户信息
                if($cardInfo->ischeck == 2 || $cardInfo->ischeck ==4) {
                    $ischeck = 4;
                } else {
                    $ischeck = 1;
                }
                (new Foss())->cardSet([
                    'vice_no' => $cardInfo->card_no,
                    'status' => 30,
                    'paylimit'  => $cardInfo->paylimit== 1 ? 1 :2,
                    'oil_top'   => $cardInfo->oil_top,
                    'month_top'  => $cardInfo->month_top,
                    'card_level'  => $cardInfo->card_level,
                    'day_top'   => $cardInfo->day_top,
                    'isreceive'   => $cardInfo->isactivation== 2 ? 1 :2,//领卡状态 foss,1:领取，2：未领取
                    'setpwd'   => $ischeck,//交易验证设置 setpwd 4:免密,1:使用密码
                    'ischeck' => $cardInfo->ischeck,
                ]);
                if($isThrow){
                    throw new \RuntimeException('密码已连续输错5次，您的账户将被锁定', CommonError::OVER_CARD_PWD);
                }else {
                    return ['s' => -1, 'm' => '密码已连续输错5次，您的账户将被锁定'];
                }
            }else {
                $leftNum = 4 - $error_num;
                if($isThrow){
                    throw new \RuntimeException('密码输入错误，您还可重试'.$leftNum."次", CommonError::ERROR_CARD_PWD);
                }else {
                    return ['s' => -2, 'm' => '密码输入错误 请重新尝试', "data" => ["leftNum" => $leftNum]];
                }
            }
        }elseif ($error_num && !$cardInfo->islock){
            $setarr['pwd_errornum'] = 0;
            $this->cardReponsitory->editCardInfo(['id'=>$cardInfo->id],$setarr);
        }
        return true;
    }

    //订单列表获取订单详情
    public function getTradeDetail(array $params)
    {
        $info = $this->getTradeById($params);
        if(!$info) {
            throw new \RuntimeException('',CommonError::BILL_NO_FOUND);
        }
        $info->trade_num = $info->trade_num ? number_format(abs($info->trade_num),2,".","") : "0.00";
        $info->service_money = $info->service_money ? number_format(abs($info->service_money),2,".","") : "0.00";
        $info->total_money = $info->total_money ? $info->total_money : "0.00";
        $info->truck_no = $info->truck_no ? $info->truck_no : "";
        $info->bill_no = "ZF".$this->createNo();
        $info->oil_com_txt = $this->getOilCom($info->oil_com);
        $info->unit = $this->getUnit($info->oil_name);
        if(!empty($info->cancel_sn) && $info->trade_money < 0){
            $info->is_refund = 1;
        }else{
            $info->is_refund = 2;
        }
        if($info->trade_money > 0) {
            $info->trade_money_txt = $info->trade_money ? "-".$info->trade_money : "0.00";
        }else{
            $info->trade_money_txt = $info->trade_money ? "+".number_format(abs($info->trade_money),2,".","") : "0.00";
        }

        $info->showQrcode = 1; //不展示
        if( in_array($info->pcode,CommonDefine::getPayMentPcode()) && empty($info->cancel_sn) &&
            isset($info->tradeExt) && isset($info->tradeExt) && in_array($info->tradeExt->data_from,[102,202,302]) ) {
            $info->showQrcode = 2;
        }

        $info->isRewrite = 1; //是否打开新页
        if( in_array($info->pcode,CommonDefine::skipHtmlPcode()) ) {
            $info->isRewrite = 2;
        }

        //E站途无需展示核销二维码
        if( in_array($info->pcode,CommonDefine::exceptPcode()) ){
            $info->showQrcode = 1;
        }

        $info->float_price = 0;
        $orgInfo = $this->getOilOrgInfoById(['id' => $info->org_id]);
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($orgInfo->orgcode,0,6),$specialList['SPECIAL_ORGLIST']) ){
            $v_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $info->trade_price = number_format(($info->trade_price - $v_price),2,".","");
            $info->float_price = $v_price;
        }
        unset($info->tradeExt);
        return $info;
    }

    public function createNo()
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = date("ymds", time());
        $str .= substr($microArr[0], 3, 2);
        $str .= sprintf('%02d', rand(0, 99));
        return $str;
    }

    public function getOilCom($oil_com)
    {
        if($oil_com == 21){
            $oil_com_txt = '共享账户';
        }elseif($oil_com == 30){
            $oil_com_txt = '发财账户';
        }else{
            $oil_com_txt = '充值账户';
        }
        return $oil_com_txt;
    }

    public function getUnit($oil_name)
    {
        if (stripos($oil_name, "天然气") !== false) {
            $unit = 'Kg';
            if (stripos($oil_name, "压缩天然气") !== false) {
                $unit = '立方';
            }
            if (stripos($oil_name, "液化天然气") !== false) {
                $unit = 'Kg';
            }
        } else {
            $unit = "升";
        }
        return $unit;
    }

    public function getCardBill(array $params)
    {
        $userList = (new FossUser())->getUserInfo(['uid'=>$params['user_id']]);
        if(!isset($userList['history_mobiles']) || empty($userList) || count($userList['history_mobiles']) == 0){
            throw new \RuntimeException('',CommonError::USER_ERROR);
        }
        $mobiles = $userList['history_mobiles'];
        unset($params['user_id']);
        if(isset($userList['orgRoot']) && !empty($userList['orgRoot'])){
            //查找该用户在该机构下的电子卡
            $cardList = (new FossUser())->getUserCardList(['phones'=>$mobiles,'orgRoot'=>$userList['orgRoot']]);
            if(!$cardList || count($cardList) == 0){
                throw new \RuntimeException('',CommonError::USER_ERROR);
            }
            $params['viceNos'] = $cardList;
        }
        //$mobiles[0] = '15910216897';
        //$mobiles[1] = '18610390918';
        $params['driver_phone'] = $mobiles;
        $params['inCome'] = 1;
        $inCome = $this->getCardStream($params);
        unset($params['inCome']);
        $params['outCome'] = 1;
        $outCome = $this->getCardStream($params);
        unset($params['outCome']);
        $list = $this->getCardStream($params);
        if(count($list) == 0){
            return [
                "current_page"=> 0,
                "data" => [],
                "first_page_url"=> "",
                "from"=> 0,
                "last_page"=> 0,
                "last_page_url"=> "",
                "next_page_url"=> "",
                "path"=> "",
                "per_page"=> "10",
                "prev_page_url"=> "",
                "to"=> 0,
                "total"=> 0,
                "totalIn" => 0,
                "totalOut" => 0
            ];
        }
        $pcodeList = CommonDefine::getPayMentPcode();
        foreach ($list as &$_item)
        {
            //1:已退款，2：无退款
            if(in_array($_item->res_type, [10, 15, 102]) && $_item->amount > 0){
                $_item->is_refund = 1;
            }else{
                $_item->is_refund = 2;
            }
            $_item->trade_money = $_item->amount;
            if($_item->amount > 0) {
                $_item->trade_money_txt = $_item->amount ? "+".number_format(abs($_item->amount),2,".","") : "0.00";
            }else{
                $_item->trade_money_txt = $_item->amount ? $_item->amount : "0.00";
            }
            if( $_item->pay_type == 20 ){
                $_item->account_type = "授信账户";
            }else{
                $_item->account_type = "现金账户";
            }
            $_item->vice_no = $_item->card_no;
            $_item->trade_place = $_item->trade_desc;
            $_item->history_id = "";
            $_item->showQrcode = 1; //不展示
            if($_item->res_type == 10) {
                $detail = $this->getTradeById(['history_id'=>$_item->res_id]);
                $_item->history_id = $detail->api_id;
                if (in_array($detail->pcode, $pcodeList) && $_item->is_refund == 2 &&
                    isset($detail->tradeExt) && isset($detail->tradeExt) && in_array($detail->tradeExt->data_from,[102,202,302]) ) {
                    $_item->showQrcode = 2;
                }
            }
        }
        $result = $list->toArray();
        $result['totalIn'] = number_format(abs($inCome),2,".","");
        $result['totalOut'] = number_format(abs($outCome),2,".","");
        return $result;
    }


    /**
     * 验G7能源账户,并获取账户信息
     *
     * @param $cardNo
     * @param $servicePrice
     * @param $oilMoney
     * @param $serviceMoney
     * @param $checkAccountExpire
     * @return array
     * @throws \Exception
     */
    public function checkCardAndGetArray($cardNo, $servicePrice, $oilMoney, $serviceMoney, $checkAccountExpire=true)
    {
        // G7能源账户信息
        $cardAndAccount = $this->fossUser->getCardInfoWithCurrAccount(['card_no' => $cardNo]);
        if (empty($cardAndAccount)) {
            throw new \RuntimeException('验证G7能源账户失败,无效的账号!', $this->errorCodeService->createOrderError(40307));
        }
        // G7能源账户状态
        if (abs($cardAndAccount['card_status'] - 10)) {// 10正常 20锁定
            throw new \RuntimeException('验证G7能源账户失败,G7能源账户状态无效!', $this->errorCodeService->createOrderError(40308));
        }
        // G7能源账户使用方式
        $cardLevelMap = [1 => OrderModel::PERSONAL_CARD, 2 => OrderModel::CAR_CARD];
        if (!array_key_exists($cardAndAccount['card_level'], $cardLevelMap)) {
            throw new \RuntimeException('验证G7能源账户失败,G7能源账户使用方式失败!', $this->errorCodeService->createOrderError(40309));
        }
        if ($cardAndAccount['card_level'] == OrderModel::CAR_CARD) {
            if ($cardAndAccount['truck_version'] == '2.0') {
                if ($checkAccountExpire && $cardAndAccount['left_seconds'] == 0) {
                    $this->truckCardService->dispatchTruckCardReturnJob($cardAndAccount['driver_tel'], $cardNo);
                    throw new \RuntimeException('车辆账户已过期，请重新扫码领取或换账户支付!', $this->errorCodeService->createOrderError(40332));
                }

                if (! $this->truckCardService->checkTruckCardTake($cardAndAccount['driver_tel'], $cardNo)) {
                    throw new \RuntimeException('您已不在车辆账户使用白名单内，请联系车队长、客服～', $this->errorCodeService->createOrderError(40333));
                }
            } else {
                $driverInfo = $this->truckCardService->checkCardAndGetInfo($cardNo,$cardAndAccount['orgcode'],$cardAndAccount['limit_type']);
            }
        }
        // G7能源账户类型
        $cardTypeMap = [20 => OrderModel::VALUE_CARD, 21 => OrderModel::SHARE_CARD, 30 => OrderModel::FACAI_CARD];
        if (!array_key_exists($cardAndAccount['card_type'], $cardTypeMap)) {
            throw new \RuntimeException('验证G7能源账户失败,无效的G7能源账户类型!', $this->errorCodeService->createOrderError(40310));
        }
        // 机构账户
        if (abs($cardAndAccount['account_status'] - 10)) { // 10正常 20异常
            throw new \RuntimeException('验证G7能源账户失败,G7能源扣款账户异常!', $this->errorCodeService->createOrderError(40311));
        }
        // 服务费
        if (abs($servicePrice - $cardAndAccount['service_rate'])) {
            throw new \RuntimeException('验证G7能源账户失败,服务费'.$servicePrice.'和当前服务费'.$cardAndAccount['service_rate'].'不一致,请核对!', $this->errorCodeService->createOrderError(40312));
        }

        $payMoney = round(bcadd($oilMoney, $serviceMoney, 3), 2);
        // G7能源账户余额
        if ($payMoney > $cardAndAccount['card_balance']) {
            throw new \RuntimeException('验证G7能源账户失败,司机所属机构的G7结算账户余额不足!', $this->errorCodeService->createOrderError(40313));
        }
        // G7能源账户限额
        if ($cardAndAccount['paylimit'] == 1) {

            if ($payMoney > $cardAndAccount['oil_top']) {
                throw new \RuntimeException('验证G7能源账户失败,加油金额超过单次限额,请司机联系车队长!', $this->errorCodeService->createOrderError(40314));
            }
            if ($payMoney > $cardAndAccount['day_top']) {
                throw new \RuntimeException('验证G7能源账户失败,加油金额超过当日限额,请司机联系车队长!', $this->errorCodeService->createOrderError(40315));
            }
            if ($payMoney > $cardAndAccount['month_top']) {
                throw new \RuntimeException('验证G7能源账户失败,加油金额超过当月限额,请司机联系车队长!', $this->errorCodeService->createOrderError(40316));
            }
        }

        $insertCardArray = [
            'org_code' => $cardAndAccount['orgcode'],
            'card_type' => $cardTypeMap[$cardAndAccount['card_type']],
            'card_level' => $cardLevelMap[$cardAndAccount['card_level']],
        ];
        $extendDriverInfo = [
            'driver_name' => empty($driverInfo) ? $cardAndAccount['driver_name'] : $driverInfo['driver_name'],
            'driver_phone' => empty($driverInfo) ? $cardAndAccount['driver_tel'] : $driverInfo['driver_phone'],
            'truck_no' => $cardAndAccount['truck_no'],
        ];
        $accountTypeMap = [10 => '现金账户', 20 => '授信账户'];
        $insertCardMirror = [
            'org_name' => $cardAndAccount['org_name'],
            'card_type_name' => OrderModel::$CARD_TYPE[$cardTypeMap[$cardAndAccount['card_type']]],
            'card_level_name' => OrderModel::$CARD_LEVEL[$cardLevelMap[$cardAndAccount['card_level']]],
            'account_type_name' => $accountTypeMap[$cardAndAccount['account_type']],
            'account_name' => $cardAndAccount['account_name'],
            'account_no' => $cardAndAccount['account_no'],
            'is_check_password' => $cardAndAccount['ischeck'] == 1 ? 1 : 0,
        ];

        return [
            'insert_card_array' => $insertCardArray,
            'insert_card_mirror' => $insertCardMirror,
            'extends_driver_info' => $extendDriverInfo
        ];
    }

}