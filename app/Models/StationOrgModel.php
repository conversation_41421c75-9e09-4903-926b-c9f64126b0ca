<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use Illuminate\Support\Collection;

class StationOrgModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_station_org_list';

    protected $guarded = ["id"];

    public function getData(array $params, array $select = ['*']): Collection
    {
        return self::scopeWithCondition(self::select($select), $params)->get();
    }
}