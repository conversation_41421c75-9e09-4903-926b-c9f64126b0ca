<?php

namespace App\Services;

use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Jobs\MonitorElectricityOrderBalance;
use App\Library\Helper\Common;
use App\Models\CityModel;
use App\Models\ElectricityOrderModel;
use App\Models\HistoryModel;
use App\Models\OrderModel;
use App\Repositories\CardRepository;
use App\Repositories\ElectricityOrderDetailRepository;
use App\Repositories\ElectricityOrderExtRepository;
use App\Repositories\ElectricityOrderProgressRepository;
use App\Repositories\ElectricityOrderRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\SupplierRepositories;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Log;
use RuntimeException;
use Throwable;

class ElectricityOrderService
{
    private $electricityOrderRepository;

    private $electricityOrderExtRepository;

    private $electricityOrderProgressRepository;

    private $electricityOrderDetailRepository;

    public function __construct(
        ElectricityOrderRepository $electricityOrderRepository,
        ElectricityOrderExtRepository $electricityOrderExtRepository,
        ElectricityOrderProgressRepository $electricityOrderProgressRepository,
        ElectricityOrderDetailRepository $electricityOrderDetailRepository
    ) {
        $this->electricityOrderRepository = $electricityOrderRepository;
        $this->electricityOrderExtRepository = $electricityOrderExtRepository;
        $this->electricityOrderProgressRepository = $electricityOrderProgressRepository;
        $this->electricityOrderDetailRepository = $electricityOrderDetailRepository;
    }

    public function checkElectricityOrderAndAccount($user_id): array
    {
        $result = app(FossUser::class)->getUserAndElectricityCard(['uid' => $user_id]);
        if (empty($result['user_info']) or !isset($result['user_info']['history_mobiles']) or
            count($result['user_info']['history_mobiles']) == 0) {
            throw new RuntimeException('', CommonError::USER_ERROR);
        }
        $orderId = 0;
        $orderInfo = $this->electricityOrderRepository->getOneElectricityOrderByParams([
            'driver_phone' => $result['user_info']['history_mobiles'],
            'order_status' => [
                ElectricityOrderModel::WAIT_START,
                ElectricityOrderModel::CHARGING,
                ElectricityOrderModel::STOPPING,
                ElectricityOrderModel::WAIT_PAY,
            ],
        ], [
            'order_id'
        ]);
        if ($orderInfo) {
            $orderId = $orderInfo->order_id;
        }
        return [
            'in_progress_order_id' => (string)$orderId,
            'has_electricity_card' => count($result['electricity_card']) > 0,
        ];
    }

    /**
     * 获取卡片信息并验证密码
     * 通过调用第三方服务获取卡片信息，并根据卡片状态和提供的密码进行验证
     * @param int $cardNo 卡片编号，用于查询卡片信息
     * @param string $password 卡片密码，用于验证卡片安全性
     * @return array 卡片信息数组，包含卡片详细数据
     * @throws RuntimeException 如果卡片密码未提供或密码验证失败，则抛出运行时异常
     */
    public function getCardInfoAndCheckPassword(int $cardNo, string $password): array
    {
        $cardInfo = app(Foss::class)->getAccountBalance([
            'vice_no' => $cardNo,
        ]);
        if (!in_array($cardInfo['ischeck'], [2, 4])) {
            if (empty($password)) {
                throw new RuntimeException('', CommonError::CARD_PASSWORD);
            }
            $cardALLInfo = app(CardRepository::class)->getInfoByCardNo(['card_no' => $cardNo]);
            app(CardService::class)->checkCardPwd($cardALLInfo, $password, true);
        }
        return $cardInfo;
    }

    /**
     * 检查给定的卡号是否可以创建订单
     * 对于特定的卡号，如果没有正在进行中的订单，则允许创建新订单
     * 如果卡号关联了正在进行中的订单，并且该用户无其它可用卡，则抛出异常阻止创建新订单
     * @param int $cardNo 卡号
     * @param mixed $userId 用户ID
     * @throws RuntimeException 如果卡号有关联的正在进行中的订单，则抛出运行时异常
     */
    public function checkCardCanCreateOrder(int $cardNo, $userId)
    {
        $cardInProgressOrder = $this->electricityOrderRepository->getOneElectricityOrderByParams([
            'card_no'      => $cardNo,
            'order_status' => [
                ElectricityOrderModel::WAIT_START,
                ElectricityOrderModel::CHARGING,
                ElectricityOrderModel::STOPPING,
                ElectricityOrderModel::WAIT_PAY,
            ],
        ], [
            'order_id'
        ]);
        $userCardAllData = array_column(
            app(FossUser::class)->getUserAndElectricityCard([
                'uid' => $userId
            ])['electricity_card'],
            'vice_no'
        );
        if (!in_array($cardNo, $userCardAllData)) {
            throw new RuntimeException(
                '',
                CommonError::NO_CARD
            );
        }
        if ($cardInProgressOrder) {
            if (!array_diff($userCardAllData, [$cardNo])) {
                throw new RuntimeException(
                    '',
                    CommonError::CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_2
                );
            }
            throw new RuntimeException(
                '',
                CommonError::CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_1
            );
        }
    }

    /**
     * @throws Exception
     */
    public function getAndCheckStationForCreateOrder(string $stationId, $connectorId)
    {
        $stationData = app(FossStation::class)->getElectricityStation([
            'station_id' => $stationId,
        ]);
        $price = $power = $maxPrice = 0;
        $hourMinute = date('H:i');
        $thirdConnectorId = '';
        foreach ($stationData['connector'] as $item) {
            if ($item['id'] == $connectorId) {
                $power = $item['power'];
                $thirdConnectorId = $item['connector_id'];
                break;
            }
        }
        foreach ($stationData['supply_price'] as $item) {
            if ($item['start_time'] <= $hourMinute and $hourMinute <= $item['end_time']) {
                $price = bcadd($item['electricity_fee'], $item['service_fee'], 2);
            }
            $maxPrice = max($maxPrice, bcadd($item['electricity_fee'], $item['service_fee'], 2));
        }
        if (empty($price)) {
            throw new RuntimeException('', CommonError::ERROR_PRICE);
        }
        if (empty($power)) {
            throw new RuntimeException('', CommonError::STATION_CONNECT_ERROR);
        }
        return array_merge($stationData, [
            'power'              => $power,
            'price'              => $price,
            'max_price'          => $maxPrice,
            'third_connector_id' => $thirdConnectorId,
        ]);
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function createElectricityOrder(array $params): array
    {
        $cardInfo = $this->getCardInfoAndCheckPassword($params['card_no'], (string)($params['password'] ?? ''));
        $this->checkCardCanCreateOrder((int)$params['card_no'], $params['_user_id']);
        $stationData = $this->getAndCheckStationForCreateOrder($params['station_id'], $params['connector_id']);
        if (bccomp($cardInfo['can_use_balance'], $params['money'], 2) < 0) {
            throw new RuntimeException('', CommonError::ACCOUNT_MONEY_NOT_ENOUGH);
        }
        if (bccomp($params['money'], 0.00, 2) <= 0 or bccomp(
                                                          $params['money'],
                                                          round(
                                                              bcdiv(
                                                                  bcmul(
                                                                      bcmul(
                                                                          $stationData['power'],
                                                                          $stationData['price'],
                                                                          10
                                                                      ),
                                                                      2,
                                                                      10
                                                                  ),
                                                                  60,
                                                                  2
                                                              ),
                                                              3
                                                          ),
                                                          2
                                                      ) < 0) {
            throw new RuntimeException('', CommonError::ELECTRICITY_ACCOUNT_MONEY_NOT_ENOUGH);
        }
        $orderId = app(RedisService::class)->makeOrderIdForElectricity();
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $regionInfo = CityModel::whereIn('city_code', [
                $stationData['station']['province_code'],
                $stationData['station']['city_code'],
            ])->get()->keyBy('city_code');
            $supplierName = app(SupplierRepositories::class)->getSupplierByScodes([
                'scode' => $stationData['station']['supplier_code'],
            ], [
                'supplier_name',
            ])->first()->supplier_name;
            $result = $this->electricityOrderRepository->insertElectricityOrder([
                'order_id'          => $orderId,
                'supplier_order_id' => '',
                'customer_order_id' => '',
                'driver_phone'      => $cardInfo['driver_tel'],
                'station_id'        => $params['station_id'],
                'org_code'          => $cardInfo['orgcode'],
                'supplier_code'     => $stationData['station']['supplier_code'],
                'card_no'           => $params['card_no'],
                'create_time'       => date('Y-m-d H:i:s'),
                'order_price'       => $stationData['max_price'],
                'order_money'       => $params['money'],
            ]);
            if (!$result) {
                throw new RuntimeException('', CommonError::ORDER_ERROR);
            }
            $result = $this->electricityOrderExtRepository->insertElectricityOrderExt([
                'order_id'        => $orderId,
                'connector_id'    => $params['connector_id'],
                'driver_name'     => $cardInfo['driver_name'],
                'truck_no'        => $cardInfo['truck_no'],
                'station_code'    => $stationData['station']['station_code'],
                'station_name'    => $stationData['station']['station_name'],
                'station_address' => $stationData['station']['address'],
                'supplier_name'   => $supplierName,
                'org_name'        => $cardInfo['org_name'],
                'province_code'   => $stationData['station']['province_code'],
                'city_code'       => $stationData['station']['city_code'],
                'province_name'   => $regionInfo[$stationData['station']['province_code']]['city_name'],
                'city_name'       => $regionInfo[$stationData['station']['city_code']]['city_name'],
                'power'           => $stationData['power'],
                'creator'         => $cardInfo['driver_name'],
            ]);
            if (!$result) {
                throw new RuntimeException('', CommonError::ORDER_ERROR);
            }
            $result = $this->electricityOrderProgressRepository->insertOrUpdateElectricityOrderProgressByOrderId(
                $orderId,
                [
                    'expected_end_time'  => date(
                        'Y-m-d H:i:s',
                        strtotime(
                            '+' . round(
                                bcmul(
                                    bcsub(
                                        bcdiv(
                                            $params['money'],
                                            bcdiv(
                                                bcmul($stationData['power'], $stationData['max_price'], 10),
                                                60,
                                                10
                                            ),
                                            10
                                        ),
                                        2,
                                        10
                                    ),
                                    60,
                                    2
                                )
                            ) . ' seconds'
                        )
                    ),
                    'third_connector_id' => $stationData['third_connector_id'],
                ]
            );
            if (!$result) {
                throw new RuntimeException('', CommonError::ORDER_ERROR);
            }
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
        try {
            app(Foss::class)->addFreezeDetail([
                'vice_no'  => $params['card_no'],
                'amount'   => $params['money'],
                'order_no' => $orderId,
            ]);
        } catch (Throwable $exception) {
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($orderId, [
                'order_status' => ElectricityOrderModel::ORDER_CANCEL,
            ]);
            $this->electricityOrderExtRepository->updateElectricityOrderExtByOrderId($orderId, [
                'remark' => $exception->getMessage(),
            ]);
            throw $exception;
        }
        try {
            $result = app(Adapter::class)->sendCreateOrderMsg([
                'message_type' => 'START_ELECTRICITY',
                'data'         => [
                    'order_id'      => $orderId,
                    'truck_no'      => $cardInfo['truck_no'],
                    'driver_phone'  => $cardInfo['driver_tel'],
                    'connector_id'  => $stationData['third_connector_id'],
                    'supplier_code' => $stationData['station']['supplier_code'],
                ],
            ]);
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($orderId, [
                'supplier_order_id' => $result['order_id'],
            ]);
        } catch (Throwable $exception) {
            try {
                app(Foss::class)->unFreezeDetail([
                    'order_no' => $orderId,
                ]);
            } catch (Throwable $exception) {
                Log::error('解冻失败', [
                    'order_no'  => $orderId,
                    'exception' => [
                        'message' => $exception->getMessage(),
                        'code'    => $exception->getCode(),
                        'file'    => $exception->getFile(),
                        'line'    => $exception->getLine(),
                    ],
                ]);
            }
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($orderId, [
                'order_status' => ElectricityOrderModel::ORDER_CANCEL,
            ]);
            $this->electricityOrderExtRepository->updateElectricityOrderExtByOrderId($orderId, [
                'remark' => $exception->getMessage(),
            ]);
            throw $exception;
        }
        dispatch(new MonitorElectricityOrderBalance($orderId))->delay(60)->onQueue('order-queue');
        return [
            'order_id' => $orderId
        ];
    }

    /**
     * @throws Throwable
     */
    public function orderStartElectricity(array $params)
    {
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderModel = $this->electricityOrderRepository->getOneElectricityOrderByParams([
                'order_id' => (int)$params['order_id'],
            ], ['order_id', 'order_status'], true);
            if (empty($orderModel)) {
                throw new RuntimeException('', CommonError::BILL_NO_FOUND);
            }
            if ($orderModel->order_status != ElectricityOrderModel::WAIT_START) {
                throw new RuntimeException('', CommonError::ELECTRICITY_ORDER_STATUS_EXCEPTION);
            }
            $this->electricityOrderRepository->updateElectricityOrderByOrderId((int)$params['order_id'], [
                'order_status' => $params['start_result'] == 1 ? ElectricityOrderModel::CHARGING :
                    ElectricityOrderModel::ORDER_CANCEL,
            ]);
            if (!empty($params['stop_code'])) {
                $this->electricityOrderExtRepository->updateElectricityOrderExtByOrderId((int)$params['order_id'], [
                    'stop_code' => $params['stop_code'],
                ]);
            }
            $this->electricityOrderProgressRepository->insertOrUpdateElectricityOrderProgressByOrderId(
                (int)$params['order_id'],
                [
                    'order_id'   => $params['order_id'],
                    'start_time' => $params['start_time'],
                ]
            );
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
    }

    /**
     * @throws Throwable
     */
    public function orderStopElectricity(array $params)
    {
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderModel = $this->electricityOrderRepository->getOneElectricityOrderByParams([
                'order_id' => (int)$params['order_id'],
            ], ['order_id', 'order_status'], true);
            if (empty($orderModel)) {
                throw new RuntimeException('', CommonError::BILL_NO_FOUND);
            }
            if ($orderModel->order_status != ElectricityOrderModel::CHARGING) {
                throw new RuntimeException('', CommonError::ELECTRICITY_ORDER_STATUS_EXCEPTION);
            }
            if ($params['stop_result'] == 1) {
                $this->electricityOrderRepository->updateElectricityOrderByOrderId((int)$params['order_id'], [
                    'order_status' => ElectricityOrderModel::WAIT_PAY,
                ]);
            }
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
    }

    /**
     * @throws Throwable
     */
    public function orderGoingElectricity(array $params)
    {
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderModel = $this->electricityOrderRepository->getOneElectricityOrderByParams([
                'order_id' => (int)$params['order_id'],
            ], ['order_id', 'order_money', 'order_status', 'order_price'], true);
            if (empty($orderModel)) {
                throw new RuntimeException('', CommonError::BILL_NO_FOUND);
            }
            if ($orderModel->order_status != ElectricityOrderModel::CHARGING) {
                throw new RuntimeException('', CommonError::ELECTRICITY_ORDER_STATUS_EXCEPTION);
            }
            $orderExtModel = $this->electricityOrderExtRepository->getOneElectricityOrderExtByParams([
                'order_id' => (int)$params['order_id'],
            ], ['power']);
            $orderProgressModel = $this->electricityOrderProgressRepository->getElectricityOrderProgressByOrderId(
                (int)$params['order_id']
                ,
                ['expected_end_time']
            );
            if (bccomp(bcsub($orderProgressModel->expected_end_time->timestamp, time(), 5), 2) >= 0) {
                $loseTime = 0.00;
                if (!empty($params['total_num']) and !empty($params['total_money'])) {
                    $loseTime = round(
                        bcmul(
                            bcsub(
                                bcdiv(
                                    bcsub($orderModel->order_money, $params['total_money'], 2),
                                    bcdiv(
                                        bcmul(
                                            $orderExtModel->power,
                                            $orderModel->order_price,
                                            10
                                        ),
                                        60,
                                        10
                                    ),
                                    10
                                ),
                                2,
                                10
                            ),
                            60,
                            2
                        )
                    );
                }
                if (!empty($loseTime)) {
                    $orderProgressModel->expected_end_time = date(
                        'Y-m-d H:i:s',
                        bcadd(strtotime($params['fetch_time']), max($loseTime, 0), 0)
                    );
                }
            }
            $this->electricityOrderProgressRepository->insertOrUpdateElectricityOrderProgressByOrderId(
                (int)$params['order_id'],
                [
                    'fetch_time'              => $params['fetch_time'],
                    'connector_status'        => $params['connector_status'],
                    'current_a'               => $params['current_a'],
                    'current_b'               => $params['current_b'],
                    'current_c'               => $params['current_c'],
                    'voltage_a'               => $params['voltage_a'],
                    'voltage_b'               => $params['voltage_b'],
                    'voltage_c'               => $params['voltage_c'],
                    'total_num'               => $params['total_num'],
                    'total_electricity_money' => $params['total_electricity_money'],
                    'total_service_money'     => $params['total_service_money'],
                    'total_money'             => $params['total_money'],
                    'price'                   => $params['price'],
                    'remainder_num'           => $params['remainder_num'],
                    'expected_end_time'       => $orderProgressModel->expected_end_time,
                ]
            );
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
    }

    /**
     * @throws Throwable
     */
    public function orderFinishElectricity(array $params)
    {
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderModel = $this->electricityOrderRepository->getOneElectricityOrderByParams([
                'order_id' => $params['order_id'],
            ], [
                'order_id',
                'order_status',
                'card_no',
                'station_id',
                'customer_order_id',
                'create_time',
                'supplier_code',
                'org_code',
                'driver_phone',
            ], true);
            $orderExtModel = $this->electricityOrderExtRepository->getOneElectricityOrderExtByParams([
                'order_id' => $params['order_id'],
            ], [
                'station_name',
                'station_code',
                'station_address',
                'province_code',
                'province_name',
                'city_code',
                'city_name',
                'truck_no',
                'driver_name',
            ]);
            if (empty($orderModel) or empty($orderExtModel)) {
                throw new RuntimeException('', CommonError::BILL_NO_FOUND);
            }
            if (!in_array($orderModel->order_status, [
                ElectricityOrderModel::CHARGING,
                ElectricityOrderModel::STOPPING,
                ElectricityOrderModel::WAIT_PAY,
            ])) {
                throw new RuntimeException('', CommonError::ELECTRICITY_ORDER_STATUS_EXCEPTION);
            }
            $price = round(bcdiv($params['money'], $params['num'], 3), 2);
            $discountedPrice = round(bcdiv($params['discounted_money'], $params['num'], 3), 2);
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($params['order_id'], [
                'electricity_num'            => $params['num'],
                'electricity_money'          => $params['electricity_money'],
                'price'                      => $price,
                'service_money'              => $params['service_money'],
                'money'                      => $params['money'],
                'supplier_electricity_money' => $params['discounted_electricity_money'],
                'supplier_price'             => $discountedPrice,
                'supplier_service_money'     => $params['discounted_service_money'],
                'supplier_money'             => $params['discounted_money'],
                'customer_electricity_money' => $params['electricity_money'],
                'customer_price'             => $price,
                'customer_service_money'     => $params['service_money'],
                'customer_money'             => $params['money'],
                'pay_electricity_money'      => $params['electricity_money'],
                'pay_price'                  => $price,
                'pay_service_money'          => $params['service_money'],
                'pay_money'                  => $params['money'],
            ]);
            foreach ($params['order_detail'] as &$v) {
                $v['order_id'] = $params['order_id'];
            }
            $this->electricityOrderDetailRepository->insertElectricityOrderDetail($params['order_detail']);
            $this->electricityOrderProgressRepository->insertOrUpdateElectricityOrderProgressByOrderId(
                (int)$params['order_id'],
                [
                    'end_time' => $params['end_time'],
                    'end_desc' => $params['stop_reason'],
                ]
            );
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $result = app(Foss::class)->tradePay([
                'order_no'             => $orderModel->order_id,
                'card_no'              => $orderModel->card_no,
                'sale_type'            => '现金消费',
                'unit'                 => 1,
                'station_id'           => $orderModel->station_id,
                'station_name'         => $orderExtModel->station_name,
                'station_code'         => $orderExtModel->station_code,
                'pay_money'            => $params['money'],
                'pay_unit_price'       => $price,
                'service_money'        => 0,
                'oil_num'              => $params['num'],
                'third_order_no'       => empty($orderModel->customer_order_id) ? $params['order_id'] :
                    $orderModel->customer_order_id,
                'oil_name'             => '电',
                'oil_type_name'        => '电',
                'trade_place'          => $orderExtModel->station_name,
                'trade_address'        => $orderExtModel->station_address,
                'oil_time'             => $orderModel->create_time->toDateTimeString(),
                'pcode'                => $orderModel->supplier_code,
                'orgcode'              => $orderModel->org_code,
                'province_code'        => $orderExtModel->province_code,
                'province_name'        => $orderExtModel->province_name,
                'city_code'            => $orderExtModel->city_code,
                'city_name'            => $orderExtModel->city_name,
                'open_check_password'  => 0,
                'card_password'        => '',
                'imgurl'               => '',
                'truck_no'             => $orderExtModel->truck_no,
                'drivername'           => $orderExtModel->driver_name,
                'drivertel'            => $orderModel->driver_phone,
                'rebate_grade'         => '',
                'xpcode_pay_price'     => $discountedPrice,
                'xpcode_pay_money'     => $params['discounted_money'],
                'truename'             => '系统',
                'remark'               => '',
                'trade_from'           => CardTradeConf::ELECTRICITY_ORDER,
                'real_oil_num'         => $params['num'],
                'mac_price'            => $price,
                'mac_amount'           => $params['money'],
                'price_id'             => '',
                'original_order_id'    => $orderModel->order_id,
                'document_type'        => CardTradeConf::$history_type_to_foss_mapping[HistoryModel::COST_ELECTRICITY_ORDER],
                'order_type'           => OrderModel::ORDER_SALE_TYPE_REFUEL,
                'ocr_truck_no_url'     => '',
                'pay_ext_info'         => [],
                'electric_service_fee' => $params['service_money'],
                'is_electric'          => 1,
            ]);
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($params['order_id'], [
                'order_status' => ElectricityOrderModel::PAY_SUCCESS,
                'pay_time'     => date('Y-m-d H:i:s'),
            ]);
            $this->electricityOrderExtRepository->updateElectricityOrderExtByOrderId($params['order_id'], [
                'foss_history_id' => $result['payment_id'],
                'gas_history_id'  => $orderModel->order_id,
                'updater'         => '系统',
            ]);
            $cardRepository = app(CardRepository::class);
            $cardInfo = $cardRepository->getOneCardByParams([
                'card_no' => (int)$orderModel->card_no,
            ], ['card_id', 'orgcode'], false);
            $cardId = $cardInfo->card_id ?? 0;
            $orgCode = $cardInfo->orgcode ?? '';
            if (empty($cardInfo)) {
                $cardInfo = $cardRepository->getCardInfo([
                    'vice_no' => (string)$orderModel->card_no
                ]);
                $cardId = $cardInfo->id ?? 0;
                $orgCode = $cardInfo->orgcode ?? '';
            }
            app(HistoryRepository::class)->insertHistory([
                'id'               => $orderModel->order_id,
                'serial_num'       => substr(app(RedisService::class)->makeSerialNum(), 0, 11),
                'pcode'            => $orderModel->supplier_code,
                'xpcode'           => CommonDefine::CARD_NUMBER_ONE,
                'gascode'          => $orderModel->org_code,
                'card_id'          => $cardId,
                'card_no'          => $orderModel->card_no,
                'unit'             => 1,
                'log_type'         => HistoryModel::COST_ELECTRICITY_ORDER,
                'sale_type'        => 11,
                'xpcode_sale_type' => 11,
                'data_type'        => 11,
                'truck_no'         => $orderExtModel->truck_no,
                'truename'         => $orderExtModel->driver_name,
                'creator'          => $orderExtModel->creator,
                'orgcode'          => $orgCode,
                'money'            => $params['money'],
                'pay_money'        => $params['money'],
                'xpcode_pay_money' => $params['discounted_money'],
                'oil_num'          => $params['num'],
                'provice_code'     => $orderExtModel->province_code,
                'city_code'        => $orderExtModel->city_code,
                'driver_name'      => $orderExtModel->driver_name,
                'driver_phone'     => $orderModel->driver_phone,
                'price'            => $price,
                'pay_price'        => $price,
                'xpcode_pay_price' => $discountedPrice,
                'support_type'     => 2,
                'service_price'    => round(bcdiv($params['service_money'], $params['num'], 3), 2),
                'station_id'       => $orderModel->station_id,
                'oil_time'         => $orderModel->create_time->toDateTimeString(),
                'createtime'       => date('Y-m-d H:i:s'),
                'updatetime'       => date('Y-m-d H:i:s'),
                'is_pay'           => 22,
                'ispay'            => 1,
                'stream_no'        => md5(Common::uuid()),
                'oil_starttime'    => $orderModel->create_time->toDateTimeString(),
                'card_classify'    => 2,
                'payment_no'       => $result['payment_id'],
            ]);
            if (empty($result)) {
                throw new Exception(
                    '添加交易流水失败,请重试!',
                    app(ErrorCodeService::class)->gotoPay(40403)
                );
            }
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            $this->electricityOrderRepository->updateElectricityOrderByOrderId($params['order_id'], [
                'order_status' => ElectricityOrderModel::PAY_FAIL,
            ]);
            $this->electricityOrderExtRepository->updateElectricityOrderExtByOrderId($params['order_id'], [
                'remark' => "code:" . $exception->getCode() . "|msg:" . $exception->getMessage(),
            ]);
            throw $exception;
        }
    }

    /**
     * @throws Throwable
     */
    public function orderStopElectricityForUser(int $orderId, $userId, string $userType)
    {
        $searchParams = [
            'order_id' => $orderId,
        ];
        if ($userType == 'driver') {
            $result = app(FossUser::class)->getUserAndElectricityCard(['uid' => $userId]);
            if (empty($result['user_info']) or !isset($result['user_info']['history_mobiles']) or
                count($result['user_info']['history_mobiles']) == 0) {
                throw new RuntimeException('', CommonError::USER_ERROR);
            }
            $searchParams['driver_phone'] = $result['user_info']['history_mobiles'];
        }
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderModel = $this->electricityOrderRepository->getOneElectricityOrderByParams($searchParams, [
                'order_id',
                'order_status',
                'supplier_code',
            ], true);
            if (empty($orderModel)) {
                throw new RuntimeException('', CommonError::BILL_NO_FOUND);
            }
            if (!in_array($orderModel->order_status, [
                ElectricityOrderModel::CHARGING,
                ElectricityOrderModel::STOPPING,
            ])) {
                throw new RuntimeException('', CommonError::ELECTRICITY_ORDER_STATUS_EXCEPTION);
            }
            if ($orderModel->order_status == ElectricityOrderModel::CHARGING) {
                $this->electricityOrderRepository->updateElectricityOrderByOrderId($orderId, [
                    'order_status' => ElectricityOrderModel::STOPPING,
                ]);
            }
            app(Adapter::class)->sendCreateOrderMsg([
                'message_type' => 'STOP_ELECTRICITY',
                'data'         => [
                    'order_id'      => $orderId,
                    'supplier_code' => $orderModel->supplier_code,
                ],
            ]);
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $exception) {
            DB::connection('mysql_gas')->rollBack();
            throw $exception;
        }
    }

    public function getElectricityOrderList(array $params): Collection
    {
        if ($params['_user_type'] == 'driver') {
            $result = app(FossUser::class)->getUserAndElectricityCard(['uid' => $params['_user_id']]);
            if (empty($result['user_info']) or !isset($result['user_info']['history_mobiles']) or
                count($result['user_info']['history_mobiles']) == 0) {
                throw new RuntimeException('', CommonError::USER_ERROR);
            }
            $params['driver_phone'] = empty($params['driver_phone']) ? [] : [$params['driver_phone']];
            $params['driver_phone'] = array_merge($result['user_info']['history_mobiles'], $params['driver_phone']);
        }
        $orderSearchParams = $orderExtSearchParams = $orderProgressSearchParams = [];
        if (empty($params['create_time_start']) and empty($params['create_time_end'])) {
            $orderSearchParams['ge_create_time'] = date('Y-m-d H:i:s', strtotime('-30 day'));
            $orderSearchParams['le_create_time'] = date('Y-m-d H:i:s');
        }
        if (!empty($params['create_time_start'])) {
            $orderSearchParams['ge_create_time'] = $params['create_time_start'];
        }
        if (!empty($params['create_time_end'])) {
            $orderSearchParams['le_create_time'] = $params['create_time_end'];
        }
        if (!empty($params['pay_time_start'])) {
            $orderSearchParams['ge_create_time'] = $params['pay_time_start'];
        }
        if (!empty($params['pay_time_end'])) {
            $orderSearchParams['le_create_time'] = $params['pay_time_end'];
        }
        if (!empty($params['order_status'])) {
            $orderSearchParams['order_status'] = $params['order_status'];
        }
        if (!empty($params['order_status_group'])) {
            switch ($params['order_status_group']) {
                case 2:
                    $orderSearchParams['order_status'] = [
                        ElectricityOrderModel::CHARGING,
                        ElectricityOrderModel::STOPPING,
                        ElectricityOrderModel::WAIT_PAY,
                    ];
                    break;
                case 3:
                    $orderSearchParams['order_status'] = [
                        ElectricityOrderModel::PAY_SUCCESS,
                        ElectricityOrderModel::ORDER_CANCEL,
                    ];
                    break;
            }
        }
        if (!empty($params['order_id'])) {
            $orderSearchParams['order_id'] = $params['order_id'];
        }
        if (!empty($params['driver_phone'])) {
            if (is_array($params['driver_phone'])) {
                $orderSearchParams['driver_phone'] = $params['driver_phone'];
            } else {
                $orderSearchParams['left_driver_phone'] = $params['driver_phone'];
            }
        }
        if (!empty($params['driver_name'])) {
            $orderExtSearchParams['left_driver_name'] = $params['driver_name'];
        }
        if (!empty($params['truck_no'])) {
            $orderExtSearchParams['left_truck_no'] = $params['truck_no'];
        }
        if (!empty($params['card_no'])) {
            $orderSearchParams['left_card_no'] = $params['card_no'];
        }
        if (!empty($params['supplier_code'])) {
            $orderSearchParams['supplier_code'] = $params['supplier_code'];
        }
        if (!empty($params['org_code'])) {
            $orderSearchParams['org_code'] = $params['org_code'];
        }
        if (!empty($params['station_id'])) {
            $orderSearchParams['station_id'] = $params['station_id'];
        }
        if (!empty($params['supplier_order_id'])) {
            $orderSearchParams['left_supplier_order_id'] = $params['supplier_order_id'];
        }
        if (!empty($params['third_connector_id'])) {
            $orderProgressSearchParams['left_third_connector_id'] = $params['third_connector_id'];
        }
        if (!empty($params['gas_history_id'])) {
            $orderExtSearchParams['left_gas_history_id'] = $params['gas_history_id'];
        }
        $page = empty($params['page']) ? 1 : $params['page'];
        $limit = empty($params['limit']) ? 10 : $params['limit'];
        if (!empty($params['page'])) {
            $page = $params['page'];
        }
        if (!empty($params['limit'])) {
            $limit = $params['limit'];
        }
        $orderSearchFields = $orderExtSearchFields = $orderProgressSearchFields = [];
        if ($params['_user_type'] == 'driver') {
            $orderSearchFields = [
                'order_id',
                'order_status',
                'pay_time',
                'create_time',
                'supplier_money',
                'customer_money',
            ];
            $orderExtSearchFields = [
                'station_name',
                'org_name',
                'connector_id',
            ];
            $orderProgressSearchFields = [
                'start_time',
                'end_time',
                'fetch_time',
                'total_money',
                'total_num',
            ];
        }
        if ($params['_user_type'] == 'employee') {
            $orderSearchFields = [
                'order_id',
                'order_status',
                'pay_time',
                'create_time',
                'supplier_money',
                'customer_money',
                'card_no',
                'driver_phone',
                'electricity_num',
            ];
            $orderExtSearchFields = [
                'gas_history_id',
                'station_name',
                'supplier_name',
                'org_name',
                'driver_name',
                'truck_no',
            ];
            $orderProgressSearchFields = [
                'start_time',
                'end_time',
                'fetch_time',
                'third_connector_id'
            ];
        }
        return $this->electricityOrderRepository->getElectricityOrderList(
            $orderSearchParams,
            $orderExtSearchParams,
            $orderProgressSearchParams,
            $orderSearchFields,
            $orderExtSearchFields,
            $orderProgressSearchFields,
            $page,
            $limit
        )->map(function ($item) use ($params) {
            $item['order_id_string'] = (string)$item['order_id'];
            if ($params['_user_type'] == 'driver') {
                $chargeTime = 0;
                if (in_array($item['order_status'], [
                    ElectricityOrderModel::WAIT_PAY,
                    ElectricityOrderModel::PAY_SUCCESS,
                    ElectricityOrderModel::PAY_FAIL,
                ])) {
                    $chargeTime = bcsub(
                        strtotime($item['end_time'] ?? 0),
                        strtotime($item['start_time'] ?? 0),
                        2
                    );
                }
                if (in_array($item['order_status'], [
                    ElectricityOrderModel::CHARGING,
                    ElectricityOrderModel::STOPPING,
                ])) {
                    $chargeTime = bcsub(
                        strtotime($item['fetch_time'] ?? 0),
                        strtotime($item['start_time'] ?? 0),
                        2
                    );
                }
                $item['charge_time'] = round(bcdiv(bcmod($chargeTime, 3600, 6), 60, 1)) . '分钟';
                if (bccomp($chargeTime, 3600, 2) > 0) {
                    $hours = bcdiv($chargeTime, 3600) . '小时';
                    $item['charge_time'] = $hours . $item['chargeTime'];
                }
            }
            return $item;
        });
    }

    public function getElectricityOrderProgress(array $params): ?ElectricityOrderModel
    {
        $result = app(FossUser::class)->getUserAndElectricityCard(['uid' => $params['_user_id']]);
        if (empty($result['user_info']) or !isset($result['user_info']['history_mobiles']) or
            count($result['user_info']['history_mobiles']) == 0) {
            throw new RuntimeException('', CommonError::USER_ERROR);
        }
        $params['driver_phone'] = empty($params['driver_phone']) ? [] : $params['driver_phone'];
        $params['driver_phone'] = array_merge($result['user_info']['history_mobiles'], $params['driver_phone']);
        $queryResult = $this->electricityOrderRepository->getElectricityOrderList(
            [
                'order_id'     => $params['order_id'],
                'driver_phone' => $params['driver_phone'],
                'order_status' => [
                    ElectricityOrderModel::WAIT_START,
                    ElectricityOrderModel::WAIT_PAY,
                    ElectricityOrderModel::CHARGING,
                    ElectricityOrderModel::STOPPING,
                    ElectricityOrderModel::PAY_SUCCESS,
                ],
            ],
            [],
            [],
            [
                'order_id',
                'order_status',
                'order_money',
                'card_no'
            ],
            [
                'station_name',
                'connector_id',
                'stop_code',
                'truck_no',
            ],
            [
                'start_time',
                'end_time',
                'fetch_time',
                'total_money',
                'total_num',
                'current_a',
                'voltage_a',
                'third_connector_id',
                'remainder_num',
            ],
            1,
            1
        )->map(function ($item) {
            $item['card_no'] = substr($item['card_no'], -4);
            $item['order_id_string'] = (string)$item['order_id'];
            $item['order_balance'] = bcsub($item['order_money'], $item['total_money'], 2);
            $item['power'] = bcmul($item['current_a'], $item['voltage_a'], 2);
            $chargeTime = 0;
            if (in_array($item['order_status'], [
                ElectricityOrderModel::WAIT_PAY,
                ElectricityOrderModel::PAY_SUCCESS,
                ElectricityOrderModel::PAY_FAIL,
            ])) {
                $chargeTime = max(
                    bcsub(
                        strtotime($item['end_time'] ?? 0),
                        strtotime($item['start_time'] ?? 0),
                        2
                    ),
                    0.00
                );
            }
            if (in_array($item['order_status'], [
                ElectricityOrderModel::CHARGING,
                ElectricityOrderModel::STOPPING,
            ])) {
                $chargeTime = max(
                    bcsub(
                        strtotime($item['fetch_time'] ?? 0),
                        strtotime($item['start_time'] ?? 0),
                        2
                    ),
                    0.00
                );
            }
            $item['charge_time'] = round(
                                       bcdiv(
                                           bcmod($chargeTime, 3600, 6),
                                           60,
                                           1
                                       )
                                   ) . '分钟';
            if (bccomp($chargeTime, 3600, 2) > 0) {
                $hours = bcdiv($chargeTime, 3600) . '小时';
                $item['charge_time'] = $hours . $item['chargeTime'];
            }
            return $item;
        });
        if ($queryResult) {
            return $queryResult->first();
        }
        return null;
    }

    public function getElectricityOrderInfo(array $params): ?ElectricityOrderModel
    {
        if ($params['_user_type'] == 'driver') {
            $result = app(FossUser::class)->getUserAndElectricityCard(['uid' => $params['_user_id']]);
            if (empty($result['user_info']) or !isset($result['user_info']['history_mobiles']) or
                count($result['user_info']['history_mobiles']) == 0) {
                throw new RuntimeException('', CommonError::USER_ERROR);
            }
            $params['driver_phone'] = empty($params['driver_phone']) ? [] : $params['driver_phone'];
            $params['driver_phone'] = array_merge($result['user_info']['history_mobiles'], $params['driver_phone']);
        }
        $orderSearchParams = [];
        if (!empty($params['order_id'])) {
            $orderSearchParams['order_id'] = $params['order_id'];
        }
        if (!empty($params['driver_phone'])) {
            $orderSearchParams['driver_phone'] = $params['driver_phone'];
        }
        $orderSearchFields = $orderExtSearchFields = $orderProgressSearchFields = $orderDetailSearchFields = [];
        if ($params['_user_type'] == 'driver') {
            $orderSearchFields = [
                'order_id',
                'order_status',
                'pay_time',
                'customer_money',
                'electricity_num',
                'customer_electricity_money',
                'customer_service_money',
                'driver_phone',
                'card_no',
            ];
            $orderExtSearchFields = [
                'station_name',
                'org_name',
                'connector_id',
                'driver_name',
                'truck_no',
            ];
            $orderProgressSearchFields = [
                'start_time',
                'end_time',
            ];
        }
        if ($params['_user_type'] == 'employee') {
            $orderSearchFields = [
                'order_id',
                'order_status',
                'supplier_order_id',
                'card_no',
                'driver_phone',
                'create_time',
                'order_money',
                'electricity_money',
                'price',
                'service_money',
                'money',
                'customer_electricity_money',
                'customer_price',
                'customer_service_money',
                'customer_money',
                'pay_electricity_money',
                'pay_price',
                'pay_service_money',
                'pay_money',
                'supplier_electricity_money',
                'supplier_price',
                'supplier_service_money',
                'supplier_money',
            ];
            $orderExtSearchFields = [
                'station_code',
                'station_name',
                'org_name',
                'supplier_name',
                'driver_name',
                'truck_no',
                'stop_code',
            ];
            $orderProgressSearchFields = [
                'start_time',
                'end_time',
                'fetch_time',
                'expected_end_time',
                'third_connector_id',
                'connector_status',
                'current_a',
                'current_b',
                'current_c',
                'voltage_a',
                'voltage_b',
                'voltage_c',
                'total_num',
                'total_electricity_money',
                'total_service_money',
                'total_money',
                'price',
                'remainder_num',
                'end_desc',
            ];
            $orderDetailSearchFields = [
                'start_time',
                'end_time',
                'price',
                'service_price',
                'total_num',
                'total_electricity_money',
                'total_service_money',
                'total_money',
            ];
        }
        $queryResult = $this->electricityOrderRepository->getElectricityOrderInfo(
            $orderSearchParams,
            [],
            [],
            $orderSearchFields,
            $orderExtSearchFields,
            $orderProgressSearchFields,
            $orderDetailSearchFields
        );
        if ($queryResult) {
            return $queryResult->map(function ($item) use ($params) {
                $item['order_id_string'] = (string)$item['order_id'];
                if ($params['_user_type'] == 'driver') {
                    $chargeTime = 0;
                    if (in_array($item['order_status'], [
                        ElectricityOrderModel::WAIT_PAY,
                        ElectricityOrderModel::PAY_SUCCESS,
                        ElectricityOrderModel::PAY_FAIL,
                    ])) {
                        $chargeTime = max(
                            bcsub(
                                strtotime($item['end_time'] ?? 0),
                                strtotime($item['start_time'] ?? 0),
                                2
                            ),
                            0.00
                        );
                    }
                    if (in_array($item['order_status'], [
                        ElectricityOrderModel::CHARGING,
                        ElectricityOrderModel::STOPPING,
                    ])) {
                        $chargeTime = max(
                            bcsub(
                                strtotime($item['fetch_time'] ?? 0),
                                strtotime($item['start_time'] ?? 0),
                                2
                            ),
                            0.00
                        );
                    }
                    $item['charge_time'] = round(
                                               bcdiv(
                                                   bcmod($chargeTime, 3600, 6),
                                                   60,
                                                   1
                                               )
                                           ) . '分钟';
                    if (bccomp($chargeTime, 3600, 2) > 0) {
                        $hours = bcdiv($chargeTime, 3600) . '小时';
                        $item['charge_time'] = $hours . $item['chargeTime'];
                    }
                }
                return $item;
            })->first();
        }
        return null;
    }

    public function getElectricityOrderDetail(array $params): ?Collection
    {
        return $this->electricityOrderRepository->getElectricityOrderDetail(
            [
                'order_id' => $params['order_id'],
            ],
            ['order_id'],
            [
                'start_time',
                'end_time',
                'total_num',
                'total_electricity_money',
                'total_service_money',
                'total_money',
            ]
        );
    }
}