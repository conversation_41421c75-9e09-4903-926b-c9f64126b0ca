<?php

namespace App\Repositories;

use App\Models\ElectricityOrderDetailModel;
use App\Models\ElectricityOrderExtModel;
use App\Models\ElectricityOrderModel;
use App\Models\ElectricityOrderProgressModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

class ElectricityOrderRepository
{
    protected $electricityOrderModel;

    public function __construct
    (
        ElectricityOrderModel $electricityOrderModel
    ) {
        $this->electricityOrderModel = $electricityOrderModel;
    }

    /**
     * @param array $params
     * @param array $select
     * @param $lock
     * @return ElectricityOrderModel|Model|Builder|mixed|object|null
     */
    public function getOneElectricityOrderByParams(array $params, array $select = ['*'], $lock = false)
    {
        return $this->electricityOrderModel::getOneElectricityOrderByParams($params, $select, $lock);
    }

    public function insertElectricityOrder(array $params): bool
    {
        return $this->electricityOrderModel::insertElectricityOrder($params);
    }

    public function updateElectricityOrderByOrderId(int $orderId, array $updateArray)
    {
        return $this->electricityOrderModel::updateElectricityOrderByOrderId($orderId, $updateArray);
    }

    public function getElectricityOrderList(
        array $orderSearchParams,
        array $orderExtSearchParams,
        array $orderProgressSearchParams,
        array $orderQueryFields,
        array $orderExtQueryFields,
        array $orderProgressQueryFields,
        int $page,
        int $limit
    ): ?Collection {
        $electricityOrderModel = $this->electricityOrderModel;
        $electricityOrderExtModel = new ElectricityOrderExtModel();
        $electricityOrderProgressModel = new ElectricityOrderProgressModel();
        $this->electricityOrderModel = $this->electricityOrderModel
            ->leftJoin(
                $electricityOrderExtModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderExtModel->getTable() . '.order_id'
            )->leftJoin(
                $electricityOrderProgressModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderProgressModel->getTable() . '.order_id'
            );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderSearchParams,
            false,
            $electricityOrderModel->getTable()
        );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderExtSearchParams,
            false,
            $electricityOrderExtModel->getTable()
        );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderProgressSearchParams,
            false,
            $electricityOrderProgressModel->getTable()
        );
        if (empty($orderQueryFields) and
            empty($orderExtQueryFields) and
            empty($orderProgressQueryFields) and
            empty($orderDetailQueryFields)) {
            return null;
        }
        if (!empty($orderQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderModel->getTable() . "." . implode(
                    ',' . $electricityOrderModel->getTable() . ".",
                    $orderQueryFields
                )
            );
        }
        if (!empty($orderExtQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderExtModel->getTable() . "." . implode(
                    ',' . $electricityOrderExtModel->getTable() . ".",
                    $orderExtQueryFields
                )
            );
        }
        if (!empty($orderProgressQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderProgressModel->getTable() . "." . implode(
                    ',' . $electricityOrderProgressModel->getTable() . ".",
                    $orderProgressQueryFields
                )
            );
        }
        return $this->electricityOrderModel->orderBy('create_time', 'desc')->offset(
            ($page - 1) * $limit
        )->limit($limit)->get()->map(function ($item) {
            if (isset($item->order_status)) {
                $item->order_status_name = ElectricityOrderModel::$orderStatus[$item->order_status] ?? '';
            }
            return $item;
        });
    }

    public function getElectricityOrderInfo(
        array $orderSearchParams,
        array $orderExtSearchParams,
        array $orderProgressSearchParams,
        array $orderQueryFields,
        array $orderExtQueryFields,
        array $orderProgressQueryFields,
        array $orderDetailQueryFields
    ): ?Collection {
        $electricityOrderModel = $this->electricityOrderModel;
        $electricityOrderExtModel = new ElectricityOrderExtModel();
        $electricityOrderProgressModel = new ElectricityOrderProgressModel();
        $electricityOrderDetailModel = new ElectricityOrderDetailModel();
        $this->electricityOrderModel = $this->electricityOrderModel
            ->leftJoin(
                $electricityOrderExtModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderExtModel->getTable() . '.order_id'
            )->leftJoin(
                $electricityOrderProgressModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderProgressModel->getTable() . '.order_id'
            )->leftJoin(
                $electricityOrderDetailModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderDetailModel->getTable() . '.order_id'
            );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderSearchParams,
            false,
            $electricityOrderModel->getTable()
        );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderExtSearchParams,
            false,
            $electricityOrderExtModel->getTable()
        );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderProgressSearchParams,
            false,
            $electricityOrderProgressModel->getTable()
        );
        if (empty($orderQueryFields) and
            empty($orderExtQueryFields) and
            empty($orderProgressQueryFields) and
            empty($orderDetailQueryFields)) {
            return null;
        }
        if (!empty($orderQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderModel->getTable() . "." . implode(
                    ',' . $electricityOrderModel->getTable() . ".",
                    $orderQueryFields
                )
            );
        }
        if (!empty($orderExtQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderExtModel->getTable() . "." . implode(
                    ',' . $electricityOrderExtModel->getTable() . ".",
                    $orderExtQueryFields
                )
            );
        }
        if (!empty($orderProgressQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderProgressModel->getTable() . "." . implode(
                    ',' . $electricityOrderProgressModel->getTable() . ".",
                    $orderProgressQueryFields
                )
            );
        }
        if (!empty($orderDetailQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderDetailModel->getTable() . "." . implode(
                    ',' . $electricityOrderDetailModel->getTable() . ".",
                    $orderDetailQueryFields
                )
            );
        }
        return $this->electricityOrderModel->limit(1)->get()->map(function ($item) {
            if (isset($item->order_status)) {
                $item->order_status_name = ElectricityOrderModel::$orderStatus[$item->order_status] ?? '';
            }
            if (isset($item->connector_status)) {
                $item->connector_status_name = ElectricityOrderProgressModel::$connectorStatusDesc[$item->connector_status] ?? '';
            }
            return $item;
        });
    }

    public function getElectricityOrderDetail(
        array $orderSearchParams,
        array $orderQueryFields,
        array $orderDetailQueryFields
    ): ?Collection {
        $electricityOrderModel = $this->electricityOrderModel;
        $electricityOrderDetailModel = new ElectricityOrderDetailModel();
        $this->electricityOrderModel = $this->electricityOrderModel
            ->leftJoin(
                $electricityOrderDetailModel->getTable(),
                $electricityOrderModel->getTable() . '.order_id',
                '=',
                $electricityOrderDetailModel->getTable() . '.order_id'
            );
        $this->electricityOrderModel = $electricityOrderModel::scopeWithCondition(
            $this->electricityOrderModel,
            $orderSearchParams,
            false,
            $electricityOrderModel->getTable()
        );
        if (empty($orderQueryFields) and
            empty($orderExtQueryFields) and
            empty($orderProgressQueryFields) and
            empty($orderDetailQueryFields)) {
            return null;
        }
        if (!empty($orderQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderModel->getTable() . "." . implode(
                    ',' . $electricityOrderModel->getTable() . ".",
                    $orderQueryFields
                )
            );
        }
        if (!empty($orderDetailQueryFields)) {
            $this->electricityOrderModel = $this->electricityOrderModel->selectRaw(
                $electricityOrderDetailModel->getTable() . "." . implode(
                    ',' . $electricityOrderDetailModel->getTable() . ".",
                    $orderDetailQueryFields
                )
            );
        }
        return $this->electricityOrderModel->orderBy(
            'start_time',
            'asc'
        )->get()->map(function ($item) {
            if (!empty($item->start_time)) {
                $item->start_time = date('H:i', strtotime($item->start_time));
            }
            if (!empty($item->end_time)) {
                $item->end_time = date('H:i', strtotime($item->end_time));
            }
            return $item;
        });
    }
}
