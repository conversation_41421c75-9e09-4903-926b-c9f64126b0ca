<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\StationTankModel;

class StationTankRepository extends \App\Repositories\EloquentRepository
{
    protected $stationTankModel;
    public function __construct(StationTankModel $stationTankModel)
    {
        $this->stationTankModel = $stationTankModel;
    }

    //查询
    public function getList(array $condition)
    {
        $data = $this->stationTankModel->select("oil_name","oil_type","oil_level")->withCondition($condition)->groupBy("oil_name","oil_type","oil_level")->get();
        return $data;
    }

    /**
     * 获取1条罐信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneTankByParams($whereParams, $select = ['*'], $toArray = true)
    {
        return $this->stationTankModel->getOneTankByParams($whereParams, $select, $toArray);
    }
}