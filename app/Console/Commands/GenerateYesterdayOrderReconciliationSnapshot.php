<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/7
 * Time: 3:18 PM
 */

namespace App\Console\Commands;

use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CouponDefine;
use App\Models\AdditionalOrderRecord;
use App\Models\CouponsModel;
use App\Models\DayOrderReconciliationSnapshotModel;
use App\Models\OrderExtModel;
use App\Models\OrderModel;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\NewOrderRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Library\Monitor\Falcon;
use Throwable;

class GenerateYesterdayOrderReconciliationSnapshot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:yesterday_order_reconciliation_snapshot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成昨日订单对账数据快照';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $orgCodes = explode(',', env('GENERATE_YESTERDAY_ORDER_RECONCILIATION_SNAPSHOT'));
        if (empty($orgCodes)) {
            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成客户名单获取失败");
            return;
        }
        foreach ($orgCodes as $orgCode) {
            try {
                Log::info("{$orgCode}生成订单昨日对账数据快照开始");
                $statisticsByTree = in_array(
                    $orgCode,
                    explode(',', env('GENERATE_YESTERDAY_ORDER_RECONCILIATION_SNAPSHOT_BY_TREE'))
                );
                $orgCodeCondition = $statisticsByTree ? $orgCode . '%' : $orgCode;
                $couponOrderData = OrderModel::selectRaw("*")
                                             ->from((new OrderModel())->getTable() . ' as a')
                                             ->leftJoin(
                                                 (new OrderExtModel())->getTable() . ' as b',
                                                 'a.order_id',
                                                 '=',
                                                 'b.order_id'
                                             )
                                             ->where(
                                                 'a.org_code',
                                                 $statisticsByTree ? 'like' : '=',
                                                 $orgCodeCondition
                                             )
                                             ->whereNotIn('trade_mode', [
                                                 CardTradeConf::TRADE_MODE_ZYZ_ZYS_BL,
                                                 CardTradeConf::TRADE_MODE_ZYZ_XYS_BL,
                                             ])
                                             ->whereIn('a.order_status', [
                                                 OrderModel::SUCCESS_PAY,
                                                 OrderModel::REFUND,
                                             ])
                                             ->whereIn('a.order_id', function ($query) {
                                                 $query->select('c.order_id')
                                                       ->from((new CouponsModel())->getTable() . ' as c')
                                                       ->where('tripartite_status', '=', CouponDefine::CHARGE_OFFED)
                                                       ->where(
                                                           'charge_off_time',
                                                           '>=',
                                                           date(
                                                               'Y-m-d 00:00:00',
                                                               strtotime('-1 day')
                                                           )
                                                       )
                                                       ->where('charge_off_time', '<', date('Y-m-d 00:00:00'));
                                             })
                                             ->get()->toArray();
                $orderData = array_merge(
                    OrderModel::selectRaw("*")
                              ->from((new OrderModel())->getTable() . ' as a')
                              ->leftJoin(
                                  (new OrderExtModel())->getTable() . ' as b',
                                  'a.order_id',
                                  '=',
                                  'b.order_id'
                              )
                              ->where(
                                  'a.org_code',
                                  $statisticsByTree ? 'like' : '=',
                                  $orgCodeCondition
                              )
                              ->whereNotIn('trade_mode', [
                                  CardTradeConf::TRADE_MODE_ZYZ_ZYS_BL,
                                  CardTradeConf::TRADE_MODE_ZYZ_XYS_BL,
                              ])
                              ->whereIn('a.order_status', [
                                  OrderModel::SUCCESS_PAY,
                                  OrderModel::REFUND,
                              ])
                              ->where('pay_time', '>=', date('Y-m-d 00:00:00', strtotime('-1 day')))
                              ->where('pay_time', '<', date('Y-m-d 00:00:00'))
                              ->where('b.g7_coupon_flag', null)
                              ->get()->toArray(),
                    $couponOrderData
                );
                $dayOrderReconciliationSnapshotData = [];
                foreach ($orderData as $v) {
                    if (!isset(
                        $v['order_status'], $v['order_id'], $v['trade_mode'], $v['oil_num'], $v['oil_money'], $v['mac_amount']
                    )) {
                        Log::error("订单数据格式异常", $v);
                        Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,订单数据格式异常");
                        return;
                    }
                    $reconciliationDay = date(
                        'Y-m-d',
                        strtotime($v['pay_time'])
                    );
                    $uniqKey = ($statisticsByTree ? $orgCode : $v['org_code']) . '_' . $reconciliationDay;
                    $dayOrderReconciliationSnapshotData = $this->getOrderReconciliationSnapshotData(
                        $dayOrderReconciliationSnapshotData,
                        $uniqKey,
                        $reconciliationDay,
                        $statisticsByTree ? $orgCode : $v['org_code']
                    );
                    if ($v['order_status'] != OrderModel::SUCCESS_PAY) {
                        continue;
                    }
                    $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'] = bcadd(
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'],
                        $v['oil_money'],
                        2
                    );
                    $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'] = bcadd(
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'],
                        $v['oil_num'],
                        2
                    );
                    $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'] = bcadd(
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'],
                        $v['mac_amount'],
                        2
                    );
                }
                $historyApproveOrderQueryParams = [
                    'gt_pay_time'  => date('Y-m-d 00:00:00', strtotime('-1 day')),
                    'lt_pay_time'  => date('Y-m-d 07:00:00'),
                    'org_code'     => $orgCode,
                    'trade_mode'   => [
                        CardTradeConf::TRADE_MODE_ZYZ_ZYS_BL,
                    ],
                    'order_status' => [
                        OrderModel::SUCCESS_PAY,
                    ],
                ];
                if ($statisticsByTree) {
                    unset($historyApproveOrderQueryParams['org_code']);
                    $historyApproveOrderQueryParams['left_org_code'] = $orgCode . '%';
                }
                $historyApproveOrderData = app(NewOrderRepository::class)->getBatchOrderByParams($historyApproveOrderQueryParams);
                if (!empty($historyApproveOrderData)) {
                    $historyApproveOrderIdToDataMapping = [];
                    foreach ($historyApproveOrderData as $v) {
                        if (!isset($v['order_id'], $v['oil_num'], $v['oil_money'], $v['ext'], $v['ext']['mac_amount'])) {
                            Log::error("订单数据格式异常", $v);
                            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,订单数据格式异常");
                            return;
                        }
                        $historyApproveOrderIdToDataMapping[$v['order_id']] = $v;
                    }
                    $historyApproveData = app(HistoryApproveRepository::class)->getBatchApproveInfoByParams([
                        'history_id' => array_keys($historyApproveOrderIdToDataMapping)
                    ], ['*'], true);
                    foreach ($historyApproveData as $v) {
                        if (!isset($v['approve_mirror'], $v['history_id'])) {
                            Log::error("异常工单数据格式异常", $v);
                            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,异常工单数据格式异常");
                            return;
                        }
                        $approveMirror = json_decode($v['approve_mirror'], true) ?? [];
                        if (!isset($approveMirror['oil_time'])) {
                            Log::error("异常工单镜像数据格式异常", $v);
                            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,异常工单镜像数据格式异常");
                            return;
                        }
                        if (strtotime($approveMirror['oil_time']) < strtotime(
                                date(
                                    'Y-m-d 00:00:00',
                                    strtotime('-1 day')
                                )
                            ) or strtotime($approveMirror['oil_time']) >= strtotime(
                                date(
                                    'Y-m-d 00:00:00'
                                )
                            )) {
                            continue;
                        }
                        $reconciliationDay = date(
                            'Y-m-d',
                            strtotime($approveMirror['oil_time'])
                        );
                        $uniqKey = ($statisticsByTree ? $orgCode : $historyApproveOrderIdToDataMapping[$v['history_id']]['org_code']) . '_' . $reconciliationDay;
                        $dayOrderReconciliationSnapshotData = $this->getOrderReconciliationSnapshotData(
                            $dayOrderReconciliationSnapshotData,
                            $uniqKey,
                            $reconciliationDay,
                            $statisticsByTree ? $orgCode : $historyApproveOrderIdToDataMapping[$v['history_id']]['org_code']
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'],
                            $historyApproveOrderIdToDataMapping[$v['history_id']]['oil_money'],
                            2
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'],
                            $historyApproveOrderIdToDataMapping[$v['history_id']]['oil_num'],
                            2
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'],
                            $historyApproveOrderIdToDataMapping[$v['history_id']]['ext']['mac_amount'],
                            2
                        );
                    }
                }
                $additionalOrderQueryParams = [
                    'gt_pay_time'   => date('Y-m-d 00:00:00', strtotime('-1 day')),
                    'lt_pay_time'   => date('Y-m-d 07:00:00'),
                    'org_code' => $orgCode,
                    'trade_mode'    => [
                        CardTradeConf::TRADE_MODE_ZYZ_XYS_BL,
                    ],
                    'order_status'  => [
                        OrderModel::SUCCESS_PAY,
                    ],
                ];
                if ($statisticsByTree) {
                    unset($additionalOrderQueryParams['org_code']);
                    $additionalOrderQueryParams['left_org_code'] = $orgCode . '%';
                }
                $additionalOrderData = app(NewOrderRepository::class)->getBatchOrderByParams($additionalOrderQueryParams);
                if (!empty($additionalOrderData)) {
                    $additionalOrderIdToDataMapping = [];
                    foreach ($additionalOrderData as $v) {
                        if (!isset($v['order_id'], $v['oil_num'], $v['oil_money'], $v['ext'], $v['ext']['mac_amount'])) {
                            Log::error("订单数据格式异常", $v);
                            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,订单数据格式异常");
                            return;
                        }
                        $additionalOrderIdToDataMapping[$v['order_id']] = $v;
                    }
                    $additionalOrderApproveData = AdditionalOrderRecord::select([
                        'record.oil_time',
                        'item.order_id',
                    ])->from('gas_additional_order_record as record')
                                                                       ->leftJoin(
                                                                           'gas_additional_order_item as item',
                                                                           'record.id',
                                                                           '=',
                                                                           'item.record_id'
                                                                       )->whereIn(
                            'item.order_id',
                            array_keys($additionalOrderIdToDataMapping)
                        )->get()->toArray();
                    foreach ($additionalOrderApproveData as $v) {
                        if (!isset($v['oil_time'], $v['order_id'])) {
                            Log::error("后台客户下单数据格式异常", $v);
                            Falcon::feishu(env('FEISHU_TOKEN'), "订单对账快照数据生成失败,后台客户下单数据格式异常");
                            return;
                        }
                        if (strtotime($v['oil_time']) < strtotime(
                                date(
                                    'Y-m-d 00:00:00',
                                    strtotime('-1 day')
                                )
                            ) or strtotime($v['oil_time']) >= strtotime(
                                date(
                                    'Y-m-d 00:00:00'
                                )
                            )) {
                            continue;
                        }
                        $reconciliationDay = date(
                            'Y-m-d',
                            strtotime($v['oil_time'])
                        );
                        $uniqKey = ($statisticsByTree ? $orgCode : $additionalOrderIdToDataMapping[$v['order_id']]['org_code']) . '_' . $reconciliationDay;
                        $dayOrderReconciliationSnapshotData = $this->getOrderReconciliationSnapshotData(
                            $dayOrderReconciliationSnapshotData,
                            $uniqKey,
                            $reconciliationDay,
                            $statisticsByTree ? $orgCode : $additionalOrderIdToDataMapping[$v['order_id']]['org_code']
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'],
                            $additionalOrderIdToDataMapping[$v['order_id']]['oil_money'],
                            2
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'],
                            $additionalOrderIdToDataMapping[$v['order_id']]['oil_num'],
                            2
                        );
                        $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'] = bcadd(
                            $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'],
                            $additionalOrderIdToDataMapping[$v['order_id']]['ext']['mac_amount'],
                            2
                        );
                    }
                }
                if (!empty($dayOrderReconciliationSnapshotData)) {
                    DayOrderReconciliationSnapshotModel::insert($dayOrderReconciliationSnapshotData);
                }
                Log::info("{$orgCode}生成订单昨日对账数据快照完成");
            } catch (Throwable $throwable) {
                Log::error("{$orgCode}生成订单昨日对账数据快照发生异常", [
                    'exception' => [
                        'message' => $throwable->getMessage(),
                        'code'    => $throwable->getCode(),
                        'file'    => $throwable->getFile(),
                        'line'    => $throwable->getLine(),
                    ]
                ]);
            }
        }
    }

    /**
     * @param array $dayOrderReconciliationSnapshotData
     * @param string $uniqKey
     * @param $reconciliationDay
     * @param $org_code
     * @return array
     */
    public function getOrderReconciliationSnapshotData(
        array $dayOrderReconciliationSnapshotData,
        string $uniqKey,
        $reconciliationDay,
        $org_code
    ): array {
        if (!isset(
            $dayOrderReconciliationSnapshotData[$uniqKey]
        )) {
            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_num'] = 0;
            $dayOrderReconciliationSnapshotData[$uniqKey]['total_oil_money'] = 0;
            $dayOrderReconciliationSnapshotData[$uniqKey]['total_mac_money'] = 0;
            $dayOrderReconciliationSnapshotData[$uniqKey]['total_num'] = 0;
            $dayOrderReconciliationSnapshotData[$uniqKey]['reconciliation_day'] = $reconciliationDay;
            $dayOrderReconciliationSnapshotData[$uniqKey]['org_code'] = $org_code;
        }
        $dayOrderReconciliationSnapshotData[$uniqKey]['total_num'] += 1;
        return $dayOrderReconciliationSnapshotData;
    }
}