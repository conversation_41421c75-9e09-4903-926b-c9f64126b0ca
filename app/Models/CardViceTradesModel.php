<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class CardViceTradesModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_card_vice_trades';

    protected $guarded = ["id"];

    protected $fillable = ['id','api_id','vice_no',"oil_com","truck_no","cancel_sn","trade_time","oil_name","trade_money","service_money","total_money",
        "trade_num","trade_place","trade_price"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function org()
    {
        return $this->belongsTo(OrgModel::class, 'gascode', 'orgcode');
    }

    public function tradeExt()
    {
        return $this->hasOne(CardViceTradesExtModel::class, 'trades_id', 'id');
    }

}