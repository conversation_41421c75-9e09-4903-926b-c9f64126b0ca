<?php


namespace App\Repositories;


use App\Models\TripartiteCouponModel;

class TripartiteCouponRepository extends EloquentRepository
{

    protected $tripartiteCouponModel;

    public function __construct
    (
        TripartiteCouponModel $tripartiteCouponModel
    )
    {
        $this->tripartiteCouponModel = $tripartiteCouponModel;
    }

    public function updateOrCreate($attributes, $data)
    {
        return $this->tripartiteCouponModel->updateOrCreate($attributes, $data);
    }

    public function update($where, $data)
    {
        $this->tripartiteCouponModel->Filter($where)->update($data);
    }
}