server {
    listen 80 default;
    server_name _;

    access_log /dev/stdout json_full;
    error_log /dev/stderr error;

    root /data/web/public;
    index  index.html index.htm index.php;

    location ~ ^/fpm-ping {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php-fpm.sock;
        fastcgi_param SCRIPT_FILENAME  $document_root$fastcgi_script_name;
    }

    location ~ ^/fpm-status {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php-fpm.sock;
        fastcgi_param SCRIPT_FILENAME  $document_root$fastcgi_script_name;
    }

    location / {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';
        set $request_start_time $msec;

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        root   /data/web/public;
        index  index.html index.htm index.php;
        try_files $uri $uri/ /index.php?$args;
    }

     location ^~ /order/ {
        proxy_set_header   Host             $host;
        rewrite ^/order/(.*)$ /$1 break;
        proxy_pass  http://127.0.0.1/;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, token';
        add_header 'Access-Control-Allow-Origin' '*';
     }

    location /admin {
        add_header Cache-Control no-cache;
        add_header Pragma no-cache;
        add_header Expires 0;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,DUCONTEXT';

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        rewrite  "^/(.*)$"  https://api-gas.huoyunren.com/apptools/ break;
        # alias   /data/web/resources/static/pro;
        index  index.html index.htm index.php;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php-fpm.sock;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }
}