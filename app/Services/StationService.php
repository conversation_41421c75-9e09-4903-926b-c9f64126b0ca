<?php

/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Services;

use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Library\GoEasySDK\Config;
use App\Library\Helper\Common;
use App\Library\SocketIo;
use App\Repositories\CardRepository;
use App\Repositories\CardViceBillRepository;
use App\Repositories\CardViceTradesRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationGunRepository;
use App\Repositories\StationLimitConfigRepository;
use App\Repositories\StationPayQrcodesRepository;
use App\Repositories\StationRepository;
use App\Repositories\StationRuleRepository;
use App\Repositories\StationTankRepository;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use App\Servitization\Gas;
use Carbon\Carbon;

class StationService
{
    private $stationRepository;
    private $stationGunReponsitory;
    private $stationTankReponsitory;
    private $dictReponsitory;
    private $cardReponsitory;
    private $cardViceTradesReponsitory;
    private $stationPayQrcodesReponsitory;
    private $orgReponsitory;
    private $stationRuleReponsitory;
    private $historyReponsitory;
    private $cardViceBillReponsitory;
    private $cardService;
    private $newOrderRepository;
    private $messageService;
    private $limitRepository;

    public function __construct()
    {
        $this->stationRepository = app(StationRepository::class);
        $this->stationGunReponsitory = app(StationGunRepository::class);
        $this->stationTankReponsitory = app(StationTankRepository::class);
        $this->dictReponsitory = app(DictRepository::class);
        $this->cardReponsitory = app(CardRepository::class);
        $this->cardViceTradesReponsitory = app(CardViceTradesRepository::class);
        $this->stationPayQrcodesReponsitory = app(StationPayQrcodesRepository::class);
        $this->orgReponsitory = app(OrgRepository::class);
        $this->stationRuleReponsitory = app(StationRuleRepository::class);
        $this->historyReponsitory = app(HistoryRepository::class);
        $this->cardViceBillReponsitory = app(CardViceBillRepository::class);
        $this->cardService = app(CardService::class);
        $this->newOrderRepository = app(NewOrderRepository::class);
        $this->messageService = app(MessageService::class);
        $this->limitRepository = app(StationLimitConfigRepository::class);
    }

    public function stationExtInfo(array $params)
    {
        $result = [];
        $stationInfo = $this->getStationInfo($params);
        if(empty($stationInfo)){
            throw new \RuntimeException("",CommonError::STATION_OFF);
        }
        $result['stationInfo'] = $stationInfo;
        $gunList = $this->getGunList($params);
        $result['gunList'] = [];
        if(count($gunList) > 0) {
            $result['gunList'] = $gunList->toArray();
        }
        $dictMap = [];
        $dict = $this->getDict();
        if(count($dict) > 0){
            foreach ($dict as $_subDict){
                $dictMap[$_subDict->dict_type][$_subDict->id] = $_subDict->dict_data;
            }
        }
        $result['oilType'] = [];
        $tankList = $this->getTankList($params);
        if(count($tankList) > 0) {
            $last = [];
            foreach ($tankList->toArray() as $_key => $_item) {
                $last_key = $_item['oil_name'];
                $last[$last_key]['id'] = $_item['oil_name'];
                $last[$last_key]['name'] = isset($dictMap["oil_name"][$_item['oil_name']]) ? $dictMap["oil_name"][$_item['oil_name']] : "";
                $type['id'] = $_item['oil_type'];
                $type['name'] = isset($dictMap["oil_type"][$_item['oil_type']]) ? $dictMap["oil_type"][$_item['oil_type']] : "";
                $last[$last_key]['classify'][] = $type;
            }
            $result['oilType'] = array_values($last);
        }
        return $result;
    }

    public function getStationInfo(array $params)
    {
        return $this->stationRepository->getInfo(['id'=>$params['station_id'],"card_classify"=>2]);
    }

    public function getStationWithTank(array $params)
    {
        $params['id'] = $params['station_id'];
        unset($params['station_id']);
        unset($params['card_no']);
        $params['card_classify'] = 2;
        return $this->stationRepository->stationTank( $params );
    }

    public function getGunList(array $params)
    {
        return $this->stationGunReponsitory->getList(['station_id'=>$params['station_id']]);
    }

    public function getGunInfo(array $params)
    {
        return $this->stationGunReponsitory->detail(['id'=>$params['oilGunId']]);
    }

    public function getTankList(array $params)
    {
        return $this->stationTankReponsitory->getList(['station_id'=>$params['station_id']]);
    }

    public function getDict()
    {
        return $this->dictReponsitory->getListByType(['oil_type', 'oil_name','oil_level']);
    }

    public function getStationId(array $params)
    {
        return $this->stationPayQrcodesReponsitory->getStationId(["qr_link"=>$params['qr_link']]);
    }

    public function getTradeById(array $params)
    {
        if( strlen($params['history_id']) > 30) {
            return $this->cardViceTradesReponsitory->getTradeById(['api_id' => $params['history_id']]);
        }else{
            return $this->cardViceTradesReponsitory->getTradeById(['id' => $params['history_id']]);
        }
    }

    public function getTradeInfo(array $params)
    {
        return $this->historyReponsitory->getTradeById(['id'=>$params['history_id']]);
    }

    public function getTradeByIdWithZbank(array $params)
    {
        return $this->cardViceTradesReponsitory->getTradeByIdWithZbank(['trade_api_id'=>$params['history_id']]);
    }

    public function getTradeList(array $params)
    {
        return $this->cardViceTradesReponsitory->getTradeList($params);
    }

    public function getCardStream(array $params)
    {
        return $this->cardViceBillReponsitory->getBillList($params);
    }

    public function getAssignDetail(array $params)
    {
        return $this->cardViceBillReponsitory->getAssignDetails($params);
    }

    public function getTransferDetail(array $params)
    {
        return $this->cardViceBillReponsitory->getTransferDetails($params);
    }

    public function getOrgInfo(array $params)
    {
        return $this->orgReponsitory->getInfo(['orgcode'=>$params['orgcode']]);
    }

    public function getOilOrgInfoById(array $params)
    {
        return $this->orgReponsitory->getOilOrgById(['id'=>$params['id']]);
    }

    public function getOilLimit(array $params)
    {
        return $this->stationRuleReponsitory->getList($params);
    }

    public function splitOrg($org_code)
    {
        $orgList = [];
        $org_len = strlen($org_code);
        if($org_len > 6){
            $each_num = ($org_len - 6) / 2;

            $orgList[] = $org_code;

            for($i=1; $i <= $each_num; $i++) {
                $orgList[] = substr($org_code,0,$org_len - ($i * 2));
            }
        }else{
            $orgList = [$org_code];
        }

        return $orgList;
    }

    public function getCardInfoByPhone(array $params)
    {
        $card_info = $this->cardReponsitory->getCardInfo(['driver_tel'=>$params['phone'],"is_use"=>2]);
        return $card_info;
    }

    public function getCardInfo(array $params,$checkPwd = true)
    {
        $card_info = $this->cardReponsitory->getInfoByCardNo(['card_no'=>$params['card_no']]);
        if(!$card_info){
            throw new \RuntimeException('', CommonError::NO_CARD);
        }
        if($card_info->Org->pcode != CommonDefine::CARD_NUMBER_ONE){
            throw new \RuntimeException('', CommonError::ERROR_CARD_PCODE);
        }
        if(!in_array($card_info->ischeck, ['2', '4']) && $checkPwd){
            if(empty($params['password'])){
                throw new \RuntimeException('', CommonError::CARD_PASSWORD);
            }else{
                $this->checkCardPwd($card_info,$params['password'],true);
            }
        }
        return $card_info;
    }

    //验证账户密码
    public function checkCardPwd($cardInfo,$post_pwd,$isThrow = false)
    {
        $error_num = $cardInfo->pwd_errornum;
        if($error_num >= 5){
            if($isThrow){
                throw new \RuntimeException('密码已连续输错5次，您的账户将被锁定', CommonError::OVER_CARD_PWD);
            }else {
                return ['s' => -1, 'm' => '密码已连续输错5次，您的账户将被锁定'];
            }
        }elseif ($cardInfo->password != $post_pwd && $cardInfo->ischeck != 4) {
            $num = $error_num + 1;
            $setarr['pwd_errornum'] = $num;
            if ($num >= 5) {
                $setarr['islock'] = 1;
            }
            $this->cardReponsitory->editCardInfo(['id'=>$cardInfo->id],$setarr);
            if($num >= 5){
                //同步FossG7能源账户信息
                if($cardInfo->ischeck == 2 || $cardInfo->ischeck ==4) {
                    $ischeck = 4;
                } else {
                    $ischeck = 1;
                }
                (new Foss())->cardSet([
                    'vice_no'    => $cardInfo->card_no,
                    'status'     => 30,
                    'paylimit'   => $cardInfo->paylimit== 1 ? 1 :2,
                    'oil_top'    => $cardInfo->oil_top,
                    'month_top'  => $cardInfo->month_top,
                    'card_level' => $cardInfo->card_level,
                    'day_top'    => $cardInfo->day_top,
                    'isreceive'  => $cardInfo->isactivation== 2 ? 1 :2,//领卡状态 foss,1:领取，2：未领取
                    'setpwd'     => $ischeck,//交易验证设置 setpwd 4:免密,1:使用密码
                    'ischeck'    => $cardInfo->ischeck,
                ]);
                if($isThrow){
                    throw new \RuntimeException('密码已连续输错5次，您的账户将被锁定', CommonError::OVER_CARD_PWD);
                }else {
                    return ['s' => -1, 'm' => '密码已连续输错5次，您的账户将被锁定'];
                }
            }else {
                $leftNum = 4 - $error_num;
                if($isThrow){
                    throw new \RuntimeException('密码输入错误，您还可重试'.$leftNum."次", CommonError::ERROR_CARD_PWD);
                }else {
                    return ['s' => -2, 'm' => '密码输入错误 请重新尝试', "data" => ["leftNum" => $leftNum]];
                }
            }
        }elseif ($error_num && !$cardInfo->islock){
            $setarr['pwd_errornum'] = 0;
            $this->cardReponsitory->editCardInfo(['id'=>$cardInfo->id],$setarr);
        }
        return true;
    }

    //获取机构油站限定
    public function stationLimit( array $params,$orgcode = '' )
    {
        if(empty($orgcode)) {
            $userList = (new FossUser())->getUserInfo(['uid' => $params['user_id']]);
            if (!isset($userList['mobile']) || empty($userList) || empty($userList['mobile'])) {
                return [];
            }
            $card_info = $this->getCardInfoByPhone(['phone' => $userList['mobile']]);
            if (!$card_info || !$card_info->org) {
                return [];
            }
            $orgcode = $card_info->org->orgcode;
        }
        //$card_info = $this->getCardInfo($params,false);
        //$limitCondition['category'] = 2; //类别：1：油站，2：商品
        //$limitCondition['rule_type'] = 5;
        $limitCondition['is_white'] = 1;
        $limitCondition['status'] = 1;
        $limitCondition['source'] = 2;
        $org_list = $this->splitOrg($orgcode);
        $limitCondition['orgcode'] = $org_list;
        //$limitCondition['left_orgcode'] = substr($card_info->gascode,0,6);
        $limitArr = $this->getOilLimit( $limitCondition );
        $activeOrgCode = $this->limitRepository->getActiveOrgcode($orgcode);
        $condition = [];
        if(count($limitArr) > 0){
            foreach ($limitArr as $_item){
                $useList[$_item->orgcode][$_item->rule_type][] = $_item->rule_val;
            }
            Common::log('error',"限定数据：".var_export($useList,true));
            Common::log('error',"油品列表：".var_export($org_list,true));
            foreach ($org_list as $_tmp){
                if(isset($useList[$_tmp])){
                    // 限品规则调整 子级ABC限站 。 上级AB限品。  此时，限品上级AB限品不生效
                    if (strlen($activeOrgCode) > strlen($_tmp)) {
                        continue;
                    }
                    $lastLimit = $useList[$_tmp];
                    foreach ($lastLimit as $_type => $_val){
                        //规则类型;1：站点，2：运营商，3：品牌，4：返利档，5：品类
                        switch ($_type){
                            case 1:
                                $condition['station_code'] = $_val;
                                break;
                            case 2:
                                $condition['pcode'] = $_val;
                                break;
                            case 3:
                                $condition['station_brand'] = $_val;
                                break;
                            case 4:
                                $condition['rebate_grade'] = $_val;
                                break;
                            case 5:
                                $condition['oilNameIn'] = $_val;
                                break;
                        }
                    }
                    break;
                }
            }
        }
        return $condition;
    }

    //获取油站商品数据
    public function getStationGoods(array $params)
    {
        $startTime = microtime(true);
        Common::log('error',"根据油站获取商品,Start：".var_export($startTime,true));
        $limit = $this->stationLimit($params);
        unset($params['user_id']);
        $params = array_merge($params,$limit);
        Common::log('error',"限制条件：".var_export($params,true).",耗时：".(microtime(true) - $startTime)."秒");
        $stationInfo = $this->getStationWithTank($params);
        //$stationInfo = $this->getStationInfo($params);
        if(empty($stationInfo)){
            throw new \RuntimeException("",CommonError::STATION_OFF);
        }

        $distance = 0;
        if(!empty($stationInfo->lat) && !empty($stationInfo->lng) && isset($params['lng']) && !empty($params['lng']) &&
            isset($params['lat']) && !empty($params['lat'])){
            $distance = Common::getDistance($stationInfo->lng,$stationInfo->lat,$params['lng'],$params['lat'],1,0);
        }

        $oilUnit = $stationInfo->station_oil_unit;
        if( in_array($stationInfo->pcode,CommonDefine::forceOilUnitPcode()) ){
            $oilUnit = 2;
        }

        $remark_name = $stationInfo->remark_name ? $stationInfo->remark_name : $stationInfo->station_name;
        $result['stationInfo'] = ['id'=>$stationInfo->id, "station_code"=>$stationInfo->station_code,
                                  "station_name" =>$stationInfo->station_name, "remark_name"=>$remark_name, "address"=>$stationInfo->address,
                                  "distance"     =>$distance, "station_oil_unit"=>$oilUnit, 'pcode'=>$stationInfo->pcode];

        //是否出现支付确认页
        $showPay = 1;
        //是否选择油枪
        $isSelectGun = 1; //选择
        if( in_array($stationInfo->pcode,CommonDefine::getPcodeList()) || in_array($stationInfo->pcode, CommonDefine::getYjfAndZdpPcodes()) ){
            $isSelectGun = 2; //不选择
        }

        //壳牌运营商,隐藏枪列表
        if (!in_array($stationInfo->pcode, CommonDefine::getPcodeList()) and (in_array($stationInfo->pcode, CommonDefine::getPayMentPcode()) || in_array($stationInfo->pcode, CommonDefine::getSpecialPcode()))
            and !in_array($stationInfo->pcode, CommonDefine::getYjfAndZdpPcodes())) {
            $isSelectGun = 3; //隐藏枪列表
        }
        if (in_array($stationInfo->pcode, CommonDefine::forceHideGunPcode())) {
            $isSelectGun = 3;
        }
        $result['isSelectGun'] = $isSelectGun;

        //由于自有站按升加油，二次确认页展示有问题，临时回滚代码
        /*if( in_array($stationInfo->pcode,CommonDefine::getPcodePaySure()) ){
            $showPay = 2;
        }*/
        $showPay = 2;

        $result['showPaySure'] = $showPay;
        // 获取所有油品
        $oil_list = $this->getDict();

        $dictMap = [];
        foreach ($oil_list as $value) {
            ${'all_' . $value->dict_type}[$value->id] = $value;
            $dictMap[$value->dict_type][$value->id] = $value->dict_data;
        }

        $get_oil_text = function ($value, $oil_type) use ($all_oil_type, $all_oil_name, $all_oil_level) {
            if (isset(${'all_' . $oil_type}[$value->$oil_type])) {
                return ${'all_' . $oil_type}[$value->$oil_type]->dict_data;
            }
            return '';
        };

        $gunBatchList = [];
        if($isSelectGun == 2) {
            //key的顺序,oil_name,oil_type,oil_level
            $gunBatchList = $this->batchGunList($stationInfo->id);
            if( count($gunBatchList) == 0 ){
                throw new \RuntimeException("",CommonError::ADAPTER_NO_GUN);
            }
            Common::log('error', "请求Adapter获取油枪列表,End：" . var_export(microtime(true) - $startTime, true) . "秒");
            //sort($gunBatchList);
        }
        $gun_lists = [];
        foreach ($stationInfo->tank as $value) {
            $gunSort = $responseData = [];
            if($isSelectGun == 2){ //车主邦获取油枪列表
                if (count($value->gun) > 0) {
                    $gun_id = $value->gun[0]->id;
                    $_dictKey = $value->oil_name."_".$value->oil_type."_".$value->oil_level;
                    if(isset($gunBatchList[$_dictKey]) && count($gunBatchList[$_dictKey]) > 0){
                        foreach ($gunBatchList[$_dictKey] as $gunNum) {
                            $gun_name = $gunNum . "号";
                            $unit = $this->getUnit($value->oil_name);
                            //$title = $get_oil_text($value, 'oil_type') . $get_oil_text($value, 'oil_name') . $get_oil_text($value , 'oil_level');
                            //$title = $get_oil_text($value, 'oil_type') . $get_oil_text($value, 'oil_name');
                            $title = "";
                            if (isset($dictMap['oil_type'])) {
                                $title .= isset($dictMap['oil_type'][$value->oil_type]) && $dictMap['oil_type'][$value->oil_type] ? $dictMap['oil_type'][$value->oil_type] : "";
                            }
                            if (isset($dictMap['oil_name'])) {
                                $title .= isset($dictMap['oil_name'][$value->oil_name]) && $dictMap['oil_name'][$value->oil_name] ? $dictMap['oil_name'][$value->oil_name] : "";
                            }
                            if (isset($dictMap['oil_level'])) {
                                $title .= isset($dictMap['oil_level'][$value->oil_level]) && $dictMap['oil_level'][$value->oil_level] ? $dictMap['oil_level'][$value->oil_level] : "";
                            }
                            $gunName[$title][$gun_name] = $gun_name;
                            $gun_lists[$title]['name'] = $title;
                            $gun_lists[$title]['oil_type'] = $value->oil_type;
                            $gun_lists[$title]['oil_level'] = $value->oil_level;
                            $gun_lists[$title]['oil_name'] = $value->oil_name;
                            $gun_lists[$title]["gunList"][] = array("gun_name" => $gun_name, "gun_id" => $gun_id,"unit"=>$unit);
                        }
                    }
                }
                $result['isSelectGun'] = 1;
            }else{
                foreach ($value->gun as $gun_fixer) {
                    $gunNum = str_replace("号枪","",$gun_fixer->name);
                    $gunSort[$gunNum] = ['num'=>$gunNum,"id"=>$gun_fixer->id];
                }
                ksort($gunSort);
                foreach ($gunSort as $_item){
                    $title = $get_oil_text($value, 'oil_type') . $get_oil_text($value, 'oil_name') . $get_oil_text($value, 'oil_level');
                    $unit = $this->getUnit($get_oil_text($value, 'oil_name'));
                    $gun_lists[$title]['name'] = $title;
                    $gun_lists[$title]['oil_type'] = $value->oil_type;
                    $gun_lists[$title]['oil_level'] = $value->oil_level;
                    $gun_lists[$title]['oil_name'] = $value->oil_name;
                    $gun_lists[$title]["gunList"][] = array("gun_name"=>$_item['num']."号","gun_id"=>$_item['id'],"unit"=>$unit);
                }
            }
        }
        sort($gun_lists);
        $result['oilGoods'] = array_values($gun_lists);
        return $result;
    }

    //下单之前,获取单价
    public function getOilTradePrice(array $params)
    {
        $card_info = $this->getCardInfo($params,false);

        $gunInfo = $this->getGunInfo($params);
        if(!$gunInfo){
            throw new \RuntimeException('',CommonError::NO_GUN);
        }


        $xInfo = $this->getOrgInfo(['orgcode'=>$card_info->orgcode]);
        //获取单价
        $condition = [
            'station_id' => $gunInfo->station_id,
            'card_no'    => $card_info->card_no,
            'oil_time'   => Carbon::now()->toDateTimeString(),
            'tank_id'    => $gunInfo->tank_id,
            'card_name'  => $card_info->card_name,
            'pcode'      => $gunInfo->station->pcode,
            'orgcode'    => $card_info->gascode,
            'xpcode'     => $xInfo->pcode,
        ];
        //print_r($condition);
        $price_info = (new Gas())->getNowPrice($condition);

        $price = array_get($price_info, 'price', 0);
        if ($price <= 0) {
            throw new \RuntimeException('',CommonError::ERROR_PRICE);
        }
        //枪价
        $highPrice = array_get($price_info, 'mac_price', 0);

        //针对上游的结算价
        $pcode_price = array_get($price_info,"pcode_price",0);

        $float_price = $actual_price = $price;
        $specialList = [];
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($card_info->gascode,0,6),$specialList['SPECIAL_ORGLIST']) ){
            $v_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $float_price = number_format(($v_price + $float_price),2,".","");
        }

        //处理油品单位
        $oil_name = array_get($params,"oilName","");
        $unit = $this->getUnit($oil_name);

        return [
            'trade_price' => number_format($price, 2, ".", ""),
            "macPrice"    => number_format($highPrice,2,".",""),
            "float_price" => number_format($float_price,2,".",""),
            "pcode_price" => number_format($pcode_price,2,".",""),
            'unit'        => $unit,
        ];
    }

    //支付成功后,获取订单详情
    public function deductSuccessDetail(array $params)
    {
        $info = $this->getTradeByIdWithZbank($params);
        if(!$info || !$info->org_id) {
            throw new \RuntimeException('',CommonError::BILL_NO_FOUND);
        }

        $info->trade_price = $info->trade_price ?: "";
        $info->float_price = 0;

        $orgInfo = $this->getOilOrgInfoById(['id' => $info->org_id]);
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($orgInfo->orgcode,0,6),$specialList['SPECIAL_ORGLIST']) ){
            $v_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $info->trade_price = number_format(($info->trade_price - $v_price),2,".","");
            $info->float_price = $v_price;
        }

        $info->service_money = $info->service_money ? $info->service_money : "0.00";
        $info->oil_name = $info->oil_name ?: "";
        $info->trade_place = $info->trade_place ?: "";
        $info->truck_no = $info->truck_no ? $info->truck_no : "";
        $info->bill_no = "ZF".$this->createNo();
        $info->oil_com_txt = $this->getOilCom($info->oil_com);
        $info->unit = $this->getUnit($info->oil_name);
        $info->truename = $info->truename ?: "";
        if($info->trade_money > 0) {
            $info->trade_money_txt = $info->trade_money ? "-".$info->trade_money : "0.00";
        }else{
            $info->trade_money_txt = $info->trade_money ? "+".number_format(abs($info->trade_money),2,".","") : "0.00";
        }
        return $info;
    }

    //订单列表获取订单详情
    public function getTradeDetail(array $params)
    {
        $info = $this->getTradeById($params);
        if(!$info) {
            throw new \RuntimeException('',CommonError::BILL_NO_FOUND);
        }
        $info->trade_num = $info->trade_num ? number_format(abs($info->trade_num),2,".","") : "0.00";
        $info->service_money = $info->service_money ? number_format(abs($info->service_money),2,".","") : "0.00";
        $info->total_money = $info->total_money ? $info->total_money : "0.00";
        $info->truck_no = $info->truck_no ? $info->truck_no : "";
        $info->bill_no = "ZF".$this->createNo();
        $info->oil_com_txt = $this->getOilCom($info->oil_com);
        $info->unit = $this->getUnit($info->oil_name);
        if(!empty($info->cancel_sn) && $info->trade_money < 0){
            $info->is_refund = 1;
        }else{
            $info->is_refund = 2;
        }
        if($info->trade_money > 0) {
            $info->trade_money_txt = $info->trade_money ? "-".$info->trade_money : "0.00";
        }else{
            $info->trade_money_txt = $info->trade_money ? "+".number_format(abs($info->trade_money),2,".","") : "0.00";
        }

        $info->showQrcode = 1; //不展示
        if( in_array($info->pcode,CommonDefine::getPayMentPcode()) && empty($info->cancel_sn) ) {
            $info->showQrcode = 2;
        }

        $info->float_price = 0;
        $orgInfo = $this->getOilOrgInfoById(['id' => $info->org_id]);
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($orgInfo->orgcode,0,6),$specialList['SPECIAL_ORGLIST']) ){
            $v_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $info->trade_price = number_format(($info->trade_price - $v_price),2,".","");
            $info->float_price = $v_price;
        }
        return $info;
    }

    public function getOilCom($oil_com)
    {
        if($oil_com == 21){
            $oil_com_txt = '共享账户';
        }elseif($oil_com == 30){
            $oil_com_txt = '发财账户';
        }else{
            $oil_com_txt = '充值账户';
        }
        return $oil_com_txt;
    }

    public function getUnit($oil_name)
    {
        if (stripos($oil_name, "天然气") !== false) {
            $unit = 'Kg';
            if (stripos($oil_name, "压缩天然气") !== false || stripos($oil_name, "立方米") !== false) {
                $unit = '立方';
            }
            if (stripos($oil_name, "液化天然气") !== false) {
                $unit = 'Kg';
            }
        } else {
            $unit = "升";
        }
        return $unit;
    }

    public function getTrades(array $params)
    {
        //请求foss-user得到手机号
        $userList = (new FossUser())->getUserInfo(['uid'=>$params['user_id']]);
        if(!isset($userList['history_mobiles']) || empty($userList) || count($userList['history_mobiles']) == 0){
            throw new \RuntimeException('',CommonError::USER_ERROR);
        }
        $mobiles = $userList['history_mobiles'];
        unset($params['user_id']);
        //$mobiles[] = '18111111111';
        $params['driver_phone'] = $mobiles;
        $list = $this->getTradeList($params);
        if(count($list) == 0){
            return [
                "current_page"   => 0,
                "data"           => [],
                "first_page_url" => "",
                "from"           => 0,
                "last_page"      => 0,
                "last_page_url"  => "",
                "next_page_url"  => "",
                "path"           => "",
                "per_page"       => "10",
                "prev_page_url"  => "",
                "to"             => 0,
                "total"          => 0
            ];
        }
        $pcodeList = CommonDefine::getPayMentPcode();
        foreach ($list as &$_item) {
            $_item->service_money = $_item->service_money ? $_item->service_money : "0.00";
            $_item->total_money = $_item->total_money ? $_item->total_money : "0.00";
            $_item->truck_no = $_item->truck_no ? $_item->truck_no : "";
            $_item->oil_com_txt = $this->getOilCom($_item->oil_com);
            $_item->unit = $this->getUnit($_item->oil_name);
            if(!empty($_item->cancel_sn) && $_item->trade_money < 0){
                $_item->is_refund = 1;
            }else{
                $_item->is_refund = 2;
            }
            if($_item->trade_money > 0) {
                $_item->trade_money_txt = $_item->trade_money ? "-".$_item->trade_money : "0.00";
            }else{
                $_item->trade_money_txt = $_item->trade_money ? "+".number_format(abs($_item->trade_money),2,".","") : "0.00";
            }
            if(in_array($_item->account_name,["现金账户","储值账户","卡现金账号","储值账号","余额",""])){
                $_item->account_type = "现金账户";
            }else{
                $_item->account_type = "授信账户";
            }
            $_item->showQrcode = 1; //不展示
            if( in_array($_item->pcode,$pcodeList) && empty($_item->cancel_sn) ) {
                $_item->showQrcode = 2;
            }
        }
        return $list;
    }

    public function getCardBill(array $params)
    {
        $userList = (new FossUser())->getUserInfo(['uid'=>$params['user_id']]);
        if(!isset($userList['history_mobiles']) || empty($userList) || count($userList['history_mobiles']) == 0){
            throw new \RuntimeException('',CommonError::USER_ERROR);
        }
        $mobiles = $userList['history_mobiles'];
        unset($params['user_id']);
        //$mobiles[0] = '***********';
        //$mobiles[1] = '***********';
        $params['driver_phone'] = $mobiles;
        $params['inCome'] = 1;
        $inCome = $this->getCardStream($params);
        unset($params['inCome']);
        $params['outCome'] = 1;
        $outCome = $this->getCardStream($params);
        unset($params['outCome']);
        $list = $this->getCardStream($params);
        if(count($list) == 0){
            return [
                "current_page"   => 0,
                "data"           => [],
                "first_page_url" => "",
                "from"           => 0,
                "last_page"      => 0,
                "last_page_url"  => "",
                "next_page_url"  => "",
                "path"           => "",
                "per_page"       => "10",
                "prev_page_url"  => "",
                "to"             => 0,
                "total"          => 0,
                "totalIn"        => 0,
                "totalOut"       => 0
            ];
        }
        $pcodeList = CommonDefine::getPayMentPcode();
        foreach ($list as &$_item) {
            //1:已退款，2：无退款
            if($_item->res_type == 10 && $_item->amount > 0){
                $_item->is_refund = 1;
            }else{
                $_item->is_refund = 2;
            }
            $_item->trade_money = $_item->amount;
            if($_item->amount > 0) {
                $_item->trade_money_txt = $_item->amount ? "+".number_format(abs($_item->amount),2,".","") : "0.00";
            }else{
                $_item->trade_money_txt = $_item->amount ? $_item->amount : "0.00";
            }
            if( $_item->pay_type == 20 ){
                $_item->account_type = "授信账户";
            }else{
                $_item->account_type = "现金账户";
            }
            $_item->vice_no = $_item->card_no;
            $_item->trade_place = $_item->trade_desc;
            $_item->history_id = "";
            $_item->showQrcode = 1; //不展示
            if($_item->res_type == 10) {
                $detail = $this->getTradeById(['history_id'=>$_item->res_id]);
                $_item->history_id = $detail->api_id;
                if (in_array($detail->pcode, $pcodeList) && $_item->is_refund == 2) {
                    $_item->showQrcode = 2;
                }
            }
        }
        $result = $list->toArray();
        $result['totalIn'] = number_format(abs($inCome),2,".","");
        $result['totalOut'] = number_format(abs($outCome),2,".","");
        return $result;
    }

    public function createNo()
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = date("ymds", time());
        $str .= substr($microArr[0], 3, 2);
        $str .= sprintf('%02d', rand(0, 99));
        return $str;
    }

    //扫码付款,根据二维码内容,获取油站id
    public function getStationByUrl(array $params)
    {
        $info = $this->getStationId($params);
        if (!$info) {
            throw new \RuntimeException('', CommonError::QRCODE_ERROR);
        }
        return $info->station_id;
    }


    /**
     * 长连接,推送消息
     * @throws \Exception
     */
    public function msg2User(array $params)
    {
        /*$content['data'] = [
            "id"=>$params['pre_history_id'],
            "card_no"=>$params['card_no'],
            "msg" =>  CommonError::formatMsg($message),
            "code" => $code,
        ];*/
        $params['id'] = array_get($params, "pre_history_id", "");
        $params['mobile'] = array_get($params, "mobile", "");
        $pushClient = 'wmp';
        if (!empty($params['id'])) {
            $orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $params['id']], ['order_id', 'order_status', 'driver_phone', 'order_channel', 'order_flag']);
            if (!empty($orderInfo) && in_array($orderInfo['order_channel'], CardTradeConf::$order_channel_h5)) {
                $pushClient = 'h5';
                if ($orderInfo['order_flag'] == CardTradeConf::TRUCKFLAG) {
                    $params['mobile'] = $orderInfo['driver_phone'];
                }
                if ($orderInfo['order_flag'] == CardTradeConf::OPEN_API_FLAG) {

                    if (in_array($params['msg_type'] ?? '', [2, 3])) {

                        $this->messageService::createKafkaMessage(json_encode([
                            'order_no' => (string)$orderInfo['order_id'],
                            'status'   => $params['msg_type'] - 1,
                            'reason'   => $params['message'],
                            'orgcode'  => $orderInfo['ext']['open_api_org_code'],
                        ]));
                    }
                }
            }
        }

        if (empty($params['mobile'])) {
            $card_info = $this->getCardInfo($params,false);
            if(empty($card_info->driverphone)){
                return false;
            }
            $params['mobile'] = $card_info->driverphone;
        }

        Common::log('error',"推送信息：".var_export($params,true));
        //$card_info->driverphone = "18612870619";
        $user = (new FossUser())->getUserInfoByPhone(['mobile'=>$params['mobile']]);
        if(!isset($user['unique_cert']) || empty($user['unique_cert'])){
            return false;
        }
        $message = array_get($params,"message","");
        //$code = array_get($params,"code","0");
        $user_id = $user['unique_cert'];
        $msg_type = array_get($params,"msg_type",1);
        $topic = CommonDefine::getTopic($msg_type);
        $content["topic"] = $topic;

        unset($params['pre_history_id']);
        unset($params['msg_type']);
        $params['msg'] = CommonError::formatMsg($message);
        $content['data'] = $params;
        Common::log('error',"长连接参数：".var_export($content,true));
        try {
            if ($pushClient == 'wmp') {
                $message = [
                    'data' => ['content' => json_encode($content)]
                ];
                $pushResult = SocketIo::pushMessage($user_id, $message);
                Common::log('info',"长连接发送结果：" .$pushResult.",参数：". json_encode($message));
            } else {
                $params['unique_cert'] = $user['unique_cert'];
                $res = $this->messageService->pushMsg2H5($params, $msg_type);
                Common::log('error',"长连接发送结果：".var_export($res,true));
            }

        }catch (\Exception $exception){
            Common::log('error',"长连接错误：".strval($exception));
        }
    }

    //验密及请求Gas生成加油流水
    public function createTrades(array $params)
    {
        $card_info = $this->getCardInfo($params,true);
        $trade_id = $params['pre_history_id'];
        try {
            $params['trade_id'] = $trade_id;
            unset($params['pre_history_id']);
            $gasRes = (new Gas())->finishTrades($params);

            if (empty($gasRes)) {
                throw new \RuntimeException('订单支付失败', CommonError::GAS_ERROR);
            }
            return $gasRes;
        }catch (\Exception $e) {
            $this->msg2User([
                'card_no'        => $params['card_no'],
                'pre_history_id' => $trade_id,
                'msg_type'       => 3,
                "code"           => CommonError::GAS_ERROR,
                "message"        => $e->getMessage()
            ]);
            throw new \RuntimeException($e->getMessage(), CommonError::GAS_ERROR);
        }
    }


    //支付确认页
    public function paySure(array $params)
    {
        $card_info = $this->getCardInfo($params,false);
        $gun_info = $this->getGunInfo($params);
        //判断该请求是否需要验证枪号的合法性(通过卡片所属运营商以及站点所属运营商check)
        $isOnLine = false;
        if ($gun_info->station->pcode) {
            if (in_array($gun_info->station->pcode, CommonDefine::getPcodeList())) {
                $gunNumber = array_get($params,"gunNumber","");
                if( $gunNumber == "") {
                    throw new \RuntimeException('请输入枪号',CommonError::GUN_NUM);
                }
                $responseData = $this->getAdapterGunList($gun_info->station->id,$gun_info->tank->oil_name,$gun_info->tank->oil_level,$gun_info->tank->oil_type);
                if (!in_array($gunNumber,$responseData) || empty($responseData) ) {
                    throw new \RuntimeException('输入的枪号有误，请和加油员确认枪号后重新输入',CommonError::GUN_FAIL);
                }
                $isOnLine = true;
            }
        }

        if (!$gun_info || empty($gun_info)) {
            throw new \RuntimeException('请选择油枪',CommonError::GUN_NUM);
        }

        $priceList = $this->getOilTradePrice($params);
        $price = $priceList['trade_price'];
        $highPrice = $priceList['macPrice'];

        $inputMoney = $params['money'];
        if (bccomp(0, floatval($inputMoney), 2) >= 0) {
            throw new \RuntimeException('请输入金额',CommonError::ERROR_MONEY);
        }
        if ($highPrice <= 0) {
            $highPrice = $price;
        }
        if (bccomp($price, $highPrice, 2) == 0) {
            $highPrice = $price;
        }

        $sureData['truck_no'] = $card_info->truck_no ? $card_info->truck_no : '未知';
        $sureData['oil_name'] = array_get($params,'oil_name',"");
        $sureData['showmsg'] = 2;//不展示提示语
        $sureData['oil_price'] = number_format($highPrice, 2, ".", "");
        $oil_num = $inputMoney / $highPrice;
        $sureData['oil_num'] = number_format($oil_num, 2, ".", "");
        $sureData['oil_fee'] = number_format($inputMoney, 2, ".", "");
        $actucal_fee = $oil_num * $price;
        $discount_fee = $inputMoney - number_format($actucal_fee,2,".","");
        $sureData['oil_discount_fee'] = number_format($discount_fee, 2, ".", "");
        if (bccomp($price, $highPrice, 2) == 0) {
            $sureData['oil_actucal_fee'] = number_format($inputMoney, 2, ".", "");;
        } else {
            $sureData['oil_actucal_fee'] = number_format($actucal_fee, 2, ".", "");
        }
        $sureData['unit'] = $this->getUnit($sureData['oil_name']);

        $sureData['isClose'] = 0;
        if (bccomp($price, $highPrice, 2) > 0) {
            $sureData['isClose'] = 1;
        }

        $sureData['float_price'] = 0.00;

        $sureData['msg'] = "";
        //祥辉调价配置
        $specialList = [];
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($card_info->gascode,0,6),$specialList['SPECIAL_ORGLIST']) ) {
            $sureData['showmsg'] = 1;
            $sureData['isClose'] = 0;

            $float_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $sureData['msg'] = "本次结算将收服务费（".$float_price."元/".$sureData['unit']."），如有异议请联系您的车队";
            $sureData['float_price'] = $float_price;
            $price += $float_price;
            $actucal_fee = $oil_num * $price;
            $sureData['oil_actucal_fee'] = number_format($actucal_fee, 2, ".", "");
        }

        $sureData['tradeByOrder'] = $isOnLine ? 'on' : 'off';
        return $sureData;
    }

    //车主邦预支付订单
    public function placeOrder(array $params)
    {
        $userList = (new FossUser())->getUserInfo(['uid'=>$params['user_id']]);
        if(!isset($userList['mobile']) || empty($userList) || empty($userList['mobile'])){
            throw new \RuntimeException('',CommonError::USER_ERROR);
        }
        $priceList = $this->getOilTradePrice($params);

        if (bccomp($priceList['trade_price'], $params['priceGun'], 2) > 0) {
            throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付",CommonError::GUN_ERROR_PRICE);
        }

        /*if(bccomp($params['price'] ,$priceList['float_price'],2) != 0){
            throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付",CommonError::GUN_ERROR_PRICE);
        }*/

        $card_info = $this->getCardInfo($params,false);
        $gun_info = $this->getGunInfo($params);

        $gas_res = (new Gas())->stationLimit([
            'orgcode'    => $card_info->gascode,
            'card_no'    => $card_info->card_no,
            'station_id' => $gun_info->station_id,
        ]);

        $error_message = array_get($gas_res, 'msg', null);
        if ($error_message) {
            throw new \RuntimeException($error_message,CommonError::GAS_ERROR);
        }

        $data = (new Gas())->placeOrder([
            'gun_id'       => $params['oilGunId'],
            'amount'       => $params['actucal_money'],
            'openid'       => rand(1,99999999),
            'order_phone'  => $userList['mobile'],
            'amountGun'    => $params['amountGun'],
            'gunNumber'    => $params['gunNumber'],
            'priceGun'     => $params['priceGun'],
            'price'        => $params['price'],
            'actual_price' => $params['actual_price']
        ]);
        return $data;
    }

    //车主邦支付待支付订单
    public function submitOrder(array $params)
    {
        $card_info = $this->getCardInfo($params);

        $gas_res = (new Gas())->stationLimit([
            'orgcode'    => $card_info->gascode,
            'card_no'    => $card_info->card_no,
            'station_id' => $params['station_id'],
        ]);

        $error_message = array_get($gas_res, 'msg', null);
        if ($error_message) {
            throw new \RuntimeException($error_message,CommonError::GAS_ERROR);
        }

        $data = (new Gas)->submitOrder([
            'order_id' => $params['orderId'],
            'card_no'  => $params['card_no'],
        ]);
        if(!isset($data['status']) || $data['status'] != 1){
            throw new \RuntimeException($data['message'],CommonError::GAS_ERROR);
        }
        return isset($data['data']) ? $data['data'] : $data;
    }

    //获取车主邦油枪列表
    public function getAdapterGunList($station_id,$oil_name,$oil_level,$oil_type)
    {
        //$gun_info->station->id,
        //$gun_info->tank->oil_name,
        //$gun_info->tank->oil_level,
        //$gun_info->tank->oil_type,
        //调用对接服务查询油品对应枪号
        $responseData = (new Adapter())->checkGun([
            'stationId'  => $station_id,
            'oilTypeId'  => $oil_name,
            'oilLevelId' => $oil_level,
            'oilNoId'    => $oil_type
        ]);
        return $responseData;
    }

    //批量获取油站枪号
    public function batchGunList($station_id)
    {
        $responseData = (new Adapter())->batchGun([
            'stationId' => $station_id,
        ]);
        return $responseData;
    }

    /**
     * 根据站点编码获取站点ID
     *
     * @param $station_code
     * @return array
     * @throws \Exception
     */
    public function getStationIdsByStationCodes($station_code)
    {
        $needParams = [
            'station_code' => $station_code
        ];
        $result = $this->stationRepository->getBatchStationInfoByParams($needParams, ['id' ,'station_code']);
        if (empty($result)) {
            return [];
        }

        return array_column($result, 'id');
    }

    /**
     * 获取gms的油品价格
     * @param array $params
     * @return array
     * @throws \Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/29 3:11 下午
     */
    public function getGmsOilPrice($params = [])
    {
        if (!isset($params['card_no']) || empty($params['card_no'])) {
            return [];
        }
        $card_info = $this->cardService->getCardInfoFromUser(['card_no' => $params['card_no']]);

        if (!$card_info || empty($card_info['orgcode'])) {
            throw new \RuntimeException('', CommonError::NO_CARD);
        }

        $gunInfo = $this->getGunInfo($params);
        if (!$gunInfo) {
            throw new \RuntimeException('', CommonError::NO_GUN);
        }
        $gunArr = $gunInfo->toArray();
        $orgcode = $card_info['orgcode'];
        $limitOrgCode = $orgcode;
        $queryStationPriceOrgCode = NewOrderRepository::getSpecialPriceOrg($gunInfo->station_id, $orgcode,
            substr($card_info['orgcode'], 0, 6));
        //请求Foss查询余额和油站限制
        /*$condition['orgcode'] = $orgcode;
        $condition['vice_no'] = $params['card_no'];
        $condition['flag'] = 1;
        $condition['station_id'] = $gunInfo->station_id;
        (new Foss())->getStationLimit($condition);*/
        $limitRule = $this->stationLimit([], $orgcode);
        if (count($limitRule) > 0) {
            foreach ($limitRule as $_flag => $_limit) {
                switch ($_flag) {
                    case 'pcode':
                        $pcode =  array_get($gunArr,'station.pcode','');
                        if( !in_array($pcode,$_limit) ){
                            throw new \RuntimeException('该站点被限定使用,请司机联系车队长~',5920);
                        }
                        break;
                    case 'station_brand':
                        $brand = array_get($gunArr,'station.station_brand','');
                        if( !in_array($brand,$_limit) ){
                            throw new \RuntimeException('该站点被限定使用,请司机联系车队长~',5920);
                        }
                        break;
                    case 'rebate_grade':
                        $rebate = array_get($gunArr,'station.rebate_grade','');
                        if( !in_array($rebate,$_limit) ){
                            throw new \RuntimeException('该站点被限定使用,请司机联系车队长~',5920);
                        }
                        break;
                    case 'oilNameIn':
                        $name = array_get($gunArr,"tank.oil_name","");
                        if( !in_array($name,$_limit) ){
                            throw new \RuntimeException('该商品被限定使用，请司机联系车队长~',5910);
                        }
                        break;
                    default:
                        $code = array_get($gunArr,'station.station_code','');
                        if( !in_array($code,$_limit) ){
                            throw new \RuntimeException('该站点被限定使用,请司机联系车队长~',5920);
                        }
                }
            }
        }

        //针对卡车宝贝，需要获取通用客户定价
        if(isset($params['isAll']) && $params['isAll'] == 1){
            $queryStationPriceOrgCode = $orgcode = '';
        }

        $data = (new FossStation())->getOilListAndPrice(['station_id' => $gunInfo->station_id,
            'orgcode' => $queryStationPriceOrgCode, 'limit_orgcode' => $limitOrgCode]);
        if(count($data) == 0){
            throw new \RuntimeException('',CommonError::NO_GUN);
        }
        $gunId = array_get($params,'oilGunId','');
        $price_info = [];
        foreach ($data as $_item){
            foreach ($_item['gun'] as $_sub){
                if($_sub['gun_id'] == $gunId){
                    $price_info = $_item;
                }
            }
        }

        $price = array_get($price_info, 'platform_price', 0);

        if ($price <= 0) {
            throw new \RuntimeException('',CommonError::ERROR_PRICE);
        }
        //枪价
        $highPrice = array_get($price_info, 'mac_price', $price);

        //针对上游的结算价
        $pcode_price = array_get($price_info,"supplier_price",0);

        $float_price = $actual_price = $price;
        $specialList = [];
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($orgcode,0,6),$specialList['SPECIAL_ORGLIST']) ){
            $v_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $float_price = number_format(($v_price + $float_price),2,".","");
        }

        //处理油品单位
        $oil_name = array_get($params,"oilName","");
        $unit = $this->getUnit($oil_name);

        return [
            'trade_price' => number_format($price, 2, ".", ""),
            "macPrice"    => number_format($highPrice,2,".",""),
            "float_price" => number_format($float_price,2,".",""),
            "pcode_price" => number_format($pcode_price,2,".",""),
            'unit'        => $unit,
            'truck_no'    => array_get($card_info,'truck_no',''),
            'orgcode'     => array_get($card_info,'orgcode',''),
        ];

    }
}