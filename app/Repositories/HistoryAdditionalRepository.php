<?php


namespace App\Repositories;


use App\Models\AdditionalOrderItem;
use App\Models\AdditionalOrderRecord;
use App\Models\BaseModel;

class HistoryAdditionalRepository
{
    protected $additionalOrderRecord;
    protected $additionalOrderItem;

    public function __construct
    (
        AdditionalOrderRecord $additionalOrderRecord,
        AdditionalOrderItem $additionalOrderItem
    )
    {
        $this->additionalOrderRecord = $additionalOrderRecord;
        $this->additionalOrderItem = $additionalOrderItem;
    }

    /**
     * 添加一条补单信息
     *
     * @param array $insertArray
     * @return mixed
     */
    public function insertOneAdditionRecord(array $insertArray)
    {
        return $this->additionalOrderRecord->insertOneRecord($insertArray);
    }

    /**
     * 更新一条补单信息
     *
     * @param $id
     * @param array $updateArray
     * @return mixed
     */
    public function updatedOneAdditionRecordById($id, array $updateArray)
    {
        return $this->additionalOrderRecord->updateOneRecordById($id, $updateArray);
    }

    /**
     * 查询一条补单信息
     *
     * @param $params
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneRecordByParams($params, $select = ['*'], $toArray = true)
    {
        return $this->additionalOrderRecord->getOneRecordByParams($params, $select, $toArray);
    }

    /**
     * 添加一条补单详情
     *
     * @param array $insertArray
     * @return mixed
     */
    public function insertOneAdditionalItem(array $insertArray)
    {
        return $this->additionalOrderItem->insertOneItem($insertArray);
    }

    /**
     * 更新一条补单详情
     *
     * @param $order_id
     * @param array $updateArray
     * @return mixed
     */
    public function updateOneAdditionalItemByOrderId($order_id, array $updateArray)
    {
        return $this->additionalOrderItem->updateOneItemByOrderId($order_id, $updateArray);
    }

    /**
     * 查询一条补单详情
     *
     * @param $params
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneAdditionItemByParams($params, $select = ['*'], $toArray = true)
    {
        return $this->additionalOrderItem->getOneItemByParams($params, $select, $toArray);
    }

    /**
     * 分页获取补录列表
     *
     * @param $whereParams
     * @param $page
     * @param $limit
     * @param bool $toArray
     * @return array
     */
    public function additionalPaginate($whereParams, $page, $limit, $toArray = true)
    {
        $query = $this->additionalOrderRecord
            ->select([
                'record.*',
                'item.order_id',
                'item.order_status',
                'item.order_msg',
                'item.third_order_id',
                'item.third_order_status',
                'item.third_order_msg',
            ])->from('gas_additional_order_record as record')
            ->leftJoin('gas_additional_order_item as item', 'record.id', '=', 'item.record_id');
        $query = BaseModel::scopeWithCondition($query, $whereParams);

        // 总数
        $count = $query->count();
        // 总金额
        $totalPayMoney = $query->sum('oil_money');
        // 加油总数量
        $totalOilNum = $query->sum('oil_num');

        $data = $query->orderBy('record.update_time', 'DESC')->offset(($page - 1)*$limit)->limit($limit)->get();
        if ($toArray) {
            $data = empty($data) ? [] : $data->toArray();
        }

        return ['count' => $count, 'total_oil_num' => $totalOilNum, 'total_pay_money' => $totalPayMoney, 'data' => $data, 'page' => $page, 'limit' => $limit];
    }
}