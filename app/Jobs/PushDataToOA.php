<?php

namespace App\Jobs;

use App\Library\Helper\Common;
use App\Services\MessageService;
use App\Servitization\Adapter;

class PushDataToOA extends Basic
{
    protected $messageType;

    protected $data;

    /**
     * Create a new job instance.
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->messageType = $params['msy_type'];
        $this->data = $params['data'];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $orderId = $this->data['order_id'] ?? '';
        if ($orderId) {
            $checkAdapterOrderInfo = [];
            for($i=0;$i<3;$i++) {
                $checkAdapterOrderInfo = app(Adapter::class)->queryOrderExchange(['order_id' => $orderId]);
                if (!empty($checkAdapterOrderInfo)) {
                    break;
                }
                usleep(1500000);
            }
            
            if (empty($checkAdapterOrderInfo)) {
                Common::log('info', date('Y-m-d H:i:s').'订单推送OA失败，OA无该订单信息，订单号：'.$orderId);
                // 报警
                return;
            }

            try {
                app(MessageService::class)->pushData2OA($this->data, $this->messageType);
            } catch (\Exception $e) {
                Common::log('info', date('Y-m-d H:i:s').'订单推送OA失败:'.$e->getMessage().$e->getFile().$e->getLine());
            }
        }
    
        Common::log('info', date('Y-m-d H:i:s').'订单推送OA'.($this->data['order_id'] ?? ''));
    }
}
