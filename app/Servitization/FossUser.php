<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/20
 * Time: 10:05
 */

namespace App\Servitization;

use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;


class FossUser
{

    private $config;

    public function __construct()
    {
        $this->config = config('foss_user');
    }

    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @param array $headers
     * @param bool $addKafkaLog
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL, $headers = [], $addKafkaLog = false)
    {
        $params = $args;
        try {
            $url = $this->config['apiUrl'] . $params['method'];
            $startTime = microtime(true)*1000;
            $data = Common::request($type, $url, 60, $params['data'], $headers);
            $cost = microtime(true)*1000 - $startTime;
            Log::info('请求user,参数:' . json_encode($params, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);
            $data = json_decode($data, true);
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($params['method'], 'foss-user', $params, $data, $cost);
            }
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (Exception $e) {
            KafkaLog::thirdOperationLog($params['method'], 'foss-user', $params, $e->getMessage());
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    public function checkCard($params)
    {
        $data = self::exec('post', [
            'method' => '',
            'data'   => $params
        ]);
        return $data;
    }

    public function getUserInfo($params)
    {
        $data = self::exec('post', [
            'method' => '/api/fossOrder/user/getUserMobile',
            'data'   => $params
        ]);
        return $data;
    }

    public function getOcrInfo($params)
    {
        $data = self::exec('post', [
            'method' => '/api/fossOrder/user/getOcrInfo',
            'data'   => $params
        ]);
        return $data;
    }

    public function getUserInfoByPhone($params)
    {
        $data = self::exec('post', [
            'method' => '/api/fossOrder/user/getUserInfoByMobile',
            'data'   => $params
        ]);
        return $data;
    }

    public function parseQrCode($params)
    {
        return self::exec('post', [
            'method' => '/api/gms/card/deCardPayQrcode',
            'data'   => $params
        ]);
    }

    //获取用户在机构下的电子卡
    public function getUserCardList($params)
    {
        $data = self::exec('post', [
            'method' => '/api/fossOrder/card/getCardListByOrgCode',
            'data'  => $params
        ]);
        return $data;
    }


    /**
     * 根据账号获取能源账户信息
     *
     * @param $params
     * @return mixed
     */
    public function getCardInfoByCardNo($params)
    {
        $data = self::exec('post', [
            'method' => '/api/fossStation/card/getList',
            'data'   => $params
        ]);

        return array_get($data, 'data.0', []);
    }

    /**
     * 根据账号获取账户信息(调用foss_user单卡查询接口)
     * @param $params
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/24 3:13 下午
     */
    public function getCardInfoByNo($params)
    {
        return self::exec('post', [
            'method' => '/api/gms/card/getCardInfoWithCurrAccount',
            'data'   => $params
        ]);
    }

    /**
     * appKey配置文件
     *
     * @param array $params
     * @return array|mixed
     * @throws Exception
     */
    public function getAppKeyConfig($params = [])
    {
        $data = self::exec('post', [
            'method' => '/admin/app/appKeyConfig',
            'data'   => $params
        ]);

        return $data;
    }

    /**
     * 获取路由的配置文件
     *
     * @param array $params
     * @return array|mixed
     * @throws Exception
     */
    public function getMenuConfigBySource(array $params)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/admin/menu/config',
            'data'   => $params
        ]);

        return $data;
    }

    /**
     * 查询用户权限
     *
     * @param array $params
     * @param array $headers
     * @return array|mixed
     */
    public function checkUserPermissionByUid(array $params, $headers = [])
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/admin/menu/checkUserPermission',
            'data'   => $params
        ], NULL, $headers);

        return $data;
    }

    /**
     * 根据Uid获取用户基本信息
     *
     * @param array $params
     * @param array $headers
     * @return array|mixed
     */
    public function getAdminBaseInfoByUid(array $params, array $headers)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/admin/getAdminBaseInfoByUid',
            'data'   => $params
        ], NULL, $headers);

        return $data;
    }

    /**
     * 取G7能源账户信息及当前使用账户
     *
     * @param $params
     * @return array|mixed
     */
    public function getCardInfoWithCurrAccount($params)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/api/gms/card/getCardInfoWithCurrAccount',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 发送长链接消息
     *
     * @param $params
     * @return array|mixed
     */
    public function sendMsg($params)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/api/gms/user/sendMsg',
            'data' => $params
        ], NULL, [], true);

        return $data;
    }

    /**
     * 校验G7能源账户密码及锁定
     *
     * @param $params
     * @return array|mixed
     */
    public function checkPassword($params)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/api/gms/card/checkPassword',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * FOSS扣款
     *
     * @param $params
     * @return array|mixed
     */
    public function tradePay($params)
    {
        if (empty($params)) {
            return [];
        }

        $data = self::exec('post', [
            'method' => '/api/gms/order/tradePay',
            'data' => $params,
        ], NULL, [], true);

        return $data;
    }

    /**
     * 查询支付结果
     *
     * @param $params
     * @return array|mixed
     */
    public function queryPayResult($params)
    {
        if (empty($params)) {
            return [];
        }

        $data = self::exec('post', [
            'method' => '/api/gms/order/queryPayResult',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 退款
     *
     * @param $params
     * @return array|mixed
     */
    public function cancelTrade($params)
    {
        if (empty($params)) {
            return [];
        }

        $data = self::exec('post', [
            'method' => '/api/gms/order/cancelTrade',
            'data'   => $params,
        ], NULL, [], true);

        return $data;
    }

    /**
     * 获取需要二次核销订单的核销凭证
     *
     * @param $params
     * @return mixed
     */
    public function getVerificationCertificate($params)
    {
        return self::exec('post', [
            'method' => '/api/fossOrder/card/getVerificationCertificate',
            'data'   => $params
        ]);
    }
    /**
     * 获取用户-角色-组织详情
     * @param array $params
     * @param array $headers
     * @return array|mixed
     * @throws \Exception
     */
    public  function getUserRoleRuleByWhere(array $params,array $headers)
    {
        if (empty($params)) {
            return [];
        }
        $data = self::exec('post', [
            'method' => '/admin/users/getUserRolesRules',
            'data'   => $params
        ], NULL, $headers);
        return $data['data'] ?? [];
    }

    public function getUserAndElectricityCard(array $params)
    {
        return self::exec('post', [
            'method' => '/api/fossOrder/card/getUserAndElectricityCard',
            'data'   => $params
        ]);
    }
}