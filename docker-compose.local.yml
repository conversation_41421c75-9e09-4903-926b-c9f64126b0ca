version: "2"
services:
  laravel-demo:
    image: kevin50ster/bluesword-php:v0.2.18
    ports:
    - 30012:30002/tcp
    - 8012:80/tcp
    environment:
      APPLICATION__NAME: 'foss-order'
      APPLICATION__DEBUG: 'true'
      CONSUL__ADDRESS: ************
      CONSUL__PORT: 8500
      CONSUL__SERVER__TAGS: 'local,dev'
      CONSUL__SERVER__PORT: 80
      RUN__MODE: local
      PROXY__HOST: http://************:9090/v1/gateway/proxy

      CACHE__DEFAULT__HOST: ************
      CACHE__DEFAULT__PORT: 6379
      CACHE__DEFAULT__PASSWORD: cae0f7fcf1

      CAT__AGENT__TYPE: Socket
      CAT__AGENT__HOST: ************
      CAT__AGENT__PORT: 2280
      EXCEPTION__ALARM: 'true'
      CRONTAB__ENABLE: 'off'
      SUPERVISOR__ENABLE: 'off'
      COMPOSER_UPDATE: 'off'
      SKIP_COMPOSER: 'off'
      PUID: 'root'
      SKIP_CHOWN: 'root'
      PHPENV: 'local'
    volumes:
    - $PWD:/data/web:rw
    - $PWD/conf/nginx/conf/vhosts:/etc/nginx/sites-available