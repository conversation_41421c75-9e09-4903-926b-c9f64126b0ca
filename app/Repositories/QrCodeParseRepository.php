<?php


namespace App\Repositories;


use App\Http\Defines\CommonError;
use App\Servitization\Adapter;
use RuntimeException;
use Throwable;
use function App;


class QrCodeParseRepository extends EloquentRepository
{
    /**
     * OA解析三方平台付款二维码
     * @param string $qrCode 二维码字符串
     * @param string $stationId 站点ID
     * @param array $checkData 验证数据,例：['rules' => ['xxx' => 'in'], 'messages' => ['xxx.in' => '']]
     * @param bool $throwException 验证失败时是否直接抛出异常
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/20 4:36 下午
     */
    public static function oa(string $qrCode, string $stationId, array $checkData = [],
                              bool $throwException = true): array
    {
        try {

            $responseData = (new Adapter())->parseQrCode([
                'qr_code'    => $qrCode,
                'station_id' => $stationId,
            ]);
            self::checkArrayField($responseData, $checkData, $throwException);
        } catch (Throwable $exception) {

            if ($exception->getCode() == 5000011) {

                throw new RuntimeException("", CommonError::CARD_PARSE_FAILED);
            }

            if ($exception->getCode() == 5000021) {

                throw new RuntimeException("", CommonError::QR_CODE_INVALID);
            }

            if ($exception->getCode() == 5000999) {

                throw new RuntimeException("三方提示：" . $exception->getMessage(),
                    $exception->getCode());
            }

            throw new RuntimeException("", CommonError::SYSTEM_ERROR);
        }


        if (!isset($responseData['card_no'])) {

            return [];
        }

        return $responseData;
    }

    /**
     * 解析自有平台司机付款二维码
     * @param string $qrCode 二维码字符串
     * @return array
     * @throws Throwable
     * @since 2020/8/20 4:36 下午
     * <AUTHOR> <<EMAIL>>
     */
    public static function fossUser(string $qrCode): array
    {
        $parseResult = self::parseQrCode(explode('_', $qrCode)[1] ?? '');
        $parseInfos = explode('_', $parseResult);
        return [
            "card_no"        => $parseInfos[0],
            "qr_code_source" => $parseInfos[1] ?? '',
            "order_no"       => $parseInfos[2] ?? '',
            "truck_no"       => $parseInfos[3] ?? '',
            "ocr_truck_no_id"       => $parseInfos[4] ?? '',
        ];
    }

    /**
     * Parse QR code to get self-operated driver
     * @param string $encodeStr
     * @return string
     * <AUTHOR> <<EMAIL>>
     * @since 2021/2/1 7:10 下午
     */
    public static function parseQrCode(string $encodeStr): string
    {
        $key = substr($encodeStr, 0, 2);
        $tex = substr($encodeStr, 2);
        $verity_str = substr($tex, 0, 3);
        $tex = substr($tex, 3);
        //完整性验证失败
        if ($verity_str != substr(md5($tex), 0, 3)) {

            throw new RuntimeException("", CommonError::QR_CODE_EXPIRED);
        }

        $tex = base64_decode($tex);
        $textLen = strlen($tex);
        $resultStr = "";
        $rand_key = md5($key);
        for ($i = 0; $i < $textLen; $i++) {

            $resultStr .= $tex{$i} ^ $rand_key{$i % 32};
        }

        $de_arr = explode('|', $resultStr);
        // 只有线上环境才验证二维码有效期
        if (in_array(App()->environment(), ["prod", "pro", "pre"])) {

            if (time() - $de_arr[1] > 60) {

                throw new RuntimeException("", CommonError::QR_CODE_EXPIRED);
            }
        }
        return $de_arr[0];
    }
}