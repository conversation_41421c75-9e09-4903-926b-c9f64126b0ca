replicaCount: 1

env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=test,product=petroleum-card,appid=foss-order-quene

image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/foss-order:c992ec0b.2

nameOverride: "foss-order-queue"
fullnameOverride: "foss-order-queue"

configmap:
  envdata:
    APPLICATION__NAME: "foss-order-queue"
    PHPENV: "local"
    APP__NAME: "foss-order"
    APP__ENV: "test"
    CRONTAB__ENABLE: "on"
    RUN_SCRIPTS: "1"
    SUPERVISOR__ENABLE: "on"
    PHP_ERRORS_STDERR: "1"
    PHP_MEM_LIMIT: "1024"
    PHP_POST_MAX_SIZE: "2000"
    SERVICE_GROUP: "queue"
resources:
  limits:
    cpu: 300m
    memory: 1000Mi
  requests:
    cpu: 200m
    memory: 500Mi
