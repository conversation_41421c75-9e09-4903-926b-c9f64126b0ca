<?php

namespace App\Providers;

use App\Http\Defines\FossRuleDefine;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;

class ValidateServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        Validator::extend('money', function($attribute, $value, $parameters){
            if(preg_match('/^(0|[1-9]\d{0,9})(\.\d{1,2})?$/', $value)) {
                return true;
            }
            return false;

        });

        Validator::extend('range_price', function($attribute, $value, $parameters){
            if( $value >= FossRuleDefine::MIN_PRICE && $value <= FossRuleDefine::MAX_PRICE ) {
                return true;
            }
            return false;

        });

        Validator::extend('oil_com', function($attribute, $value, $parameters){
            if(in_array($value, [20, 21])) {
                return true;
            }
            return false;

        });
        //账号
        Validator::extend('vice_no', function($attribute, $value, $parameters){
            if(is_numeric($value)) {
                return true;
            }
            return false;
        });
        //G7能源账户密码
        Validator::extend('vice_password', function($attribute, $value, $parameters){
            if((preg_match('/^[0-9]{6}$/', $value))) {
                return true;
            }
            return false;
        });
        //支付限制
        Validator::extend('paylimit', function($attribute, $value, $parameters){
            return in_array($value, [0, 1]);
        });
        Validator::extend('mobile', function($attribute, $value, $parameters){
            if((preg_match("/^1\d{10}$/", $value))) {
                return true;
            }
            return false;
        });
        Validator::extend('card_status', function($attribute, $value, $parameters){
            return in_array($value, [1,2,3,4,5]);
        });
        Validator::extend('card_level', function($attribute, $value, $parameters){
            return in_array($value, [1,2]);
        });

        // 手机号
        Validator::extend('phone_no', function ($attribute, $value, $parameters) {
            if (preg_match("/^1[3456789]\d{9}$/", $value)) {
                return true;
            }
            return false;
        });
        Validator::extend('plate_number', function ($attribute, $value, $parameters) {
            if (preg_match("/^[a-zA-Z0-9\-#\x{4e00}-\x{9fa5}]{5,30}$/u", $value)) {
                return true;
            }
            return false;
        });
    }
}
