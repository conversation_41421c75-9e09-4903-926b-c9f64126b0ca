#!/bin/bash

#1.初始化env配置

echo "当前环境:"$APP__ENV

if [ $APP__ENV = "prod" ]; then
cp .env.pro .env
elif [ $APP__ENV = "test" ]; then
cp .env.test .env
elif [ $APP__ENV = "demo" ]; then
cp .env.pre .env
else
cp .env.dev .env
fi
echo "完成环境初始化"

#2.monitor 发布脚本

echo "当前环境:"$APP_ENV
if [ "$APP__ENV" = "prod" ]; then
    fileName=monitor.linux.pro.last.tar.gz
else
    fileName=monitor.linux.test.last.tar.gz
fi

#log存放目录
LOGPATH=/data/logs/monitor
#用户组要与php用户一致
USER=nginx
GROUP=nginx
#sock路径（固定不可修改）
SOCK=/var/tmp/monitor.sock

#卸载
#supervisorctl stop monitor
rm -rf /data/logs/monitor

cd /data/web/monitor || exit
echo "解压文件"
cd /data/web/monitor && tar zxvf $fileName
if [ ! -d $LOGPATH ]; then
    mkdir -p $LOGPATH
fi
chown -R $USER:$GROUP $LOGPATH
if [ ! -f $SOCK ]; then
    touch $SOCK
fi

chown -R $USER:$GROUP $SOCK

echo "完成monitor配置"

#3.supervisor 配置


echo "完成supervisor配置"

#4.定时清理日志
echo "59 23 */3 * * echo ''>/data/log/foss-order/laravel.log && chown nginx:nginx /data/log/foss-order/laravel.log"  >> /var/spool/cron/crontabs/root
killall crond;
crond;
echo "完成定时清理日志cron配置"