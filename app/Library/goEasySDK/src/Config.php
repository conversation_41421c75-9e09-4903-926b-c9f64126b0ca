<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-24
 * Time: 下午4:59
 */

namespace GoEasySDK;


class Config
{
	static public $config;
	
	static public function getConfig()
	{
		if (!self::$config) {
			if (defined('GOEASY_SDK_CONFIG')) {
				if (!file_exists(GOS_SDK_CONFIG)) {
					throw new \RuntimeException('GOEASY_SDK_CONFIG 文件不存在', 2);
				}
				self::$config = require_once GOS_SDK_CONFIG;
			} else {
				self::$config = require_once __DIR__ . DIRECTORY_SEPARATOR . 'config/goEasy.php';
			}
		}
		
		return self::$config;
	}
	
	public static function setConfig($config = [])
	{
		$defaultConfig = self::getConfig();
		self::$config = array_merge($defaultConfig, $config);
	}
}