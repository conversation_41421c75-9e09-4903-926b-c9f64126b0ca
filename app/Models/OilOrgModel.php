<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;


class OilOrgModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_org';

    protected $guarded = ["id"];

    protected $fillable = ['id', 'orgcode', "org_name"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 批量获取机构信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array|\Illuminate\Support\Collection
     * @noinspection PhpFullyQualifiedNameUsageInspection
     */
    public function getBatchOrgByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}