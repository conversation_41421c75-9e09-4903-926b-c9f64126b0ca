<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\CardViceBillModel;
use App\Models\OilAccountAssignDetails;
use App\Models\OilAccountMoneyTransfer;

class CardViceBillRepository extends \App\Repositories\EloquentRepository
{
    protected $cardViceBillModel;
    protected $accountAssignDetail;
    protected $transferModel;
    public function __construct(CardViceBillModel $cardViceBillModel,OilAccountAssignDetails $accountAssignDetails,OilAccountMoneyTransfer $oilAccountMoneyTransfer)
    {
        $this->cardViceBillModel = $cardViceBillModel;
        $this->accountAssignDetail = $accountAssignDetails;
        $this->transferModel = $oilAccountMoneyTransfer;
    }

    public function getBillList(array $condition)
    {
        //$this->debugstart('mysql_foss');
        $obj = $this->cardViceBillModel->where("trade_time",">=",$condition['bill_time'])
            ->where("trade_time","<",$condition['bill_end_time'])
            ->whereIn('oil_com',[20,21,30])
            ->whereIn('mobile',$condition['driver_phone']);
            #->whereRaw("if (oil_com = 21,res_type not in (20,30),1=1)");


        if(isset($condition['viceNos']) && is_array($condition['viceNos'])){
            $obj->whereIn("card_no",$condition['viceNos']);
        }

        if(isset($condition['card_no']) && $condition['card_no'] != ''){
            $obj->where("card_no",$condition['card_no']);
        }
        if(isset($condition['account_type']) && $condition['account_type'] != ''){
            if($condition['account_type'] == 2){
                $obj->where("pay_type",20);
            }else {
                $obj->where("pay_type", 10);
            }
        }
        if(isset($condition['type']) && $condition['type']){
            $obj->where("res_type",$condition['type']);
        }

        $obj = $obj->select(["card_no","res_type","res_id","amount","pay_type","trade_time",'trade_desc','oil_com'])
            ->orderBy('trade_time', 'desc')
            ->orderBy('createtime', 'desc');
        if(isset($condition['inCome']) && $condition['inCome'] == 1){
            return $obj->whereIn("res_type",[15,20,50,60,102])->sum("amount");
        }elseif(isset($condition['outCome']) && $condition['outCome'] == 1){
            return $obj->whereIn("res_type",[10,30,40,101])->sum("amount");
        }
        $data = $obj->paginate($condition['limit'],['*'],'page',$condition['page']);
        //$sql = $this->debugend('mysql_foss');
        //print_r($sql);
        return $data;
    }

    //获取分配详情
    public function getAssignDetails(array $condition)
    {
        //$this->debugstart('mysql_foss');
        $obj = $this->accountAssignDetail
            ->leftJoin("oil_account_assign",'oil_account_assign.id','=','oil_account_assign_details.assign_id')
            ->leftJoin("oil_org",'oil_org.id','=','oil_account_assign.org_id')
            ->select(['oil_account_assign.no',"oil_account_assign.complete_time","oil_account_assign_details.remark_work","oil_org.org_name","oil_account_assign_details.assign_money","oil_account_assign_details.actual_money"]);
        $data = $obj->where("oil_account_assign_details.id",$condition['detail_id'])->first();
        //$sql = $this->debugend('mysql_foss');
        //print_r($sql);
        return $data;
    }

    //获取划拨详情
    public function getTransferDetails(array $condition)
    {
        return $this->transferModel->withCondition($condition)->select(['no',"from_phone","from_orgname","from_account_no","into_account_no","into_phone","into_orgname","money","remark_work","createtime"])->first();
    }
}