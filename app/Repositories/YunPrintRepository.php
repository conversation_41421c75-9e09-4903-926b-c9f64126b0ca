<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\YunPrintLogModel;

class YunPrintRepository extends \App\Repositories\EloquentRepository
{
    protected $printModel;

    public function __construct(
        YunPrintLogModel $print
    )
    {
        $this->printModel = $print;
    }

    /**
     * 单个获取信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneOrgByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->printModel->getOneOrgByParams($whereParams, $select, $toArray);
    }

    public function insertOrder(array $insertArray)
    {
        return $this->printModel->insertOrder($insertArray);
    }
}