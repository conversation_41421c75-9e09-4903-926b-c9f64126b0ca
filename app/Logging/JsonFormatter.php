<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/29
 * Time: 12:57
 */

namespace App\Logging;

use Components\Helper\Docker;
use Monolog\Formatter\JsonFormatter as BaseJsonFormatter;

class JsonFormatter extends BaseJsonFormatter
{
    public function format(array $record)
    {
        if (isset($record['cat']) == false) {

            $record['cat'] = 'php';
        }
        $record = array_merge([
            'app_name'  => env('APP_NAME'),
            'trace'     => isset($_SERVER['HTTP_TRACE']) ? intval($_SERVER['HTTP_TRACE']) + 1 : (isset($_REQUEST['trace']) ? intval($_REQUEST['trace']) + 1 : 0),
            'timestamp' => date('Y-m-d H:i:s'),
            'hostname'  => Docker::getHostName(),
        ], $record);
        global $rootSpan;
        $record['request_id'] = $rootSpan ? $rootSpan->getTraceId() : '';
        return $this->toJson($this->normalize($record), true) . ($this->appendNewline ? "\n" : '');
    }
}

