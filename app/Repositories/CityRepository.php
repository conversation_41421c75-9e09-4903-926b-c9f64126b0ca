<?php


namespace App\Repositories;


use App\Models\CityModel;

class CityRepository
{
    protected $cityModel;

    public function __construct
    (
        CityModel $cityModel
    )
    {
        $this->cityModel = $cityModel;
    }


    /**
     * 获取省、市字典
     *
     * @param bool $toArray
     * @return array
     */
    public function getProvinceCityDict($toArray = true)
    {

        $select = ['level', 'city_code', 'city_name'];

        $cache_city_key = 'cache_city_getProvinceCityDict';
        if($toArray) {
            $cache_city_key.='-true';
        }
        $redisConn = app("redis");

        $cache_info = $redisConn->get($cache_city_key);
        if($cache_info) {

            return unserialize($cache_info);

        } else {

            $info = $this->cityModel->getBatchCityByParams(['level' => [2,3,4]], $select, $toArray);

            $redisConn->set($cache_city_key,serialize($info), 'ex',86400*7, 'nx');

            return  $info;
        }
    }
}