<?php

namespace App\Console\Commands;

use App\Models\GasHistoryApproveModel;
use Illuminate\Console\Command;

class addProvinceCityToApprove extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:province_city_code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清洗交易流水表省、市编码';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $result = GasHistoryApproveModel::select(['*'])->get()->toArray();
        echo '开始处理'."\n";
        foreach ($result as $item) {
            if (empty($item['province_code']) && empty($item['city_code'])) {
                $mirror = json_decode($item['approve_mirror'], true);
                GasHistoryApproveModel::from('gas_history_approve')->where(['id' => $item['id']])->update(
                    [
                        'province_code' => $mirror['province_code'],
                        'city_code' => $mirror['city_code'],
                    ]
                );
                echo 'id:'.$item['id'].',province_code:'.$mirror['province_code'].','.$mirror['city_code']."\n";
            }
        }
        echo '处理完成'."\n";
    }
}
