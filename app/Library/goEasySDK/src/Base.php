<?php


namespace GoEasySDK;


use App\Library\Helper\Common;
use GuzzleHttp\Client;

class Base
{
	protected $subscribe_kay = null;
	
	protected $common_kay = null;
	
	protected $url = null;
	
	public function __construct()
	{
		$config = require 'config/goEasy.php';
		
		$this->url = $config['url'];
		$this->common_kay = $config['common_key'];
	}
	
	public function request($methodType, $path, $postData)
	{
		$postData['appkey'] = $this->common_kay;
		try {
			$client = new Client(
				[
					'base_uri' => $this->url,
					'timeout'  => 10,
                    'verify' => false,
				]
			);
		} catch (\Exception $exception) {
            Common::log('error',"消息推送：".strval($exception));
			throw new \RuntimeException($exception->getMessage(), $exception->getCode());
		}
		
		if(strtoupper($methodType) == 'POST'){
			$requestParams = [
				'form_params' => $postData,
			];
		}else{
			$requestParams = [
				'query' => $postData,
			];
		}
		$response = $client->request($methodType, $path, $requestParams);
        Common::log('error',"消息推送Response：".var_export($response,true));

        if ($response->getStatusCode() !== 200) {
			throw new \RuntimeException($response->getBody()->getContents(), $response->getStatusCode());
		}
		
		$jsonData = $response->getBody()->getContents();
        Common::log('error',"消息推送结果：".var_export($jsonData,true));
		
		return \GuzzleHttp\json_decode($jsonData);
	}
}