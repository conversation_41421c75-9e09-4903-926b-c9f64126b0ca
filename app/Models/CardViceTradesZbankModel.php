<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class CardViceTradesZbankModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_card_vice_trades_zbank';

    protected $guarded = ["id"];

    protected $fillable = ['id','api_id','trade_api_id','vice_no',"oil_com","truck_no","trade_time","oil_name","trade_money","service_money",
        "trade_num","trade_place","trade_price"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function org()
    {
        return $this->belongsTo(OrgModel::class, 'gascode', 'orgcode');
    }

}