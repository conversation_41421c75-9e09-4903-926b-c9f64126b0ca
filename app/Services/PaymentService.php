<?php


namespace App\Services;


use App\Events\OrderPaySuccess;
use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Jobs\PushDataToOA;
use App\Library\Helper\Common;
use App\Library\Helper\NumberUtil;
use App\Library\Request;
use App\Models\CityModel;
use App\Models\CouponsModel;
use App\Models\DictModel;
use App\Models\HistoryModel;
use App\Models\OrderModel;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\StationRepository;
use App\Repositories\TripartiteCouponRepository;
use App\Servitization\FeiShu;
use App\Servitization\Foss;
use App\Servitization\FossApi;
use App\Servitization\FossUser;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class PaymentService
{
    protected $fossUser;
    protected $errorCodeService;
    protected $historyService;
    protected $messageService;
    protected $newOrderRepository;
    protected $numberUtil;
    protected $feishu;
    protected $fossApi;
    protected $redisService;
    protected $stationRepository;
    protected $cardService;
    protected $cardInfo = '';
    protected $orderExtRepository;
    protected $foss;
    protected $couponSrv;
    protected $refundSrv;

    protected $truckSrv;

    private $able_foss_refund = false;

    protected $tripartiteCouponRepository;
    private   $newOrderExtRepository;

    public function __construct
    (
        FossUser $fossUser,
        ErrorCodeService $errorCodeService,
        HistoryService $historyService,
        MessageService $messageService,
        NewOrderRepository $newOrderRepository,
        NumberUtil $numberUtil,
        FeiShu $feiShu,
        FossApi $fossApi,
        RedisService $redis,
        StationRepository $station,
        CardService $card,
        NewOrderExtRepository $ext,
        Foss $foss,
        CouponService $couponSrv,
        RefundService $refundSrv,
        TruckCardService $truckSrv,
        TripartiteCouponRepository $tripartiteCouponRepository,
        NewOrderExtRepository $newOrderExtRepository
    )
    {
        $this->fossUser = $fossUser;
        $this->errorCodeService = $errorCodeService;
        $this->historyService = $historyService;
        $this->messageService = $messageService;
        $this->newOrderRepository = $newOrderRepository;
        $this->numberUtil = $numberUtil;
        $this->feishu = $feiShu;
        $this->fossApi = $fossApi;
        $this->redisService = $redis;
        $this->stationRepository = $station;
        $this->cardService = $card;
        $this->orderExtRepository = $ext;
        $this->foss = $foss;
        $this->couponSrv = $couponSrv;
        $this->refundSrv = $refundSrv;
        $this->truckSrv = $truckSrv;
        $this->tripartiteCouponRepository = $tripartiteCouponRepository;
        $this->newOrderExtRepository = $newOrderExtRepository;
    }

    /**
     * 验密和支付
     *
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function checkPasswordAndPay($params, $orderInfo = [])
    {
        $cardNo = array_get($params, 'card_no', '');
        $orderId = array_get($params, 'order_id', '');
        $password = array_get($params, 'password', '');
        //卡车回调支付无需验密
        $kcCallBack = array_get($params, 'kcCallBack', 0);

        try {
            // 验账号
            $cardInfo = $this->fossUser->getCardInfoWithCurrAccount(['card_no' => $cardNo]);
            if (empty($cardInfo)) {
                throw new \Exception('验密失败:账号错误!', $this->errorCodeService->gotoPay(40320));
            }
            /*if (strcmp($cardInfo['ischeck'], 1)) {
                throw new \Exception('验密失败:当前G7能源账户不需要验密!', $this->errorCodeService->gotoPay(40321));
            }*/
            // 验订单
            if (empty($orderInfo)) {
                $orderInfo = $this->checkOrder($orderId, $cardNo);
            }

        } catch (\Exception $e) {
            if (in_array($orderInfo['order_channel'], [CardTradeConf::WMP_PAY_CODE, CardTradeConf::H5_PAY_CODE])) {
                //卡片冻结错误消息，通知司机端小程序
                $this->messageService->driverPayFail($cardNo, $orderId, $e->getMessage(), $e->getCode());
            }
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        // 发起验密
        $mirror = json_decode($orderInfo['mirror'], true);
        $data = [
            'order_id' => strval($orderInfo['order_id']),
            'station_name' => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
            'truck_no' => $orderInfo['truck_no'],
            'driver_phone' => $orderInfo['driver_phone'],
            'card_no' => $orderInfo['card_no'],
            'goods' => $mirror['goods'],
            'oil_price' => $orderInfo['oil_price'],
            'oil_num' => $orderInfo['oil_num'],
            'oil_money' => $orderInfo['oil_money'],
            'service_price' => $orderInfo['service_price'],
            'service_money' => $orderInfo['service_money'],
            'total_money' => round(bcadd($orderInfo['service_money'], $orderInfo['oil_money'], 3), 2),
            'update_time' => date("Y-m-d H:i:s", time()),
            'creator' => $orderInfo['creator'],
        ];
        /**
         * 验密
         */
        if ($cardInfo['ischeck'] == 1 && $kcCallBack != 1) {
            try {
                // 验密
                $this->fossUser->checkPassword(['vice_no' => $cardNo, 'password' => $password]);
            } catch (\Exception $e) {
                // G7能源账户锁定(锁卡)
                if ($e->getCode() == 3013102) {
                    $updateArray = [
                        'order_status' => OrderModel::FAIL_PAY,
                        'remark' => $e->getMessage(),
                        'updator' => '系统'
                    ];
                    $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
                    // 推送给PDA
                    $this->messageService->pushMsgToDoper($mirror['order_token'], [
                        'code' => 403,
                        'msg' => $e->getMessage(),
                        'data' => $data,
                    ]);
                    // 卡片锁定,推送司机端小程序
                    throw new \RuntimeException($e->getMessage(), CommonError::OVER_CARD_PWD);
                }
                // 非密码错误消息推送到PDA
                if (strcmp($e->getCode(), 3013101)) {
                    $this->messageService->pushMsgToDoper($mirror['order_token'], [
                        'code' => 403,
                        'msg' => $e->getMessage(),
                        'data' => $data,
                    ]);
                    throw $e;
                } else {
                    // 密码错误,推送到司机端小程序
                    throw new \RuntimeException($e->getMessage(), CommonError::ERROR_CARD_PWD);
                }
            }
        }

        /**
         * 扣款
         */
        try {
            // 消息推送
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 201,
                'msg' => '正在扣款中,请勿退出该页面'
            ]);
            //发起支付
            $orderInfo['card_password'] = $password;
            $orderInfo['original_order_id'] = $orderId;
            //txb 增加交易类型
            #$orderInfo['trade_from'] = array_get($params,"trade_from",201);
            $payRes = $this->fossPay($orderInfo, HistoryModel::COST_SUCCESS);

            // 消息推送
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 200,
                'msg' => '扣款成功',
                'data' => $data,
            ]);

            if (in_array($orderInfo['order_channel'], [CardTradeConf::WMP_PAY_CODE])) {
                $this->messageService->driverPaySuccess($cardNo, $orderId);
            }

            if ($orderInfo['order_channel'] == CardTradeConf::H5_PAY_CODE) {
                $this->messageService->pushMsg2H5([
                    'id' => $orderId,
                    'card_no' => $cardNo,
                    'mobile' => $orderInfo['driver_phone'],
                ], 2);
            }

            return $payRes;
        } catch (\Exception $e) {
            // 其他问题
            /*$updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark'       => $e->getMessage(),
                'updator'      => '系统'
            ];
            $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);*/
            // 失败消息
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 403,
                'msg' => $e->getMessage(),
                'data' => $data,
            ]);

            if (in_array($orderInfo['order_channel'], [CardTradeConf::WMP_PAY_CODE])) {
                $this->messageService->driverPayFail($cardNo, $orderId, $e->getMessage(), $e->getCode());
            }

            if ($orderInfo['order_channel'] == CardTradeConf::H5_PAY_CODE) {
                $this->messageService->pushMsg2H5([
                    'id' => $orderId,
                    'card_no' => $cardNo,
                    'mobile' => $orderInfo['driver_phone'],
                    'code' => $e->getCode(),
                    'msg' => $e->getMessage(),
                ], 3);
            }
            throw $e;
        }
    }

    //验证订单
    private function checkOrder($orderId, $cardNo = '')
    {
        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $orderId]);
        if (empty($orderInfo)) {
            throw new \Exception('验密失败:订单不存在!', $this->errorCodeService->gotoPay(40322));
        }
        if (!empty($cardNo) && strcmp($orderInfo['card_no'], $cardNo)) {
            throw new \Exception('验密失败:订单账号和验密G7能源账号不一致!', $this->errorCodeService->gotoPay(40323));
        }
        if (strcmp($orderInfo['order_status'], OrderModel::WAIT_PAY)) {
            throw new \Exception('验密失败:订单状态为[' . OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . ']不允许验密!', $this->errorCodeService->gotoPay(40324));
        }
        if (empty($orderInfo['mirror'])) {
            $orderInfo['mirror'] = json_encode($orderInfo['ext']);
        }
        return $orderInfo;
    }

    /**
     * 微信卡包验密后支付
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function cardBoxPayment($params, $orderInfo = [])
    {
        $cardNo = array_get($params, 'card_no', '');
        $orderId = array_get($params, 'order_id', '');
        $password = array_get($params, 'password', '');
        //卡车回调支付无需验密
        $kcCallBack = array_get($params, 'kcCallBack', 0);

        // 验账号
        $cardInfo = $this->fossUser->getCardInfoWithCurrAccount(['card_no' => $cardNo]);
        if (empty($cardInfo)) {
            throw new \Exception('验密失败:账号错误!', $this->errorCodeService->gotoPay(40320));
        }
        /*if (strcmp($cardInfo['ischeck'], 1)) {
            throw new \Exception('验密失败:当前G7能源账户不需要验密!', $this->errorCodeService->gotoPay(40321));
        }*/

        // 验订单
        if (empty($orderInfo)) {
            $orderInfo = $this->checkOrder($orderId, $cardNo);
        }
        /*$orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $orderId]);
        if (empty($orderInfo)) {
            throw new \Exception('验密失败:订单不存在!', $this->errorCodeService->gotoPay(40322));
        }
        if (strcmp($orderInfo['card_no'], $cardNo)) {
            throw new \Exception('验密失败:订单账号和验密G7能源账号不一致!', $this->errorCodeService->gotoPay(40323));
        }
        if (strcmp($orderInfo['order_status'], OrderModel::WAIT_PAY)) {
            throw new \Exception('支付失败:订单状态为['.OrderModel::$ORDER_STATUS[$orderInfo['order_status']].']不允许支付!', $this->errorCodeService->gotoPay(40324));
        }*/

        // 发起验密
        $mirror = json_decode($orderInfo['mirror'], true);
        $data = [
            'order_id' => strval($orderInfo['order_id']),
            'station_name' => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
            'truck_no' => $orderInfo['truck_no'],
            'driver_phone' => $orderInfo['driver_phone'],
            'card_no' => $orderInfo['card_no'],
            'goods' => $mirror['goods'],
            'oil_price' => $orderInfo['oil_price'],
            'oil_num' => $orderInfo['oil_num'],
            'oil_money' => $orderInfo['oil_money'],
            'service_price' => $orderInfo['service_price'],
            'service_money' => $orderInfo['service_money'],
            'total_money' => round(bcadd($orderInfo['service_money'], $orderInfo['oil_money'], 3), 2),
            'update_time' => date("Y-m-d H:i:s", time()),
            'creator' => $orderInfo['creator'],
        ];

        /**
         * 验密
         */
        if ($cardInfo['ischeck'] == 1 && $kcCallBack != 1) {
            try {
                // 验密
                $this->fossUser->checkPassword(['vice_no' => $cardNo, 'password' => $password]);
            } catch (\Exception $e) {
                // 锁账户
                if ($e->getCode() == 3013102) {
                    $updateArray = [
                        'order_status' => OrderModel::FAIL_PAY,
                        'remark' => $e->getMessage(),
                        'updator' => '系统'
                    ];
                    $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
                    // 推送给PDA
                    $this->messageService->pushMsgToDoper($mirror['order_token'], [
                        'code' => 403,
                        'msg' => $e->getMessage(),
                        'data' => $data,
                    ]);
                    if (in_array($orderInfo['order_channel'], [CardTradeConf::WECHAT_ON_LINE, CardTradeConf::WECHAT_TICKET])) {
                        throw new \Exception($e->getMessage(), 2);
                    } else {
                        // 锁卡
                        return ['s' => -1, 'm' => $e->getMessage()];
                    }
                }
                // 非密码错误消息推送到PDA
                if (strcmp($e->getCode(), 3013101)) {
                    $this->messageService->pushMsgToDoper($mirror['order_token'], [
                        'code' => 403,
                        'msg' => $e->getMessage(),
                        'data' => $data,
                    ]);
                    throw new \Exception($e->getMessage(), 2);
                } else {
                    // 密码错误
                    if (in_array($orderInfo['order_channel'], [CardTradeConf::WECHAT_ON_LINE, CardTradeConf::WECHAT_TICKET])) {
                        throw new \Exception($e->getMessage(), 2);
                    } else {
                        preg_match('/\d/', $e->getMessage(), $matches);
                        return ['s' => -2, 'm' => $e->getMessage(), 'data' => ['leftNum' => empty($matches[0][0]) ? 0 : $matches[0][0]]];
                    }
                }
            }
        }

        /**
         * 扣款
         */
        try {
            // 消息推送
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 201,
                'msg' => '正在扣款中,请勿退出该页面'
            ]);
            //发起支付
            $orderInfo['card_password'] = $password;
            $orderInfo['original_order_id'] = $orderId;
            //txb 增加交易类型
            #$orderInfo['trade_from'] = array_get($params,"trade_from",101);
            $payRes = $this->fossPay($orderInfo, HistoryModel::COST_SUCCESS);

            // 消息推送
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 200,
                'msg' => '扣款成功',
                'data' => $data,
            ]);

            $payRes['s'] = 0;
            $payRes['m'] = '支付成功';
            return $payRes;
            //return ['s' => 0, 'm' => '支付成功', 'order_id' => $orderId];
        } catch (\Exception $e) {
            // 其他问题
            /*$updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark'       => $e->getMessage(),
                'updator'      => '系统'
            ];
            $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);*/
            // 失败消息
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 403,
                'msg' => $e->getMessage(),
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * OA扣款成功,通知FOSS扣款
     *
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function oaPay($params, $orderInfo = [])
    {
        $orderId = array_get($params, 'order_id', 0);
        $thirdOrderId = array_get($params, 'third_order_id', '');
        $code = array_get($params, 'code', 0);
        $msg = array_get($params, 'msg', '成功');

        /*if (empty($thirdOrderId)) {
            throw new \Exception('支付失败,三方订单号不能为空!', $this->errorCodeService->gotoPay(40301));
        }*/
        // 查询订单信息
        if (empty($orderInfo)) {
            $orderInfo = $this->checkOrder($orderId, '');
        }
        /*$orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $orderId]);
        $mirror = json_decode($orderInfo['mirror'], true);
        if (empty($orderInfo)) {
            throw new \Exception('支付失败,订单号不存在!', $this->errorCodeService->gotoPay(40302));
        }
        // 幂等校验
        if ($orderInfo['order_status'] != OrderModel::WAIT_PAY) {
            throw new \Exception('支付失败,订单状态为:'.OrderModel::$ORDER_STATUS[$orderInfo['order_status']].'不允许支付!', $this->errorCodeService->gotoPay(40303));
        }*/

        $mirror = json_decode($orderInfo['mirror'], true);
        $data = [
            'order_id' => strval($orderInfo['order_id']),
            'station_name' => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
            'truck_no' => $orderInfo['truck_no'],
            'driver_phone' => $orderInfo['driver_phone'],
            'card_no' => $orderInfo['card_no'],
            'goods' => $mirror['goods'],
            'oil_price' => $orderInfo['oil_price'],
            'oil_num' => $orderInfo['oil_num'],
            'oil_money' => $orderInfo['oil_money'],
            'service_price' => $orderInfo['service_price'],
            'service_money' => $orderInfo['service_money'],
            'total_money' => round(bcadd($orderInfo['service_money'], $orderInfo['oil_money'], 3), 2),
            'update_time' => date("Y-m-d H:i:s", time()),
            'creator' => $orderInfo['creator'],
        ];

        // 消息推送
        if (!empty($mirror['order_token'])) {
            $this->messageService->pushMsgToDoper($mirror['order_token'], [
                'code' => 201,
                'msg' => '正在扣款中,请勿退出该页面'
            ]);
        }

        try {
            if (!empty($code)) {
                throw new \Exception($msg, $code);
            }
            $orderInfo['remark'] = $msg;
            $orderInfo['card_password'] = '';
            $orderInfo['third_order_id'] = $thirdOrderId;
            $logType = HistoryModel::COST_SUCCESS;
            if (in_array($orderInfo['trade_mode'], CardTradeConf::$trade_mode_bl)) {
                $orderInfo['oil_time'] = $mirror['oil_time'];
                $logType = HistoryModel::COST_SUCCESS_ADD;
            }
            $orderInfo['original_order_id'] = $orderId;
            $orderInfo['pay_extend_info'] = $params['extend_info'] ?? [];
            $payRes = $this->fossPay($orderInfo, $logType, $mirror['org_name']);

            // 消息推送
            if (!empty($mirror['order_token'])) {
                $this->messageService->pushMsgToDoper($mirror['order_token'], [
                    'code' => 200,
                    'msg' => '扣款成功',
                    'data' => $data,
                ]);
            }
            return $payRes;
        } catch (\Exception $e) {
            /*$updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark'       => $msg,
                'updator'      => $mirror['org_name'],
            ];
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);*/
            // 飞书报警
            $this->feishu->sendExceptionToFeiShu(new \Exception($msg, $code));
            // 失败消息
            if (!empty($mirror['order_token'])) {
                $this->messageService->pushMsgToDoper($mirror['order_token'], [
                    'code' => 403,
                    'msg' => $e->getMessage(),
                    'data' => $data
                ]);
            }
            throw $e;
        }
    }

    /**
     * 订单支付
     * @param array $params
     * @param string $payChannel
     * @return array|bool|mixed
     * @throws
     */
    public function finishOrder($params = [], $payChannel = 'G7')
    {
        try {
            $orderId = array_get($params, 'order_id', 0);
            $thirdOrderId = array_get($params, 'third_order_id', '');
            $password = array_get($params, 'password', '');

            if (empty($orderId)) {
                throw new \Exception('订单号不合法!', $this->errorCodeService->gotoPay(40302));
            }

            $result = $this->redisService->tryGetLock("pay-" . $orderId, 1, 15);
            if (!$result) {
                throw new \Exception('支付中，请稍后重试!', $this->errorCodeService->gotoPay(40303));
            }

            // 查询订单信息
            $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $orderId]);
            if (empty($orderInfo)) {
                throw new \Exception('支付失败,订单号不存在!', $this->errorCodeService->gotoPay(40302));
            }

            // 订单状态验证
            if ($orderInfo['order_status'] != OrderModel::WAIT_PAY) {
                switch ($orderInfo['order_status']) {
                    case OrderModel::SUCCESS_PAY:
                        // 支持OA幂等支付
                        if ($payChannel == 'OA') {
                            return [];
                        }
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_PAID], CommonError::ORDER_PAID);
                    case OrderModel::FAIL_PAY:
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_FAILED], CommonError::ORDER_FAILED);
                    case OrderModel::CANCEL:
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_CANCELED], CommonError::ORDER_CANCELED);
                    case OrderModel::REFUNDING:
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_REFUNDING], CommonError::ORDER_REFUNDING);
                    case OrderModel::REFUND:
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_REFUNDED], CommonError::ORDER_REFUNDED);
                    default:
                        throw new \Exception(CommonError::$codeMsg[CommonError::ORDER_EXCEPTION], CommonError::ORDER_EXCEPTION);
                }
            }

            $cardNo = array_get($orderInfo, "card_no", '');
            $pCardNo = array_get($params, "card_no", '');
            //针对无卡下单，支付时必须有卡号
            if (empty($cardNo)) {
                if (empty($pCardNo)) {
                    throw new \Exception('支付失败,支付账号不能为空!', $this->errorCodeService->gotoPay(40302));
                }
                $cardNo = $pCardNo;
                //todo 把卡信息更新到订单上
                $this->cardInfo = $this->cardService->checkCardAndGetArray($pCardNo, array_get($orderInfo, 'service_price', 0), array_get($orderInfo, 'oil_money', 0), array_get($orderInfo, 'service_money', 0), false);
                if (empty($this->cardInfo)) {
                    throw new \Exception('支付失败,卡号不存在!', $this->errorCodeService->gotoPay(40302));
                }
                $orderInfo['org_code'] = array_get($this->cardInfo, 'insert_card_array.org_code', '');
                $orderInfo['card_no'] = $pCardNo;
                $orderInfo['card_type'] = array_get($this->cardInfo, 'insert_card_array.card_type', '');
                $orderInfo['card_level'] = array_get($this->cardInfo, 'insert_card_array.card_level', '');
                $orderInfo['driver_name'] = array_get($this->cardInfo, 'extends_driver_info.driver_name', '');
                $orderInfo['driver_phone'] = array_get($this->cardInfo, 'extends_driver_info.driver_phone', '');
                $orderInfo['truck_no'] = array_get($this->cardInfo, 'extends_driver_info.truck_no', '');
            } else {
                if ($orderInfo['card_level'] == OrderModel::CAR_CARD && $orderInfo['driver_phone']) {
                    if (!$this->truckSrv->checkTruckCardTake($orderInfo['driver_phone'], $cardNo)) {
                        throw new \RuntimeException('您已不在车辆账户使用白名单内，请联系车队长、客服～', $this->errorCodeService->gotoPay(40333));
                    }
                }
            }

            $station = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $orderInfo['station_id']]);
            if (!$station) {
                throw new \Exception('站点不存在，请稍后重试!', $this->errorCodeService->gotoPay(40303));
            }
        } catch (\Exception $e) {

            if (in_array($orderInfo['order_channel'], [CardTradeConf::WMP_PAY_CODE])) {
                //卡片冻结错误消息，通知司机端小程序
                $this->messageService->driverPayFail($orderInfo['card_no'], $orderId, $e->getMessage(), $e->getCode(), $orderInfo['driver_phone']);
            }

            if ($orderInfo['order_channel'] == CardTradeConf::H5_PAY_CODE) {
                $this->messageService->pushMsg2H5([
                    'id' => $orderId,
                    'card_no' => $orderInfo['card_no'],
                    'mobile' => $orderInfo['driver_phone'],
                    'code' => $e->getCode(),
                    'msg' => $e->getMessage(),
                ], 3);
            }

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        } finally {
            $this->redisService->releaseLock("pay-" . $orderId, 1);
        }

        $payMentPcode = CommonDefine::getPayMentPcode();

        $resData = [];
        try {
            if (empty($orderInfo['mirror'])) {
                $orderInfo['mirror'] = json_encode($orderInfo['ext']);
            }
            $params['card_no'] = $cardNo;
            if (in_array($orderInfo['order_channel'], CardTradeConf::$normal_pay_channel)) {
                $resData = $this->checkPasswordAndPay($params, $orderInfo);
            } elseif (in_array($orderInfo['order_channel'], CardTradeConf::$card_box_pay_channel)) {
                $resData = $this->cardBoxPayment($params, $orderInfo);
                if ($resData['s'] < 0) {
                    return $resData;
                }
            } elseif (in_array($orderInfo['order_channel'], CardTradeConf::$oa_pay_channel)) {
                $resData = $this->oaPay($params, $orderInfo);
            } else {
//                throw new \Exception('支付失败，未知支付channel!', $this->errorCodeService->gotoPay(40313));
                // 补录 @todo
                $resData = $this->oaPay($params, $orderInfo);
            }

            $resData['showQrcode'] = 1; //不展示
            $resData['qr_code'] = "";
            if (in_array($station['pcode'], $payMentPcode)) {
                $resData['showQrcode'] = 2; //展示
            }
            $resData['isRewrite'] = 1; //是否打开新页
            if (in_array($station['pcode'], CommonDefine::skipHtmlPcode())) {
                $resData['isRewrite'] = 2;
            }

            //壳牌模式支付后，E站途不能出现核销二维码
            if (in_array($station['pcode'], CommonDefine::exceptPcode())) {
                $resData['showQrcode'] = 1;
            }

            //卡包特殊返回值
            $resData['s'] = 0;
            $resData['m'] = '支付成功';
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
            if ($this->able_foss_refund) {

                $updateArray = [
                    'order_status' => OrderModel::REFUNDING,
                    'remark' => "系统原因,自动退款" . $e->getCode(),
                    'updator' => '系统'
                ];
                $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);

                $refundParams = [
                    'order_id' => $orderId,
                    'third_order_id' => $thirdOrderId,
                    #'original_history_id' => $orderInfo['history_id'],
                ];
                //G7WALLET-2136
                $this->refundSrv->oaRefund($refundParams);
            }
            // 黄牛支付失败删除原单
            if ($orderInfo['trade_mode'] == CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP_HN) {
                $this->hnOrderPayFail($orderId);
            }
            throw new \RuntimeException($msg, $code);
        } finally {
            $this->redisService->releaseLock("pay-" . $orderId, 1);
        }

        $this->pushOrder2OA($orderId, $orderInfo);

        if ($orderInfo['card_level'] == OrderModel::CAR_CARD && $orderInfo['driver_phone']) {
            $this->truckSrv->dispatchTruckCardReturnJob($orderInfo['driver_phone'], $cardNo);
        }

        return $resData;
    }

    /**
     * 扣费成功后，推送OA
     */
    public function pushOrder2OA($orderId = "", $orderInfo = [])
    {
        $pcode = array_get($orderInfo, 'supplier_code', '');
        $pushData = $couponG7 = [];

        $coupon_alias = "";
        $coupon_flag = array_get($orderInfo, 'ext.g7_coupon_flag', '');
        $third_coupon_flag = array_get($orderInfo, 'ext.third_coupon_flag', '');
        if (!empty($coupon_flag)) {
            $couponG7 = $couponInfo = $this->couponSrv->checkCouponExist($coupon_flag);
            $coupon_alias = array_get($couponInfo, "coupon_type_alias", "");
            $third_coupon_flag = $couponG7->tripartite_voucher;
        }

        if (in_array($orderInfo['trade_mode'], CardTradeConf::$push2OA)) { //一键付站点推送OA订单数据
            $supplierPrice = array_get($orderInfo, 'ext.supplier_price', 0);

            $pushData['id'] = strval($orderInfo['order_id']);
            $pushData['drivertel'] = array_get($orderInfo, 'driver_phone', '');
            $pushData['oil_type_id'] = array_get($orderInfo, 'oil_type', '');
            $pushData['oil_name_id'] = array_get($orderInfo, 'oil_name', '');
            $pushData['oil_level_id'] = array_get($orderInfo, 'oil_level', '');
            $pushData['station_id'] = $orderInfo['station_id'];
            $pushData['vice_no'] = array_get($orderInfo, 'card_no', '');
            $pushData['trade_price'] = $orderInfo['oil_price'];
            $pushData['trade_money'] = $orderInfo['oil_money'];
            $pushData['trade_num'] = $orderInfo['oil_num'];
            $pushData['trade_place'] = array_get($orderInfo, 'ext.station_name', '');
            $pushData['trade_place_provice_name'] = array_get($orderInfo, 'ext.province_name', '');
            $pushData['trade_place_city_name'] = array_get($orderInfo, 'ext.city_name', '');
            $pushData['lng'] = array_get($orderInfo, 'ext.lng', '');
            $pushData['lat'] = array_get($orderInfo, 'ext.lat', '');
            $pushData['trade_time'] = array_get($orderInfo, 'ext.oil_time', '');
            $pushData['app_station_id'] = array_get($orderInfo, 'ext.app_station_id', '');
            $pushData['pcode'] = $pcode;
            $pushData['supplier_pay_price'] = $supplierPrice;
            $pushData['trades_no'] = array_get($orderInfo, 'trade_no', '');
            $pushData['truck_no'] = array_get($orderInfo, 'truck_no', '');
            $pushData['oil_name'] = array_get($orderInfo, 'ext.goods', '');
            $pushData['driver_name'] = array_get($orderInfo, 'driver_name', '');

            // 新增进价和进价金额
            $pushData['supplier_price'] = $supplierPrice;
            $pushData['supplier_money'] = round(bcmul($supplierPrice, $orderInfo['real_oil_num'], 3), 2);//进价计算金额;

            //$extends['price'] = $orderInfo['oil_price'];
//            $extends['price'] = array_get($orderInfo, 'ext.supplier_price', '');
            $extends['price'] = $supplierPrice;
            $extends['priceGun'] = array_get($orderInfo, 'ext.mac_price', '');
            $extends['amountGun'] = array_get($orderInfo, 'ext.mac_amount', '');
            $extends['gunNumber'] = array_get($orderInfo, 'ext.gun_number', '');
            //oA用driver_source是为了给司机下发长链接使用 1:卡包，2：小程序，订单中：driver_source是为了记录自营司机1，还是下游司机2，两者字段相同，意义不同
            $extends['driver_source'] = 2;
            if (in_array($orderInfo['order_channel'], [CardTradeConf::WECHAT_ON_LINE,CardTradeConf::WECHAT_PAY_CODE,CardTradeConf::WECHAT_TICKET])) {
                $extends['driver_source'] = 1;
            }
            $pushData['pushExtends'] = $extends;
            $pushData['coupon_alias'] = $coupon_alias;
            $pushData['tripartite_voucher'] = $third_coupon_flag;
            $pushData['oil_unit'] = $orderInfo['oil_unit'];
            $pushData['org_code'] = $orderInfo['org_code'];
            $pushData['org_name'] = array_get($orderInfo, 'ext.org_name', '');
            $pushData['oil_num'] = $orderInfo['oil_num'];
            $pushData['real_oil_num'] = $orderInfo['real_oil_num'];
            $pushData['province_code'] = $orderInfo['province_code'];
            $pushData['master_card_no'] = array_get($orderInfo, 'ext.master_card_no','');
            $pushData['supplementary_card_no'] = array_get($orderInfo, 'ext.supplementary_card_no','');
            $pushData['card_user_id'] = array_get($orderInfo, 'ext.card_user_id','');
            $message_type = "AUTONOMOUS_ORDER";
            $pushResult = $this->messageService->pushData2OA($pushData, $message_type);

            Common::log("info", "pushResult", $pushResult);
            if (!empty($coupon_flag)) {
                $voucher = $this->updateCouponInfo($couponG7, $pushResult, true);

                $modifyData = ['third_coupon_flag' => $voucher, 'update_time' => date("Y-m-d H:i:s")];
                $this->orderExtRepository->updateOrderExtByOrderId($orderInfo['order_id'], $modifyData);
            }
        } elseif (in_array($orderInfo['trade_mode'], CardTradeConf::$callBack2OA) || in_array($pcode, CommonDefine::getSpecialPcode()) || in_array($pcode, CommonDefine::getPcodeList())) {
            //todo 需要确定在什么情况下推送OA
            $pushData['order_id'] = $orderId;
            $message_type = "PAY_SUCCESS";
            // 异步推送OA
            if (in_array($orderInfo['trade_mode'], [CardTradeConf::TRADE_MODE_SYZ_ZYS_SMP, CardTradeConf::TRADE_MODE_SYZ_XYS_SMP,])) {
                dispatch(new PushDataToOA(['msy_type' => $message_type, 'data' => $pushData]))->delay(1)->onQueue('push-oa-queue');
            } else {
                $this->messageService->pushData2OA($pushData, $message_type);
            }
        }
        if ($orderInfo['order_flag'] == CardTradeConf::OPEN_API_FLAG and in_array($orderInfo['trade_mode'],
                CardTradeConf::$trade_mode_need_write_off)) {
            // 如果订单是开放平台客户的且需要核销一类的订单，推送客户核销结果
            $this->messageService::createKafkaMessage(json_encode([
                'order_no' => (string)$orderInfo['order_id'],
                'status' => 1,
                'reason' => '核销成功',
                'orgcode' => $orderInfo['ext']['open_api_org_code'],
            ]));
        }
    }

    /**
     * 更新券数据
     * @param $couponG7 CouponsModel
     * @param $pushResult
     * @param $enableTransaction
     * @return string
     */
    public function updateCouponInfo($couponG7, $pushResult, $enableTransaction=false)
    {
        $updateData = [];

        $tripartiteVoucher = $start = $end = "";

        if ($couponG7->pcode == CouponDefine::getHeBeiZSYPCode()) {

            $couponInfo = array_get($pushResult, "coupons", []);
            $updateData['distribute_batch_no'] = array_get($pushResult, "batchno", "");
            if (count($couponInfo) >= 1) {
                $oneCoupon = $couponInfo[0];
                $tripartiteVoucher = array_get($oneCoupon, "voucher", "");;

                $updateData['distribute_time'] = array_get($oneCoupon, 'createTime', "");
                $updateData['tripartite_img'] = array_get($oneCoupon, 'img', "");
                $updateData['instructions'] = array_get($oneCoupon, 'declare', "");

                $start = array_get($oneCoupon, 'start', "");
                $end = array_get($oneCoupon, 'end', "");
            }

        } elseif ($couponG7->pcode == CouponDefine::getHuBeiZSYPCode()) {
            $tripartiteVoucher = $couponG7->tripartite_voucher;
            $couponInfo = array_get($pushResult, "rows", []);
            $start = $couponG7->start_time;
            $end = $couponG7->end_time;
            if (count($couponInfo) >= 1) {
                $tripartiteVoucher = $couponInfo[0]['voucher'];
                $start = $couponInfo[0]['start'] . ' 00:00:00';
                $end = $couponInfo[0]['end'] . ' 23:59:59';
            }
        } elseif ($couponG7->pcode == CouponDefine::getShPCode()) {
            $tripartiteVoucher = array_get($pushResult, 'transaction_id', '');
            $start = $couponG7->start_time;
            $end = $couponG7->end_time;
        }

        $updateData['tripartite_voucher']   = $tripartiteVoucher;
        $updateData['start_time']           = $start;
        $updateData['end_time']             = $end;
        $updateData['tripartite_status']    = CouponDefine::NO_CHARGE_OFF;
        $updateData['status']               = CouponDefine::NO_CHARGE_OFF;
        $this->couponSrv->updateCoupon($couponG7->voucher, $updateData, $enableTransaction);
        $this->tripartiteCouponRepository->updateOrCreate([
            'tripartite_voucher' => $tripartiteVoucher,
            'pcode' => $couponG7->pcode,
        ], [
            "voucher" => $couponG7->voucher,
            'coupon_type_alias' => $couponG7->coupon_type_alias,
            'start_time' => $start,
            'end_time' => $end,
            'tripartite_voucher' => $tripartiteVoucher,
            'distribute_time' => $updateData['distribute_time'] ?? null,
            'tripartite_status' => CouponDefine::NO_CHARGE_OFF,
            'pcode' => $couponG7->pcode,
            'updated_time' => date('Y-m-d H:i:s'),
        ]);

        return $tripartiteVoucher;
    }

    /**
     * Foss 退款接口
     * @param $refundParams
     * @throws \Exception
     */
    public function fossRefund($refundParams)
    {
        try {
            $this->fossUser->cancelTrade($refundParams);
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
            // 飞书报警
            $this->feishu->sendExceptionToFeiShu("扣费异常，发起退款是吧" . $e->getMessage());
            throw new \Exception($msg, $code);
        }
    }

    /**
     * FOSS扣款
     *
     * @param $params [orderInfo]
     * @param $logType
     * @param string $updator
     * @return array
     * @throws \Exception
     * $isTrans 1：开启事务，0：无需开启 防止事务嵌套
     */
    public function fossPay($params, $logType, $updator = '系统', $isTrans = 1)
    {
        try {
            $orderMirror = json_decode($params['mirror'], true);
            // 三方订单手机号存在加密情况,foss验证手机号合法性,如果是不合法的手机号,就不给foss
            $driverPhone = '';
            if (!empty($this->numberUtil->pregPhoneNo($params['driver_phone']))) {
                $driverPhone = $params['driver_phone'];
            }
            if ($isTrans == 1) {
                DB::connection('mysql_gas')->beginTransaction();
            }
            if ($params['order_sale_type'] == OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL and
                $logType == HistoryModel::COST_SUCCESS) {
                $logType = HistoryModel::COST_RESERVATION_REFUEL;
            }
            // foss扣费
            $needParams = [
                'order_no' => $params['order_id'],
                'card_no' => $params['card_no'],
                'sale_type' => '现金消费', // 和gas保持同步
                'unit' => 1, // foss 1代表按金额加油 2代表按升加油
                'station_id' => $params['station_id'],
                'station_name' => $orderMirror['station_name'],
                'station_code' => $params['station_code'],
                'pay_money' => $params['oil_money'], // 不带服务费
                'pay_unit_price' => $params['oil_price'],
                'service_money' => $params['service_money'],
                'oil_num' => $params['oil_num'],
                'third_order_no' => empty($params['third_order_id']) ? $params['order_id'] : $params['third_order_id'],
                'oil_name' => $orderMirror['goods'],
                'oil_type_name' => $orderMirror['oil_name_name'],
                'trade_place' => $orderMirror['station_name'], //油站名称
                'trade_address' => $orderMirror['station_address'], //油站地址
                'oil_time' => $params['create_time'] ?? date('Y-m-d H:i:s'),
                'pcode' => $params['supplier_code'],
                'orgcode' => $params['org_code'],
                'province_code' => $params['province_code'],
                'province_name' => $orderMirror['province_name'],
                'city_code' => $params['city_code'],
                'city_name' => $orderMirror['city_name'],
                'open_check_password' => empty(strcmp($logType, HistoryModel::COST_SUCCESS_ADD)) ? 0 : 1, // 补录不需要验密
                'card_password' => $params['card_password'],
                'imgurl' => '', // PDA加油手工签名地址
                'truck_no' => $params['truck_no'],
                'drivername' => $params['driver_name'],
                'drivertel' => $driverPhone,
                'rebate_grade' => $orderMirror['rebate_grade'],
                'xpcode_pay_price' => $orderMirror['supplier_price'], // 站点应收(进价)
                'xpcode_pay_money' => (string)round(bcmul($orderMirror['supplier_price'], $params['real_oil_num'], 3), 2),
                'truename' => $params['creator'],
                'remark' => $params['remark'] ?? '',
                'trade_from' => $params['trade_from'] ?? null,
                'real_oil_num' => $params['real_oil_num'],
                'mac_price'         => $orderMirror['mac_price'] ?? 0,
                'mac_amount'        => $orderMirror['mac_amount'] ?? 0,
                'price_id'        => $orderMirror['price_id'] ?? '',
                'original_order_id' => $params['original_order_id'] ?? 0,
                'document_type' => CardTradeConf::$history_type_to_foss_mapping[$logType] ?? 0,
                'order_type' => $params['order_sale_type'] ?? OrderModel::ORDER_SALE_TYPE_REFUEL,
                'ocr_truck_no_url' => $orderMirror['ocr_truck_no_url'] ?? '',
                'pay_ext_info' => $params['pay_extend_info'] ?? [],
            ];

            // 补录及异常订单修改特殊处理
            if (in_array($logType, [HistoryModel::COST_SUCCESS_ADD, HistoryModel::COST_ABNORMAL]) && !empty($params['oil_time'])) {
                $needParams['oil_time'] = $params['oil_time'];
            }

            $needParams['trade_from'] = array_get($params, "order_channel", 101);
            $fossPayResult = $this->foss->tradePay($needParams);
            if (!isset($fossPayResult['pay_status'])) {
                throw new \Exception('FOSS扣款失败,请重试！！！', '9003006');
            }
            if ($fossPayResult['pay_status'] != 1) {
                throw new \Exception('FOSS扣款失败,请重试！', '9003001');
            }
            $this->able_foss_refund = true;
            $paymentId = $fossPayResult['payment_id'];
            $payTime = date('Y-m-d H:i:s'); // 需要foss回传支付时间
            // 生成交易流水
            if (isset($params['force_pay_time'])) {

                $payTime = $params['force_pay_time'];
            }
            $params['pay_time'] = $payTime;
            $params['payment_id'] = $paymentId;
            $params['trade_no'] = array_get($params, "trade_no", '');
            $params['old_id'] = $params['original_order_id'] ?? 0;
            $historyId = $this->historyService->addHistoryAfterPay($params, $logType);
            // 回写订单
            $updateArray = [
                'order_status' => OrderModel::SUCCESS_PAY,
                'payment_id' => $paymentId,
                'pay_time' => $payTime,
                'third_order_id' => $params['third_order_id'] ?? '',
                'history_id' => $historyId,
                'remark' => $params['remark'] ?? '',
                'updator' => $updator
            ];
            // 服务费以foss为准
            if (strcmp($params['service_money'], $fossPayResult['service_money'])) {
                $updateArray['service_money'] = $fossPayResult['service_money'];
                $updateArray['service_price'] = round(bcdiv($fossPayResult['service_money'], $params['oil_num'], 3), 2);
            }
            //支持无卡下单，成功后，更新到订单上卡数据
            if (!empty($this->cardInfo)) {
                $updateArray['org_code'] = array_get($this->cardInfo, 'insert_card_array.org_code', '');
                $updateArray['card_no'] = $params['card_no'];
                $updateArray['card_type'] = array_get($this->cardInfo, 'insert_card_array.card_type', '');
                $updateArray['card_level'] = array_get($this->cardInfo, 'insert_card_array.card_level', '');
                $updateArray['driver_name'] = array_get($this->cardInfo, 'extends_driver_info.driver_name', '');
                $updateArray['driver_phone'] = array_get($this->cardInfo, 'extends_driver_info.driver_phone', '');
                $updateArray['truck_no'] = array_get($this->cardInfo, 'extends_driver_info.truck_no', '');

                $extData['org_name'] = array_get($this->cardInfo, 'insert_card_mirror.org_name', '');
                $extData['account_type_name'] = array_get($this->cardInfo, 'insert_card_mirror.account_type_name', '');
                $extData['account_no'] = array_get($this->cardInfo, 'insert_card_mirror.account_no', '');
                $extData['account_name'] = array_get($this->cardInfo, 'insert_card_mirror.account_name', '');
                $extData['update_time'] = $payTime;
                $this->orderExtRepository->updateOrderExtByOrderId($params['order_id'], $extData);
            }
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);
            if ($isTrans == 1) {
                DB::connection('mysql_gas')->commit();
            }

            /**
             * 绑定支付成功事件
             * 小票机站点，打印小票
             */
            //G7WALLET-3942
            try {
                $doperId = !empty(Request::get('uid')) ? Request::get('uid') : array_get($orderMirror, 'operator_id', '');
                event(new OrderPaySuccess($params, $doperId));
                /*if (in_array($params['trade_mode'], [CardTradeConf::TRADE_MODE_ZYZ_XYS_SMP, CardTradeConf::TRADE_MODE_ZYZ_ZYS_SMP]) && !empty($doperId)) {
                    event(new OrderPaySuccess($params, $doperId));
                } else {

                    Log::warning("获取不到收款成功通知人的用户ID", [
                        'doperId'   => $doperId,
                        'orderInfo' => $params,
                    ]);
                }*/
            }catch (\Exception $e){
                Log::info('event-OrderPaySuccess-exception:' . json_encode(['orderInfo'=>$params,'msg'=>$e->getMessage()],JSON_UNESCAPED_UNICODE) . ',返回结果:' . json_encode(['code'=>$e->getCode()],JSON_UNESCAPED_UNICODE));
            }

            return [
                'order_id' => strval($params['order_id']),
                'payment_id' => $paymentId,
                'third_order_id' => $params['third_order_id'] ?? '',
                'history_id' => strval($historyId),
                'xpcode' => '20003JCP',
                'money' => $params['oil_money'],
            ];
        } catch (\Exception $e) {
            if ($isTrans == 1) {
                DB::connection('mysql_gas')->rollBack();
            }
            $updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark' => substr($e->getMessage(), 0, 200) . $e->getCode(),
                'updator' => $updator
            ];
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);
            if (!empty($orderMirror['g7_coupon_flag'])) {
                $this->couponSrv->updateCoupon($orderMirror['g7_coupon_flag'], [
                    'status' => CouponDefine::NO_USE,
                ]);
            }
            // 飞书报警
            $this->feishu->sendExceptionToFeiShu($e);
            throw $e;
        }
    }


    /**
     *
     * Gms异常修改
     *
     * @param $params
     * @param $logType
     * @param string $updator
     * @param $isTrans
     */
    public function fossCancelPay($params,$logType,$updator = '系统')
    {
        try {
            $orderMirror = json_decode($params['mirror'], true);
            // 三方订单手机号存在加密情况,foss验证手机号合法性,如果是不合法的手机号,就不给foss
            $driverPhone = '';
            if (!empty($this->numberUtil->pregPhoneNo($params['driver_phone']))) {
                $driverPhone = $params['driver_phone'];
            }
            if ($params['order_sale_type'] == OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL and
                $logType == HistoryModel::COST_SUCCESS) {
                $logType = HistoryModel::COST_RESERVATION_REFUEL;
            }
            // foss扣费
            $needParams = [
                'stream_no' => $params['order_id'],
                'id'       => $params['order_id'],
                'order_id' => $params['order_id'],
                'card_no' => $params['card_no'],
                'sale_type' => '现金消费', // 和gas保持同步
                'unit' => 1, // foss 1代表按金额加油 2代表按升加油
                'station_id' => $params['station_id'],
                'station_name' => $orderMirror['station_name'],
                'station_code' => $params['station_code'],
                'money' => $params['oil_money'], // 不带服务费
                'price' => $params['oil_price'],
                'service_money' => $params['service_money'],
                'oil_num' => $params['oil_num'],
                'client_order_id' => empty($params['third_order_id']) ? $params['order_id'] : $params['third_order_id'],
                'oil_name' => $orderMirror['goods'],
                'oil_name_val' => $orderMirror['oil_name_name'],
                'trade_place' => $orderMirror['station_name'], //油站名称
                'trade_address' => $orderMirror['station_address'], //油站地址
                'oil_time' => $params['create_time'] ?? date('Y-m-d H:i:s'),
                'pcode' => $params['supplier_code'],
                'orgcode' => $params['org_code'],
                'provice_code' => $params['province_code'],
                'provice_name' => $orderMirror['province_name'],
                'city_code' => $params['city_code'],
                'city_name' => $orderMirror['city_name'],
                'open_check_password' => empty(strcmp($logType, HistoryModel::COST_SUCCESS_ADD)) ? 0 : 1, // 补录不需要验密
                'card_password' => $params['card_password'],
                'imgurl' => '', // PDA加油手工签名地址
                'truck_no' => $params['truck_no'],
                'drivername' => $params['driver_name'],
                'drivertel' => $driverPhone,
                'rebate_grade' => $orderMirror['rebate_grade'],
                'xpcode_pay_price' => $orderMirror['supplier_price'], // 站点应收(进价)
                'xpcode_pay_money' => (string)round(bcmul($orderMirror['supplier_price'], $params['real_oil_num'], 3), 2),
                'truename' => $params['creator'],
                'remark' => $params['remark'] ?? '',
                'trade_from' => $params['trade_from'] ?? null,
                'real_oil_num' => $params['real_oil_num'],
                'mac_price'         => $orderMirror['mac_price'] ?? 0,
                'mac_amount'        => $orderMirror['mac_amount'] ?? 0,
                'price_id'        => $orderMirror['price_id'] ?? '',
                'original_order_id' => $params['original_order_id'] ?? 0,
                'document_type' => CardTradeConf::$history_type_to_foss_mapping[$logType] ?? 0,
                'order_type' => $params['order_sale_type'] ?? OrderModel::ORDER_SALE_TYPE_REFUEL,
                'cancel_order_id' => $params['cancel_order_id'] ?? 0,
                'ocr_truck_no_url'        => $orderMirror['ocr_truck_no_url'] ?? '',
            ];

            // 补录及异常订单修改特殊处理
            if (in_array($logType, [HistoryModel::COST_SUCCESS_ADD, HistoryModel::COST_ABNORMAL]) && !empty($params['oil_time'])) {
                $needParams['oil_time'] = $params['oil_time'];
            }

            $needParams['trade_from'] = array_get($params, "order_channel", 101);
            $fossPayResult = $this->foss->refundAndTradePay($needParams);
            if (!isset($fossPayResult['pay_status'])) {
                throw new \Exception('FOSS扣款失败,请重试！！！', '9003006');
            }
            if ($fossPayResult['pay_status'] != 1) {
                throw new \Exception('FOSS扣款失败,请重试！', '9003001');
            }
            $paymentId = $fossPayResult['payment_id'];
            $payTime = date('Y-m-d H:i:s'); // 需要foss回传支付时间
            // 生成交易流水
            if (isset($params['force_pay_time'])) {

                $payTime = $params['force_pay_time'];
            }
            $params['pay_time'] = $payTime;
            $params['payment_id'] = $paymentId;
            $params['trade_no'] = array_get($params, "trade_no", '');
            $params['old_id'] = $params['original_order_id'] ?? 0;
            $historyId = $this->historyService->addHistoryAfterPay($params, $logType);
            // 回写订单
            $updateArray = [
                'order_status' => OrderModel::SUCCESS_PAY,
                'payment_id' => $paymentId,
                'pay_time' => $payTime,
                'third_order_id' => $params['third_order_id'] ?? '',
                'history_id' => $historyId,
                'remark' => $params['remark'] ?? '',
                'updator' => $updator
            ];
            // 服务费以foss为准
            if (strcmp($params['service_money'], $fossPayResult['service_money'])) {
                $updateArray['service_money'] = $fossPayResult['service_money'];
                $updateArray['service_price'] = round(bcdiv($fossPayResult['service_money'], $params['oil_num'], 3), 2);
            }
            //支持无卡下单，成功后，更新到订单上卡数据
            if (!empty($this->cardInfo)) {
                $updateArray['org_code'] = array_get($this->cardInfo, 'insert_card_array.org_code', '');
                $updateArray['card_no'] = $params['card_no'];
                $updateArray['card_type'] = array_get($this->cardInfo, 'insert_card_array.card_type', '');
                $updateArray['card_level'] = array_get($this->cardInfo, 'insert_card_array.card_level', '');
                $updateArray['driver_name'] = array_get($this->cardInfo, 'extends_driver_info.driver_name', '');
                $updateArray['driver_phone'] = array_get($this->cardInfo, 'extends_driver_info.driver_phone', '');
                $updateArray['truck_no'] = array_get($this->cardInfo, 'extends_driver_info.truck_no', '');

                $extData['org_name'] = array_get($this->cardInfo, 'insert_card_mirror.org_name', '');
                $extData['account_type_name'] = array_get($this->cardInfo, 'insert_card_mirror.account_type_name', '');
                $extData['account_no'] = array_get($this->cardInfo, 'insert_card_mirror.account_no', '');
                $extData['account_name'] = array_get($this->cardInfo, 'insert_card_mirror.account_name', '');
                $extData['update_time'] = $payTime;
                $this->orderExtRepository->updateOrderExtByOrderId($params['order_id'], $extData);
            }
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);

            return [
                'order_id' => strval($params['order_id']),
                'payment_id' => $paymentId,
                'third_order_id' => $params['third_order_id'] ?? '',
                'history_id' => strval($historyId),
                'xpcode' => '20003JCP',
                'money' => $params['oil_money'],
            ];
        } catch (\Exception $e) {
            $updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark' => substr($e->getMessage(), 0, 200) . $e->getCode(),
                'updator' => $updator
            ];
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);
            // 飞书报警
            $this->feishu->sendExceptionToFeiShu($e);
            throw $e;
        }
    }

    /**
     * 黄牛下单失败处理
     * @param $orderId
     * @return bool
     */
    public function hnOrderPayFail($orderId)
    {
        // 查询订单信息
        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $orderId], ['order_id', 'trade_mode', 'order_status']);
        if (!empty($orderInfo) && in_array($orderInfo['order_status'], [OrderModel::FAIL_PAY, OrderModel::CANCEL, OrderModel::WAIT_PAY])) {
            $this->newOrderRepository->deleteByOrderId($orderId);
        }

        return true;
    }

    /**
     * 预售订单转销售订单-中物流
     * @throws Throwable
     */
    public function reservationOrderToSaleOrder(array $params)
    {
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderInfo = $this->newOrderRepository->getOrderInfoByLock([
                'order_id'     => $params['reservation_order_id'],
                'order_status' => OrderModel::SUCCESS_PAY,
            ]);
            if (empty($orderInfo)) {
                throw new Exception(
                    '订单号不存在或订单状态异常,请稍后再试',
                    $this->errorCodeService->getOrderErrorCode(500001)
                );
            }
            $historyId = $this->historyService->updateHistoryAddRefund(
                $orderInfo['history_id'],
                HistoryModel::COST_RESERVATION_REFUEL_REFUND
            );
            if (empty($historyId)) {
                throw new Exception(
                    '退款失败,添加交易流水失败!', $this->errorCodeService->gotoRefund(
                    40304
                )
                );
            }
            $result = $this->newOrderRepository->updateOrderByOrderId($orderInfo['order_id'], [
                'order_status' => OrderModel::REFUND,
                'updator'      => $orderInfo['creator'],
            ]);
            if (!$result) {
                throw new Exception(
                    '退款失败,更新订单状态失败!', $this->errorCodeService->gotoRefund(
                    40343
                )
                );
            }
            $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams([
                'id' => $params['station_id'],
            ], "*");
            if (!$stationInfo) {
                throw new Exception('验站失败，站点和商品不存在或已删除', '40321');
            }
            $provinceCityDict = (new CityModel())->whereIn('city_code', [
                array_get($stationInfo, 'provice_code', ''),
                array_get($stationInfo, 'city_code', ''),
            ])->get()->keyBy('city_code')->toArray();
            $oilDict = (new DictModel())->whereIn('id', [
                $params['oil_type'],
                $params['oil_level'],
                $params['oil_name']
            ])->get()->keyBy('id')->toArray();
            $orderId = $this->redisService->makeOrderId();
            $tradeNo = $this->redisService->makeSerialNum();
            $oilTime = date('Y-m-d H:i:s');
            $specialPriceOrgCode = $this->newOrderRepository::getSpecialPriceOrg(
                $params['station_id'],
                $orderInfo['org_code'],
                substr($orderInfo['org_code'], 0, 6)
            );
            $stationPriceData = app(NewOrderService::class)->checkAndPackStationPriceData([
                'station_id' => $params['station_id'],
                'oil_name'   => $params['oil_name'],
                'oil_level'  => $params['oil_level'],
                'oil_type'   => $params['oil_type'],
                'oil_price'  => 0,
                'orgcode'    => $specialPriceOrgCode,
                'trade_mode' => $orderInfo['trade_mode'],
                'pcode'      => $stationInfo['pcode'],
            ]);
            $makeOrder = [
                'order_id'           => $orderId,
                'order_sn'           => md5(Common::uuid()),
                'trade_no'           => $tradeNo,
                'order_type'         => $orderInfo['order_type'],
                'order_channel'      => $orderInfo['order_channel'],
                'trade_mode'         => $orderInfo['trade_mode'],
                'order_status'       => OrderModel::WAIT_PAY,
                'station_id'         => $params['station_id'],
                'station_code'       => $stationInfo['station_code'],
                'province_code'      => $stationInfo['provice_code'],
                'city_code'          => $stationInfo['city_code'],
                'supplier_code'      => $stationInfo['pcode'],
                'org_code'           => $orderInfo['org_code'],
                'card_no'            => $orderInfo['card_no'],
                'card_type'          => $orderInfo['card_type'],
                'card_level'         => $orderInfo['card_level'],
                'driver_name'        => $orderInfo['driver_name'],
                'driver_source'      => $orderInfo['driver_source'],
                'driver_phone'       => $orderInfo['driver_phone'],
                'truck_no'           => $orderInfo['truck_no'],
                'oil_type'           => $params['oil_type'],
                'oil_name'           => $params['oil_name'],
                'oil_level'          => $params['oil_level'],
                'oil_unit'           => $params['oil_unit'],
                'oil_num'            => $params['oil_num'],
                'oil_price'          => $stationPriceData['insert_station_mirror']['platform_price'],
                'oil_money'          => round(
                    bcmul(
                        bcdiv(
                            $params['mac_amount'],
                            $params['mac_price'],
                            10
                        ),
                        $stationPriceData['insert_station_mirror']['platform_price'],
                        3
                    ),
                    2
                ),
                'third_actual_price' => $stationPriceData['insert_station_mirror']['platform_price'],
                'third_actual_fee'   => round(
                    bcmul(
                        bcdiv(
                            $params['mac_amount'],
                            $params['mac_price'],
                            10
                        ),
                        $stationPriceData['insert_station_mirror']['platform_price'],
                        3
                    ),
                    2
                ),
                'real_oil_num'       => $params['oil_num'],
                'service_price'      => 0,
                'service_money'      => 0,
                'mirror'             => '',
                'creator'            => $orderInfo['creator'],
                'updator'            => $orderInfo['creator'],
                'create_time'        => $oilTime,
                'third_order_id'     => $params['third_order_id'] ?? '',
            ];
            $mirror = [
                'order_id'                => $orderId,
                'org_name'                => $orderInfo['ext']['org_name'],
                'account_type_name'       => $orderInfo['ext']['account_type_name'],
                'account_name'            => $orderInfo['ext']['account_name'],
                'account_no'              => $orderInfo['ext']['account_no'],
                'station_name'            => $stationInfo['station_name'],
                'remark_name'             => $stationInfo['remark_name'],
                'station_address'         => $stationInfo['address'],
                'rebate_grade'            => $stationInfo['rebate_grade'],
                'supplier_name'           => $orderInfo['ext']['supplier_name'],
                'province_name'           => $provinceCityDict[$stationInfo['provice_code']]['city_name'],
                'city_name'               => $provinceCityDict[$stationInfo['city_code']]['city_name'],
                'oil_type_name'           => $oilDict[$params['oil_type']]['dict_data'] ?? '',
                'oil_name_name'           => $oilDict[$params['oil_name']]['dict_data'],
                'oil_level_name'          => $oilDict[$params['oil_level']]['dict_data'] ?? '',
                'goods'                   => ($oilDict[$params['oil_type']]['dict_data'] ?? '') .
                                             $oilDict[$params['oil_name']]['dict_data'] .
                                             ($oilDict[$params['oil_level']]['dict_data'] ?? ''),
                'gun_id'                  => $stationPriceData['insert_station_mirror']['gun_id'],
                'gun_name'                => $stationPriceData['insert_station_mirror']['gun_name'],
                'tank_id'                 => $stationPriceData['insert_station_mirror']['tank_id'],
                'tank_name'               => $stationPriceData['insert_station_mirror']['tank_name'],
                'supplier_price'          => $params['supplier_price'],
                'platform_price'          => $stationPriceData['insert_station_mirror']['platform_price'],
                'price_id'                => $stationPriceData['insert_station_mirror']['price_id'],
                'order_token'             => '', // 补录无websocket标识
                'operator_id'             => $orderInfo['ext']['operator_id'],
                'app_station_id'          => $stationInfo['app_station_id'],
                'trade_type'              => $stationInfo['trade_type'],
                'lng'                     => $stationInfo['lng'],
                'lat'                     => $stationInfo['lat'],
                'mac_price'               => $params['mac_price'],
                'mac_amount'              => $params['mac_amount'],
                'oil_time'                => $oilTime,
                'original_order_id'       => $params['reservation_order_id'],
                'original_supplier_money' => $params['supplier_money'],
                'ocr_truck_no_url'        => $orderInfo['ext']['ocr_truck_no_url'] ?? '',
            ];
            $result = $this->newOrderRepository->insertOrder($makeOrder);
            if (empty($result)) {
                throw new Exception(
                    '创建订单失败,请重试!',
                    $this->errorCodeService->editApproveError(
                        $this->errorCodeService->approveError(
                            40305
                        )
                    )
                );
            }
            $extRes = $this->newOrderExtRepository->insertOrderExt($mirror);
            if (!$extRes) {
                throw new Exception(
                    '创建订单扩展失败,请重试!',
                    $this->errorCodeService->editApproveError(
                        $this->errorCodeService->approveError(
                            40305
                        )
                    )
                );
            }
            $payResult = $this->foss->cancelTradePay([
                'reservation_fuel_order_id' => $params['reservation_order_id'],
                'order_id'                  => $makeOrder['order_id'],
                'card_no'                   => $makeOrder['card_no'],
                'sale_type'                 => '现金消费',
                'unit'                      => 1,
                'station_id'                => $makeOrder['station_id'],
                'station_name'              => $mirror['station_name'],
                'station_code'              => $makeOrder['station_code'],
                'money'                     => $makeOrder['oil_money'],
                'price'                     => $makeOrder['oil_price'],
                'service_money'             => $makeOrder['service_money'],
                'oil_num'                   => $makeOrder['oil_num'],
                'third_order_no'            => $makeOrder['third_order_id'],
                'oil_name'                  => $mirror['goods'],
                'oil_name_val'              => $mirror['oil_name_name'],
                'trade_place'               => $mirror['station_name'],
                'trade_address'             => $mirror['station_address'],
                'oil_time'                  => $makeOrder['create_time'],
                'pcode'                     => $makeOrder['supplier_code'],
                'orgcode'                   => $makeOrder['org_code'],
                'provice_code'              => $makeOrder['province_code'],
                'provice_name'              => $mirror['province_name'],
                'city_code'                 => $makeOrder['city_code'],
                'city_name'                 => $mirror['city_name'],
                'open_check_password'       => 0,
                'card_password'             => '',
                'imgurl'                    => '',
                'truck_no'                  => $makeOrder['truck_no'],
                'drivername'                => $makeOrder['driver_name'],
                'drivertel'                 => $makeOrder['driver_phone'],
                'rebate_grade'              => $mirror['rebate_grade'],
                'xpcode_pay_price'          => $mirror['supplier_price'],
                'xpcode_pay_money'          => $params['supplier_money'],
                'truename'                  => $makeOrder['creator'],
                'remark'                    => '',
                'real_oil_num'              => $makeOrder['oil_num'],
                'mac_price'                 => $mirror['mac_price'],
                'mac_amount'                => $mirror['mac_amount'],
                'price_id'                  => $mirror['price_id'],
                'ocr_truck_no_url'          => $mirror['ocr_truck_no_url'],
            ]);
            $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
            $makeOrder['oil_time'] = $oilTime;
            $makeOrder['pay_time'] = $payResult['trade_time'];
            $makeOrder['payment_id'] = $payResult['payment_id'];
            $makeOrder['old_id'] = $orderId;
            $makeOrder['supplier_money'] = $params['supplier_money'];
            $historyId = $this->historyService->addHistoryAfterPay(
                $makeOrder,
                HistoryModel::COST_SUCCESS
            );
            $this->newOrderRepository->updateOrderByOrderId($orderId, [
                'order_status'  => OrderModel::SUCCESS_PAY,
                'payment_id'    => $payResult['payment_id'],
                'pay_time'      => $payResult['trade_time'],
                'history_id'    => $historyId,
                'updator'       => $orderInfo['creator'],
                'service_money' => $payResult['service_money'],
                'service_price' => round(
                    bcdiv($payResult['service_money'], $params['oil_num'], 3),
                    2
                ),
            ]);
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $throwable) {
            DB::connection('mysql_gas')->rollBack();
            throw $throwable;
        }
    }
}