<?php


namespace App\Http\Controllers;


use App\Library\Request;
use App\Services\CommonService;
use App\Services\ExportService;
use App\Services\HistoryService;

class ExportController extends Controller
{
    protected $exportService;
    protected $historyService;

    public function __construct
    (
        ExportService $exportService,
        HistoryService $historyService
    )
    {
        $this->exportService = $exportService;
        $this->historyService = $historyService;
    }

    /**
     * 交易流水报表导出
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportHistory()
    {
        $rule = [
            'business_log_id'            => 'nullable',              // ID
            'serial_num'                 => 'nullable|alpha_num',         // G7流水ID
            'stream_no'                  => 'nullable',                    // 三方订单号
            'need_relevant_business_log' => 'nullable',   // 查看关联流水 0不需要 1需要[暂时不加]
            'card_no'                    => 'nullable',                      // 账号 原16位卡号
            'ge_create_time'             => 'nullable',               // 流水创建时间(开始)
            'le_create_time'             => 'nullable',               // 流水创建时间(截止)
            'ge_oil_time'                => 'nullable',                  // 订单支付时间(开始)
            'le_oil_time'                => 'nullable',                  // 订单支付时间(截止)
            'station_id'                 => 'nullable',                   // 站点ID,逗号隔开
            'station_code'               => 'nullable',                 // 站点编码
            'log_type'                   => 'nullable',                     // 单据类型
            'driver_name' => 'nullable',                  // 司机姓名
            'driver_phone' => 'nullable',                 // 手机号
            'truck_no' => 'nullable',                     // 车牌号
            'org_code' => 'nullable',                     //司机机构
            'need_relevant_org_order' => 'nullable|in:0,1', // 查看机构子订单 0否 1是
            'province_code' => 'nullable',                // 所属省
            'city_code' => 'nullable',                    // 所属市
            'data_type' => 'nullable',                    // 订单来源
            'pcode' => 'nullable',                        // 站点供应商
            'oil_type' => 'nullable|alpha_num',           // 油品类型
            'oil_name' => 'nullable|alpha_num',           // 油品名称
            'oil_level' => 'nullable|alpha_num',          // 油品级别
            'channel_id' => 'nullable|integer',           // 采购渠道ID
            'limit' => 'nullable|numeric|between:1,100',
            'page' => 'nullable|numeric'
        ];

        $params = $this->trimNull(array_keys($rule));
        try {
            $this->validator($params, $rule);

            $maxDay = $this->historyService->getMaxDay();
            $operatorUid = Request::get('uid');
            //G7WALLET-6827【GMS】合作伙伴道科查看站点交易流水的时间范围增加到近6个月
            if(in_array($operatorUid, ['469', '68'])) {
                $maxDay = 180;
            }
            if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
                // 不能超过180天
                if(strtotime(date('Y-m-d 00:00:00',time())) - strtotime($params['ge_create_time']) > $maxDay*86400){
                    throw new \Exception('无法查询出该时间段的结果!');
                }
            }

            if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
                if (strtotime(date('Y-m-d 00:00:00', time())) - strtotime($params['ge_oil_time']) > $maxDay * 86400) {
                    throw new \Exception('无法查询出该时间段的结果!');
                }
            }
            $this->exportService->exportHistory($params);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 订单导出
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportOrder()
    {
        $field = [
            'station_id' => '', // 站点ID,逗号分割
            'station_code' => '', // 站点编码
            'order_id' => '', // 订单号
            'third_order_id' => '', // 三方订单号
            'order_status' => '', // 订单状态,逗号分割
            'driver_name' => '', // 司机姓名
            'driver_phone' => '', // 司机手机号
            'truck_no' => '', // 司机车牌号
            'card_no' => '', // 账号 原卡号
            'supplier_code' => '', // 站点供应商
            'org_code' => '', // 机构编码
            'need_relevant_org_order' => '', // 是否查看子机构订单 1是 0否
            'oil_name' => '', // 油品名称
            'oil_type' => '', // 油品类型
            'oil_level' => '', // 油品等级
            'province_code' => '', // 省
            'city_code' => '', // 市
            'ge_create_time' => '', // 创建时间开始
            'le_create_time' => '', // 创建时间截止
            'ge_pay_time' => '', // 更新时间开始
            'le_pay_time' => '', // 更新时间截止
            'channel_id' => '', // 渠道ID
            'payment_id' => '', // 支付单ID
            'order_sale_type'         => '', //订单销售类型(10销售订单20预售订单)
        ];

        try {
            $params = $this->getParamAll($field);

            $this->exportService->exportOrder($params);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 交易流水报表导出-对外
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportHistoryOuter()
    {
        $rule = [
            'business_log_id'            => 'nullable',              // ID
            'serial_num'                 => 'nullable|alpha_num',         // G7流水ID
            'stream_no'                  => 'nullable',                    // 三方订单号
            'need_relevant_business_log' => 'nullable',   // 查看关联流水 0不需要 1需要[暂时不加]
            'card_no'                    => 'nullable',                      // 账号 原16位卡号
            'ge_create_time'             => 'nullable',               // 流水创建时间(开始)
            'le_create_time'             => 'nullable',               // 流水创建时间(截止)
            'ge_oil_time'                => 'nullable',                  // 订单支付时间(开始)
            'le_oil_time'                => 'nullable',                  // 订单支付时间(截止)
            'station_id'                 => 'nullable',                   // 站点ID,逗号隔开
            'station_code'               => 'nullable',                 // 站点编码
            'log_type'                   => 'nullable',                     // 单据类型
            'driver_name' => 'nullable',                  // 司机姓名
            'driver_phone' => 'nullable',                 // 手机号
            'truck_no' => 'nullable',                     // 车牌号
            'org_code' => 'nullable',                     //司机机构
            'need_relevant_org_order' => 'nullable|in:0,1', // 查看机构子订单 0否 1是
            'province_code' => 'nullable',                // 所属省
            'city_code' => 'nullable',                    // 所属市
            'data_type' => 'nullable',                    // 订单来源
            'pcode' => 'nullable',                        // 站点供应商
            'oil_type' => 'nullable|alpha_num',           // 油品类型
            'oil_name' => 'nullable|alpha_num',           // 油品名称
            'oil_level' => 'nullable|alpha_num',          // 油品级别
            'channel_id' => 'nullable|integer',           // 采购渠道ID
            'limit' => 'nullable|numeric|between:1,100',
            'page' => 'nullable|numeric',
            'type' => 'nullable',                         //平台/对外
        ];

        $params = $this->trimNull(array_keys($rule));
        try {
            $this->validator($params, $rule);
            //lp-begin增加用户绑定组织下的站点/运营商的判断
            $params=(new CommonService())->getStationOrSupplierOfAdminUserRule($params);
            //end
            //lp-begin导出字段自定义 对外/平台
            $res=$this->exportService->getExportHeadsAndFields();
            //end
            $this->exportService->exportHistory($params,$res['header'],$res['exportField']);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }


}