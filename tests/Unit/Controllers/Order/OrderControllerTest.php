<?php

namespace Tests\Unit\Controllers\Order;

use App\Http\Controllers\Order\OrderController;
use App\Services\NewOrderService;
use App\Services\HistoryService;
use App\Services\CouponService;
use Illuminate\Http\JsonResponse;
use App\Library\Request;
use Tests\TestCase;
use Mockery;

class OrderControllerTest extends TestCase
{
    protected $controller;
    protected $newOrderService;
    protected $historyService;
    protected $couponService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock services
        $this->newOrderService = Mockery::mock(NewOrderService::class);
        $this->historyService = Mockery::mock(HistoryService::class);
        $this->couponService = Mockery::mock(CouponService::class);
        
        // Create controller instance with mocked services
        $this->controller = new OrderController(
            $this->newOrderService,
            $this->historyService,
            $this->couponService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试获取商品信息 - 成功场景
     */
    public function testGetGoodsLimitSuccess()
    {
        // Mock request parameters
        $params = [
            'order_token' => '123456789',
            'station_id' => 'station_001'
        ];
        
        // Mock service response
        $expectedResult = [
            'goods' => [
                'oil_type' => '92#汽油',
                'price' => 7.50,
                'limit' => 100
            ]
        ];
        
        $this->newOrderService
            ->shouldReceive('getGoodsLimit')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        // Mock request
        request()->merge($params);
        
        $response = $this->controller->getGoodsLimit();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试获取商品信息 - 参数验证失败
     */
    public function testGetGoodsLimitValidationFailed()
    {
        // Mock invalid parameters
        $params = [
            'order_token' => '', // 缺少必需参数
            'station_id' => 'station_001'
        ];
        
        request()->merge($params);
        
        $response = $this->controller->getGoodsLimit();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
        $this->assertContains('下单token必传', $responseData['msg']);
    }

    /**
     * 测试校验上笔订单状态 - 成功场景
     */
    public function testCheckStationLatestOrderSuccess()
    {
        $params = [
            'station_id' => 'station_001'
        ];
        
        $expectedResult = [
            'has_pending_order' => false,
            'latest_order_status' => 'completed'
        ];
        
        $this->newOrderService
            ->shouldReceive('checkStationLatestOrder')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);
        
        $response = $this->controller->checkStationLatestOrder();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试下单 - 成功场景
     */
    public function testCreateOrderSuccess()
    {
        $params = [
            'order_token' => 'token_123',
            'order_type' => '1',
            'station_id' => 'station_001',
            'oil_type' => '92#',
            'oil_name' => '92#汽油',
            'oil_level' => '国VI',
            'oil_unit' => '1',
            'oil_num' => '50.00',
            'oil_price' => '7.50',
            'oil_money' => '375.00',
            'service_price' => '0.00',
            'service_money' => '0.00'
        ];
        
        $expectedResult = [
            'order_id' => '2023070112345678',
            'status' => 'created'
        ];
        
        $this->newOrderService
            ->shouldReceive('createOrder')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);
        
        $response = $this->controller->createOrder();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
        $this->assertEquals('成功', $responseData['msg']);
    }

    /**
     * 测试下单 - 参数验证失败
     */
    public function testCreateOrderValidationFailed()
    {
        $params = [
            'order_token' => 'token_123',
            'order_type' => '3', // 无效的订单类型
            'station_id' => 'station_001'
        ];
        
        request()->merge($params);
        
        $response = $this->controller->createOrder();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
        $this->assertContains('订单类型错误', $responseData['msg']);
    }

    /**
     * 测试获取订单列表 - 成功场景
     */
    public function testGetOrderPaginateSuccess()
    {
        $params = [
            'station_id' => 'station_001',
            'page' => 1,
            'limit' => 10
        ];
        
        $expectedResult = [
            'data' => [
                [
                    'order_id' => '2023070112345678',
                    'station_name' => '测试加油站',
                    'oil_money' => '375.00'
                ]
            ],
            'total' => 1,
            'current_page' => 1,
            'per_page' => 10
        ];
        
        $this->newOrderService
            ->shouldReceive('getOrderPaginate')
            ->once()
            ->andReturn($expectedResult);

        request()->merge($params);
        
        $response = $this->controller->getOrderPaginate();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试获取最大天数
     */
    public function testGetMaxDay()
    {
        $expectedMaxDay = 180;
        
        $this->historyService
            ->shouldReceive('getMaxDay')
            ->once()
            ->andReturn($expectedMaxDay);
        
        $response = $this->controller->getMaxDay();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals(['maxDay' => $expectedMaxDay], $responseData['data']);
    }

    /**
     * 测试获取订单详情 - 参数验证失败
     */
    public function testGetOrderItemValidationFailed()
    {
        $params = [
            // 缺少必需的参数
        ];

        request()->merge($params);

        $response = $this->controller->getOrderItem();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
    }

    /**
     * 测试订单关闭 - 成功场景
     */
    public function testOrderTimeOutSuccess()
    {
        $params = [
            'order_id' => '2023070112345678'
        ];
        
        $expectedResult = [
            'order_id' => '2023070112345678',
            'status' => 'timeout'
        ];
        
        $this->newOrderService
            ->shouldReceive('orderTimeOut')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);
        
        $response = $this->controller->orderTimeOut();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试取消订单 - 成功场景
     */
    public function testCancelOrderSuccess()
    {
        $params = [
            'order_id' => '2023070112345678',
            'force' => 1,
            'pushMsgToDoper' => false,
            'pay_reason' => '用户主动取消'
        ];
        
        $expectedResult = [
            'order_id' => '2023070112345678',
            'status' => 'cancelled'
        ];
        
        $this->newOrderService
            ->shouldReceive('cancelOrder')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);
        
        $response = $this->controller->cancelOrder();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试批量获取订单详情 - 成功场景
     */
    public function testGetBatchOrderItemSuccess()
    {
        $params = [
            'order_ids' => ['2023070112345678', '2023070112345679']
        ];

        $expectedResult = [
            [
                'order_id' => '2023070112345678',
                'station_name' => '测试加油站1',
                'oil_money' => '375.00'
            ],
            [
                'order_id' => '2023070112345679',
                'station_name' => '测试加油站2',
                'oil_money' => '400.00'
            ]
        ];

        $this->newOrderService
            ->shouldReceive('getBatchOrderItem')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);

        $response = $this->controller->getBatchOrderItem();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试批量获取订单详情 - 参数验证失败
     */
    public function testGetBatchOrderItemValidationFailed()
    {
        $params = [
            'order_ids' => 'invalid_format' // 应该是数组
        ];

        request()->merge($params);

        $response = $this->controller->getBatchOrderItem();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
    }

    /**
     * 测试查看小票 - 成功场景
     */
    public function testGetTicketSuccess()
    {
        $params = [
            'order_id' => '2023070112345678'
        ];

        $expectedResult = [
            'order_id' => '2023070112345678',
            'ticket_url' => 'https://example.com/ticket.pdf',
            'ticket_content' => '小票内容'
        ];

        $this->newOrderService
            ->shouldReceive('getTicket')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);

        $response = $this->controller->getTicket();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试查看小票 - 参数验证失败
     */
    public function testGetTicketValidationFailed()
    {
        $params = [
            'order_id' => 'invalid_order_id' // 应该是数字
        ];

        request()->merge($params);

        $response = $this->controller->getTicket();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
        $this->assertContains('订单号格式错误', $responseData['msg']);
    }

    /**
     * 测试OA下单 - 成功场景
     */
    public function testCreateOrderByOASuccess()
    {
        $params = [
            'card_no' => '1234567890123456',
            'station_id' => 'station_001',
            'oil_type' => '92#',
            'oil_name' => '92#汽油',
            'oil_level' => '国VI',
            'oil_unit' => '1',
            'oil_num' => '50',
            'oil_price' => '7.50',
            'oil_money' => '375.00',
            'creator' => '测试用户',
            'driver_name' => '张三',
            'driver_phone' => '13800138000',
            'truck_no' => '京A12345',
            'third_order_id' => 'OA_ORDER_123'
        ];

        $expectedResult = '2023070112345678';

        $this->newOrderService
            ->shouldReceive('createOrderByOA')
            ->once()
            ->with($params)
            ->andReturn($expectedResult);

        request()->merge($params);

        $response = $this->controller->createOrderByOA();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
    }

    /**
     * 测试获取交易流水 - 成功场景
     */
    public function testGetHistoryPaginateSuccess()
    {
        $params = [
            'station_id' => 'station_001',
            'page' => 1,
            'limit' => 10
        ];

        $expectedResult = [
            'data' => [
                [
                    'business_log_id' => '123456',
                    'serial_num' => 'G7_123456',
                    'oil_money' => '375.00'
                ]
            ],
            'total' => 1,
            'current_page' => 1,
            'per_page' => 10
        ];

        $this->historyService
            ->shouldReceive('getHistoryPaginate')
            ->once()
            ->andReturn($expectedResult);

        request()->merge($params);

        $response = $this->controller->getHistoryPaginate();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(0, $responseData['code']);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedResult, $responseData['data']);
        $this->assertEquals('成功', $responseData['msg']);
    }

    /**
     * 测试异常处理
     */
    public function testExceptionHandling()
    {
        $params = [
            'order_token' => '123456789',
            'station_id' => 'station_001'
        ];

        $this->newOrderService
            ->shouldReceive('getGoodsLimit')
            ->once()
            ->with($params)
            ->andThrow(new \Exception('服务异常', 500));

        request()->merge($params);

        $response = $this->controller->getGoodsLimit();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(500, $responseData['code']);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('服务异常', $responseData['msg']);
    }







    /**
     * 测试数据提供者 - 无效的订单参数
     */
    public function invalidOrderParamsProvider()
    {
        return [
            'missing_order_token' => [
                [
                    'order_type' => '1',
                    'station_id' => 'station_001'
                ],
                'token密钥必传'
            ],
            'invalid_order_type' => [
                [
                    'order_token' => 'token_123',
                    'order_type' => '3',
                    'station_id' => 'station_001'
                ],
                '订单类型错误'
            ]
        ];
    }

    /**
     * 使用数据提供者测试创建订单的参数验证
     *
     * @dataProvider invalidOrderParamsProvider
     */
    public function testCreateOrderWithInvalidParams($params, $expectedErrorMessage)
    {
        request()->merge($params);

        $response = $this->controller->createOrder();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(403, $responseData['code']);
        $this->assertFalse($responseData['success']);
        $this->assertContains($expectedErrorMessage, $responseData['msg']);
    }

    /**
     * 测试控制器构造函数
     */
    public function testControllerConstruction()
    {
        $controller = new OrderController(
            $this->newOrderService,
            $this->historyService,
            $this->couponService
        );

        $this->assertInstanceOf(OrderController::class, $controller);
    }
}
