<?php

use App\Models\StationOrgRuleModel;
use App\Services\NewOrderService;
use App\Services\StationService;
use Tests\TestCase;

class LimitTest extends TestCase
{
    public function testLimit()
    {
        $params = [
            'orgcode_list' => ['200NW5', '200NW501'],
            'type' =>1
        ];

//        $result = app(StationOrgRuleModel::class)->getActiveOrgCode($params);
//        $result =  app(NewOrderService::class)->getLimitStation('200NW5');
        $result =  app(StationService::class)->stationLimit([],'200NWQ01');
        dd($result);
    }

}