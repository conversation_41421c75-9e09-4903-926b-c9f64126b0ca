<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationOrgRuleModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_org_rule';

    protected $guarded = ["id"];


    /**
     * 获取当前生效的规则的机构id
     * @param $orgCodeList
     * @return mixed
     */
    public function getActiveOrgCode($whereParams, $select = ['*'], $toArray = false)
    {
        $query = [
            'type' => 1,
        ];

        $sqlObj = BaseModel::scopeWithCondition(self::select($select), $query);

        if (!empty($whereParams['orgcode_list'])) {
            $sqlObj->whereIn('orgcode', $whereParams['orgcode_list']);
        }
        $result = $sqlObj->get()->toArray();

        if (!empty($result)) {
            $ruleMap = [];
            foreach ($result as $r) {
                $ruleMap[$r['orgcode']] = $r;
            }
            krsort($ruleMap);

            return array_values($ruleMap)[0]['orgcode'];
        }
        return '';
    }


}