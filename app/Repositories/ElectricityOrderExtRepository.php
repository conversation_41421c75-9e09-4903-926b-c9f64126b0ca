<?php

namespace App\Repositories;

use App\Models\ElectricityOrderExtModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

class ElectricityOrderExtRepository
{
    protected $electricityOrderExtModel;

    public function __construct
    (
        ElectricityOrderExtModel $electricityOrderExtModel
    )
    {
        $this->electricityOrderExtModel = $electricityOrderExtModel;
    }

    /**
     * @param array $params
     * @param array $select
     * @param $lock
     * @return ElectricityOrderExtModel|Model|Builder|mixed|object|null
     */
    public function getOneElectricityOrderExtByParams(array $params, array $select = ['*'], $lock = false)
    {
        return $this->electricityOrderExtModel::getOneElectricityOrderExtByParams($params, $select, $lock);
    }

    public function insertElectricityOrderExt(array $params): bool
    {
        return $this->electricityOrderExtModel::insertElectricityOrderExt($params);
    }

    public function updateElectricityOrderExtByOrderId(int $orderId, array $params): bool
    {
        return $this->electricityOrderExtModel::updateElectricityOrderExt($orderId, $params);
    }
}
