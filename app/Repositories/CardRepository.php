<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Repositories;

use App\Http\Defines\CommonError;
use App\Models\CardModel;
use App\Models\CardViceModel;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use RuntimeException;
use Throwable;

class CardRepository extends EloquentRepository
{
    protected $cardModel;
    protected $cardViceModel;
    protected $truckCardGetModel;

    public function __construct(CardModel $cardModel, CardViceModel $cardViceModel)
    {
        $this->cardModel = $cardModel;
        $this->cardViceModel = $cardViceModel;
    }

    //查询
    public function getInfoByCardNo(array $condition)
    {
        $data = $this->cardModel->select(['password', 'card_name', 'ischeck', 'gascode', 'orgcode',
                                          'truck_no', 'card_id', 'card_no', 'id', 'pwd_errornum', "driverphone", "paylimit", "oil_top", "month_top", "card_level",
                                          "day_top", "isactivation", "ischeck"])
                                ->withCondition($condition)
                                ->with('org')
                                ->first();
        return $data;
    }

    //编辑GasG7能源账户信息
    public function editCardInfo(array $condition, $data)
    {
        //return $this->cardModel->find($condition)->update($data);
        return CardModel::where($condition)->update($data);
    }

    //编辑FossG7能源账户信息
    public function editCardInfoForGsp(array $condition, $data)
    {
        return CardViceModel::where($condition)->update($data);
    }

    //获取G7能源账户信息
    public function getCardInfo(array $condition)
    {
        $data = $this->cardViceModel->select(['vice_password', 'id', 'vice_no', 'org_id','truck_type'])
                                    ->withCondition($condition)
                                    ->with('org')
                                    ->first();
        return $data;
    }

    /**
     * 批量获取G7能源账户信息
     *
     * @param $params
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getCardInfoByParamsBatch($params, $select = ['*'], $toArray = true)
    {
        return $this->cardModel->getCardInfoByParamsBatch($params, $select, $toArray);
    }

    /**
     * 验证卡片使用状态
     * @param mixed $cardUseStatus 卡片使用状态
     * @param bool $throwException 当验证不通过时是否抛出异常
     * @return bool
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/21 11:15 上午
     */
    public static function checkCardUseStatus($cardUseStatus, bool $throwException = false): bool
    {
        if ($cardUseStatus != 10) {

            if ($throwException) {

                throw new RuntimeException(CommonError::$codeMsg[CommonError::ABNORMAL_CARD],
                    CommonError::ABNORMAL_CARD);
            }

            return false;
        }

        return true;
    }

    /**
     * 验证卡片账户状态
     * @param mixed $accountStatus 卡片默认绑定账户状态
     * @param bool $throwException 当验证不通过时是否抛出异常
     * @return bool
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/21 11:35 上午
     */
    public static function checkCardAccountStatus($accountStatus, bool $throwException = false): bool
    {
        if ($accountStatus != 10) {

            if ($throwException) {

                throw new RuntimeException(CommonError::$codeMsg[CommonError::ABNORMAL_CARD_ACCOUNT],
                    CommonError::ABNORMAL_CARD_ACCOUNT);
            }

            return false;
        }

        return true;
    }

    /**
     * 验证卡片所属机构的消费限制(站点、商品)
     * @param string $orgCode 卡片所属机构代码
     * @param string $stationId 加油员绑定站点ID
     * @param string $oilName 司机消费的商品
     * @param bool $throwException 当验证不通过时是否抛出异常
     * @return bool
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/21 6:03 下午
     */
    public static function checkCardForStationAndGoodsConsumeLimit(string $orgCode, string $stationId, string $oilName = "",
                                                                   bool $throwException = false): bool
    {
        $requestData = [
            'orgcode'    => $orgCode,
            'station_id' => $stationId,
        ];
        //当oilName字段值不为空的时候才传递该参数,避免检测接口误判
        if (!empty($oilName)) {

            $requestData['oil_name'] = $oilName;
        }

        $checkResult = (new FossStation())->checkCardForStationAndGoodsConsumeLimit($requestData);

        if (!array_has($checkResult, ['oil_can_use', 'station_can_use'])) {

            throw new RuntimeException("", CommonError::SYSTEM_ERROR);
        }

        if (!$checkResult['oil_can_use']) {

            if ($throwException) {

                throw new RuntimeException("", CommonError::CARD_GOODS_CONSUME_LIMIT_FAILED);
            }

            return false;
        }

        if (!$checkResult['station_can_use']) {

            if ($throwException) {

                throw new RuntimeException("", CommonError::CARD_STATION_CONSUME_LIMIT_FAILED);
            }

            return false;
        }

        return true;
    }

    /**
     * 获取卡片信息
     * @param string $cardNo 卡号
     * @param array $checkData 验证数据,例：['rules' => ['xxx' => 'in'], 'messages' => ['xxx.in' => '']]
     * @param bool $throwException 验证失败时是否直接抛出异常
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 5:20 下午
     */
    public static function getCardInfoByNo(string $cardNo, array $checkData = [],
                                           bool $throwException = true): array
    {
        try {

            $responseData = (new FossUser())->getCardInfoByNo([
                'card_no' => $cardNo,
            ]);
            self::checkArrayField($responseData, $checkData, $throwException);
        } catch (Throwable $exception) {

            throw new RuntimeException("", CommonError::SYSTEM_ERROR);
        }

        return $responseData;
    }

    /**
     * 获取G7能源账户信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneCardByParams($whereParams, $select = ['*'], $toArray = true)
    {
        return $this->cardModel->getOneCardByParams($whereParams, $select, $toArray);
    }
}