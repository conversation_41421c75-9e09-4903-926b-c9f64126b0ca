<?php


namespace App\Repositories;


use App\Models\TruckCardWechatPhonesModel;

class TruckCardRepository
{
    protected $cardWechatPhonesModel;

    public function __construct
    (
        TruckCardWechatPhonesModel $cardWechatPhonesModel
    )
    {
        $this->cardWechatPhonesModel = $cardWechatPhonesModel;
    }

    /**
     * 获取G7能源车辆账户最近的使用记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->cardWechatPhonesModel->getOneByParams($whereParams, $select, $toArray);
    }
}