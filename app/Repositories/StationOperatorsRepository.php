<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\CardViceBillModel;
use App\Models\OilAccountAssignDetails;
use App\Models\OilAccountMoneyTransfer;
use App\Models\StationOperatorsModel;

class StationOperatorsRepository extends \App\Repositories\EloquentRepository
{
    protected $stationOperator;
    public function __construct(StationOperatorsModel $operatorsModel)
    {
        $this->stationOperator = $operatorsModel;
    }

    public function getPcodeList($condition = [])
    {
        //$this->debugstart('mysql_foss');
        $data = $this->stationOperator->withCondition($condition)->pluck("operators_code");
        //$sql = $this->debugend('mysql_foss');
        //print_r($sql);
        return $data;
    }
}