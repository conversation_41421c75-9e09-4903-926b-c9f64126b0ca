<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\OilOrgModel;
use App\Models\OrgModel;
use App\Models\StationPcodeModel;

class OrgRepository extends EloquentRepository
{
    protected $orgModel;
    protected $oilOrgModel;
    protected $stationPcodeModel;

    public function __construct(
        OrgModel          $orgModel,
        OilOrgModel       $oilOrgModel,
        StationPcodeModel $stationPcodeModel
    )
    {
        $this->orgModel = $orgModel;
        $this->oilOrgModel = $oilOrgModel;
        $this->stationPcodeModel = $stationPcodeModel;
    }

    //查询
    public function getInfo(array $condition)
    {
        $data = $this->orgModel->withCondition($condition)->select("id","orgcode","pcode","orgname")->first();
        return $data;
    }

    //查询Foss的机构信息
    public function getOilOrgById(array $condition)
    {
        $data = $this->oilOrgModel->select("id","orgcode","org_name","is_system_orgcode")->withCondition($condition)->first();
        return $data;
    }

    /**
     * 获取G7能源账户机构信息
     *
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneCardOrg($select = ['*'], $toArray = false)
    {
        return $this->oilOrgModel->getBatchOrgByParams([], $select, $toArray);
    }

    /**
     * 单个获取机构信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneOrgByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->orgModel->getOneOrgByParams($whereParams, $select, $toArray);
    }

    /**
     * 批量获取机构信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchOrgByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->oilOrgModel->getBatchOrgByParams($whereParams, $select, $toArray);
    }
}