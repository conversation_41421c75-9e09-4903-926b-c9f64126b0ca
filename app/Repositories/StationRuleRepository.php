<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\StationRuleModel;

class StationRuleRepository extends \App\Repositories\EloquentRepository
{
    protected $stationRuleModel;
    public function __construct(StationRuleModel $stationRuleModel)
    {
        $this->stationRuleModel = $stationRuleModel;
    }

    //查询
    public function getList(array $condition,$field="rule_val",$_key="orgcode")
    {
        //$this->debugstart('mysql_gas');
        $data = $this->stationRuleModel->select("rule_val","orgcode","id","category","rule_type")->withCondition($condition)->orderby("create_time","desc")->get();
        //$sql = $this->debugend('mysql_gas');
        //print_r($sql);exit;
        return $data;
    }
}