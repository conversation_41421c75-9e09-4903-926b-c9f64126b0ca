<?php


namespace App\Models;


class OrderExtModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_order_new_extend';

    protected $fillable = [
        'order_id', 'driver_signature', 'oil_time', 'org_name', 'account_no', 'account_type_name','account_name', 'station_name',
        'rebate_grade', 'lng', 'lat', 'province_name', 'city_name', 'station_address', 'oil_type_name', 'oil_name_name',
        'oil_level_name','goods', 'price_id','tank_id','gun_id','gun_name','tank_name','supplier_price', 'platform_price',
        'mac_price','is_sync', 'create_time','update_time','remark_name','supplier_name','order_token','mac_amount','app_station_id',
        'trade_type','third_coupon_flag','g7_coupon_flag','ndrc_price','charge_rebate_money','discount_rate', 'original_supplier_money',
        'master_card_no','supplementary_card_no','card_user_id','ocr_truck_no_url'
    ];

    public $timestamps = false;

    /**
     * 插入记录
     * @param array $insertArray
     * @return mixed
     */
    public function insertOrderExt(array $insertArray)
    {
        return self::insert($insertArray);
    }

    /**
     * 更新订单信息
     *
     * @param $orderId
     * @param $updateArray
     * @return mixed
     */
    public function updateOrderExtByOrderId($orderId, $updateArray)
    {
        return self::from($this->table)->where(['order_id' => $orderId])->update($updateArray);
    }

    public function getOrderExtInfo($orderId)
    {
        return self::from($this->table)->where(['order_id' => $orderId])->first();
    }

    /**
     * 关联电子券使用记录表
     */
    public function coupons()
    {
        return $this->hasOne(CouponsModel::class, 'voucher', 'g7_coupon_flag');
    }

    public function getPluckExtendByParams(array  $whereParams,$pluck){
        $sqlObj = BaseModel::scopeWithCondition(self::select(),$whereParams,$pluck);

        $result = $sqlObj->pluck($pluck);

        return !$result ? [] : $result->toArray();
    }

}