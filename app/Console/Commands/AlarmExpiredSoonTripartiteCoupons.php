<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/7
 * Time: 3:18 PM
 */

namespace App\Console\Commands;

use App\Http\Defines\CouponDefine;
use App\Models\BaseModel;
use App\Models\CouponsModel;
use App\Models\SupplierModel;
use App\Servitization\FeiShu;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AlarmExpiredSoonTripartiteCoupons extends Command
{
    const LOG = 'queue_order_operation';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'alarm:expired_tripartite_coupon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '预警即将过期的三方电子券';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $couponModel = new CouponsModel();
        $supplierModel = new SupplierModel();
        $data = BaseModel::scopeWithCondition(
            $couponModel->leftJoin(
                $supplierModel->getTable(),
                $couponModel->getTable() . '.pcode',
                '=',
                $supplierModel->getTable() . '.scode'
            ),
            [
                'pcode' => [
                    CouponDefine::getHuBeiZSYPCode(),
                ]
            ],
            true,
            $couponModel->getTable()
        )->where(
            $couponModel->getTable() . ".tripartite_status",
            '=',
            CouponDefine::NO_CHARGE_OFF
        )->where(
            $couponModel->getTable() . ".status",
            '=',
            CouponDefine::NO_CHARGE_OFF
        )->whereRaw(
            "unix_timestamp(" . $couponModel->getTable() . ".end_time) - " . time() . " > 0"
        )->whereRaw(
            "unix_timestamp(" . $couponModel->getTable() . ".end_time) - " . time() . " <= 864000"
        )->orderBy(
            DB::raw("unix_timestamp(" . $couponModel->getTable() . ".end_time) - " . time()),
            'desc'
        )->select([
            $supplierModel->getTable() . '.supplier_name',
            $couponModel->getTable() . '.end_time',
            $couponModel->getTable() . '.voucher',
            $couponModel->getTable() . '.face_value',
            $couponModel->getTable() . '.coupon_type_name',
            $couponModel->getTable() . '.refuel_type',
        ])->get()->toArray();
        if (empty($data)) {
            return;
        }
        foreach ($data as $v) {
            (new FeiShu())->alarmExpiredSoonTripartiteCoupons([
                'days'          => ceil((strtotime($v['end_time']) - time()) / 86400),
                'coupon_name'   => $v['coupon_type_name'],
                'face_value'    => $v['face_value'] . ($v['refuel_type'] == 1 ? '元' : '升'),
                'end_time'      => $v['end_time'],
                'supplier_name' => $v['supplier_name'],
            ]);
        }
    }
}