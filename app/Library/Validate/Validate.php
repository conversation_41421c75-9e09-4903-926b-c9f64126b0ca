<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/19
 * Time: 19:22
 */
namespace App\Library;
use Validator;
class Validate extends \Illuminate\Validation\Factory
{

    private static $message = 'ok';
    private static $headers =
        [
            'e' => 'rules/data is empty',
            'na' => 'rules/data is not a array'
        ];



    /**
     * @param array $rules  验证规则
     * @param array $data   验证数据
     * @return bool
     */
    public static function validators($rules= [], $data=[])
    {
        if( empty($rules) || empty($data) )
        {
            self::$message = self::$headers['e'];
            return false;
        }
        if(is_array($rules) && is_array($data))
        {
            $validator = Validator::make($data, $rules);
            if( $validator->fails() )
            {
                self::$message = $validator->messages();
                return false;
            }
            return true;
        }
        self::$message = self::$headers['na'];

        return false;
    }

    /**
     * 验证实例
     * @param $rules
     * @param $data
     * @return \Illuminate\Validation\Validator
     */
    private static function vmake($rules,$data)
    {
        $v = self::getInstance()->make($data,$rules);
        return $v;
    }

    /**
     * 获取错误消息
     * @return string
     */
    public static function getMessage()
    {
        return self::$message;
    }
}