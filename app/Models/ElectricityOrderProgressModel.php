<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ElectricityOrderProgressModel
 * @package App\Models
 * @version June 9, 2025, 2:14 pm CST
 *
 * @property integer order_id
 * @property string $third_connector_id
 * @property string|\Carbon\Carbon start_time
 * @property string|\Carbon\Carbon fetch_time
 * @property string|\Carbon\Carbon expected_end_time
 * @property string|\Carbon\Carbon end_time
 * @property string connector_status
 * @property number current_a
 * @property number current_b
 * @property number current_c
 * @property number voltage_a
 * @property number voltage_b
 * @property number voltage_c
 * @property number total_num
 * @property number total_electricity_money
 * @property number total_service_money
 * @property number total_money
 * @property number price
 * @property number remainder_num
 * @property string end_desc
 */
class ElectricityOrderProgressModel extends BaseModel
{

    public $table = 'foss_electricity_order_progress';

    public $connection = 'mysql_gas';

    public $timestamps = false;

    public $incrementing = false;

    public $primaryKey = 'order_id';

    public $fillable = [
        'order_id',
        'third_connector_id',
        'start_time',
        'fetch_time',
        'expected_end_time',
        'end_time',
        'connector_status',
        'current_a',
        'current_b',
        'current_c',
        'voltage_a',
        'voltage_b',
        'voltage_c',
        'total_num',
        'total_electricity_money',
        'total_service_money',
        'total_money',
        'price',
        'remainder_num',
        'end_desc'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'order_id'                => 'integer',
        'third_connector_id'      => 'string',
        'start_time'              => 'datetime',
        'fetch_time'              => 'datetime',
        'expected_end_time'       => 'datetime',
        'end_time'                => 'datetime',
        'connector_status'        => 'string',
        'current_a'               => 'float',
        'current_b'               => 'float',
        'current_c'               => 'float',
        'voltage_a'               => 'float',
        'voltage_b'               => 'float',
        'voltage_c'               => 'float',
        'total_num'               => 'float',
        'total_electricity_money' => 'float',
        'total_service_money'     => 'float',
        'total_money'             => 'float',
        'price'                   => 'float',
        'remainder_num'           => 'float',
        'end_desc'                => 'string'
    ];

    public const OFF_LINE = 10;
    public const IDLE     = 20;
    public const OCCUPIED = 30;
    public const CHARGING = 40;
    public const RESERVED = 50;
    public const FAULT    = 60;
    public const UNKNOWN  = 99;

    public static $connectorStatusDesc = [
        self::OFF_LINE => '离线',
        self::IDLE     => '空闲',
        self::OCCUPIED => '占用(未充电)',
        self::CHARGING => '占用(充电中)',
        self::RESERVED => '占用(预约中)',
        self::FAULT    => '故障',
        self::UNKNOWN  => '未知',
    ];

    /**
     * @param int $orderId
     * @param array $select
     * @param bool $lock
     * @return Builder|ElectricityOrderProgressModel|Model|\Illuminate\Database\Query\Builder|object|null
     */
    public static function getElectricityOrderProgressByOrderId(int $orderId, array $select = ['*'], bool $lock = false)
    {
        if ($lock) {
            return self::where(['order_id' => $orderId])->select($select)->lockForUpdate()->first();
        }
        return self::where(['order_id' => $orderId])->select($select)->first();
    }

    public static function insertElectricityOrderProgress(array $params): bool
    {
        return self::insert($params);
    }

    public static function insertOrUpdateElectricityOrderDetail(int $orderId, array $params): bool
    {
        return (bool)self::updateOrInsert([
            'order_id' => $orderId
        ], $params);
    }
}
