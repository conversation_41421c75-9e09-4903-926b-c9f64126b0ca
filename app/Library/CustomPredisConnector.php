<?php

namespace App\Library;

use App\Library\Helper\Common;
use Illuminate\Support\ServiceProvider;
use Illuminate\Redis\Connectors\PredisConnector;
use Illuminate\Support\Facades\Log;
use Predis\Client as PredisClient;
use Predis\Connection\StreamConnection;

class CustomPredisConnector extends PredisConnector
{
    /**
     * Establish a Redis connection.
     *
     * @param array $config
     * @param array $options
     * @return mixed
     */
    public function connect(array $config, array $options)
    {
        $startTime = microtime(true);
        $predis = parent::connect($config, $options);
        $endTime = microtime(true);
        $connectionTime = bcsub($endTime, $startTime, 10);
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'start_time'            => $startTime,
                'end_time'              => $endTime,
                'redis_config'          => $config,
                'redis_options'         => $options,
                'redis_connection_time' => $connectionTime . 's',
            ]);
        }
        Common::log('info', "Redis连接记录", [
            'start_time'            => $startTime,
            'end_time'              => $endTime,
            'redis_config'          => $config,
            'redis_options'         => $options,
            'redis_connection_time' => $connectionTime . 's',
        ]);
        return $predis;
    }
}
