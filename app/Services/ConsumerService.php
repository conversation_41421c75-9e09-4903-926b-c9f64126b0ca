<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/7
 * Time: 2:53 PM
 */

namespace App\Services;


use Illuminate\Support\Facades\Redis;
use App\Library\Helper\Common;

class ConsumerService
{
    CONST QUEUE = "queue:order:operation";

    protected $handle;

    public function registerHandler(callable $func)
    {
        $this->handle = $func;

        return $this;
    }

    public function start()
    {
        while (true) {
            $message = Redis::rpop(self::QUEUE);
            if (empty($message)) {
                sleep(5);
                continue;
            }

            Common::log('info', 'queue_order_operation_consume 消息 ', [$message]);

            $msg = json_decode($message, true);

            if (empty($msg['TaskID']) || empty($msg['ModuleName']) || empty($msg['FuncName']) || empty($msg['params']) || !is_string($msg['params'])) {
                Common::log('error', 'queue_order_operation_consume 消息格式错误', [$message]);
                continue;
            }

            $param = json_decode($msg['params'], true);
            if (!is_array($param) || count($param) == 0) {
                Common::log('error', 'queue_order_operation_consume 消息格式错误 params 为空', $msg);
                continue;
            }

            switch ($msg['FuncName']) {
                case 'order_update':
                    call_user_func($this->handle, $msg['ModuleName'], $param);
                    break;
            }

            Common::log('info', 'queue_order_operation_consume 消费成功 ', $msg);
        }
    }
}