<?php


namespace App\Models;


use DB;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DriverRefundApplicationRecordModel extends BaseModel
{
    const WAIT_REVIEW = 10;
    const REVIEW_PASSED_AND_REFUND_FAILED = 20;
    const REVIEW_PASSED_AND_REFUND_SUCCEED = 30;
    const REVIEW_REJECTED = 40;
    public static $REVIEW_STATUS = [
        self::WAIT_REVIEW                      => '待审核',
        self::REVIEW_PASSED_AND_REFUND_FAILED  => '审核通过且退款失败',
        self::REVIEW_PASSED_AND_REFUND_SUCCEED => '审核通过且退款成功',
        self::REVIEW_REJECTED                  => '已驳回',
    ];
    public $timestamps = false;
    protected $connection = 'mysql_gas';
    protected $table = 'gas_driver_refund_application_record';
    protected $fillable = [
        'order_no',
        'platform_order_no',
        'applicant',
        'application_reason',
        'reviewer',
        'approval_reason',
        'approval_status',
        'created_at',
        'updated_at',
    ];

    /**
     * @param array $data
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:22 下午
     */
    public static function add(array $data)
    {
        return self::insert($data);
    }

    /**
     * @param int $id
     * @param array $data
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:23 下午
     */
    public static function updateById(int $id, array $data)
    {
        return self::where(['id' => $id])->update($data);
    }

    /**
     * 通过指定条件获取满足条件的第一条记录
     * @param array $whereParams
     * @param string[] $select
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:17 下午
     */
    public static function getOneRecord(array $whereParams, array $select = ['*'], bool $writeLock = false)
    {
        $model = BaseModel::scopeWithCondition(self::select($select), $whereParams);
        if ($writeLock) {

            $model->lockForUpdate();
        }
        return $model->first();
    }

    /**
     * 获取分页数据
     * @param array $whereParams
     * @param int $page
     * @param int $limit
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:20 下午
     */
    public static function getList(array $whereParams = [], $page = 1, $limit = 10): array
    {
        $orderModel = new OrderModel();
        $orderExtModel = new OrderExtModel();
        $model = new DriverRefundApplicationRecordModel();
        $realWhereParams = [];
        foreach ($whereParams as $k => $v) {

            if (empty($v) or is_object($v) or is_array($v)) {

                continue;
            }
            switch ($k) {
                case "approval_status":

                    $approvalStatuses = explode(",", $v);
                    array_walk($approvalStatuses, function (&$v) {
                        $v = (int)$v;
                    });
                    $realWhereParams['a.approval_status'] = $approvalStatuses;
                    break;
                case "order_status":

                    $orderStatuses = explode(",", $v);
                    array_walk($orderStatuses, function (&$v) {
                        $v = (int)$v;
                    });
                    $realWhereParams['b.order_status'] = $orderStatuses;
                    break;
                case "driver_phone":

                    $realWhereParams['b.driver_phone'] = (string)$v;
                    break;
                case "order_no":

                    $realWhereParams['a.order_no'] = (int)$v;
                    break;
                case "platform_order_no":

                    $realWhereParams['a.platform_order_no'] = (string)$v;
                    break;
                case "station_id":

                    $stationIds = explode(",", $v);
                    $realWhereParams['b.station_id'] = $stationIds;
                    break;
            }
        }
        $query = BaseModel::scopeWithCondition(DB::table($model->getTable() . " as a")->select([
            "a.*",
            "b.driver_phone",
            "b.third_actual_fee",
            "b.oil_money",
            "b.order_status",
            "b.oil_num",
            "c.station_name",
            "c.supplier_name",
            "c.goods",
            "b.third_actual_price"
        ]), $realWhereParams);
        $query = $query->leftJoin("{$orderModel->getTable()} as b", "a.order_no",
            "=", "b.order_id")->leftJoin("{$orderExtModel->getTable()} as c",
            "a.order_no", "=", "c.order_id");
        $count = $query->count();
        $data = $query->orderBy("a.created_at", 'DESC')
                      ->offset(($page - 1) * $limit)
                      ->limit($limit)
                      ->get();
        foreach ($data as $v) {

            $v->approval_status_name = self::$REVIEW_STATUS[$v->approval_status];
            $v->order_status_name = OrderModel::$ORDER_STATUS[$v->order_status];
            $v->order_no = (string)$v->order_no;
        }
        return [
            'count' => $count,
            'data'  => $data,
            'page'  => $page,
            'limit' => $limit
        ];
    }

    /**
     * @return HasOne
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 4:14 下午
     */
    public function order(): HasOne
    {
        return $this->hasOne(OrderModel::class, 'order_id', 'order_no');
    }
}