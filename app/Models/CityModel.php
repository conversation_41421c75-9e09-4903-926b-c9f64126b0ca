<?php


namespace App\Models;


class CityModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_city';

    protected $fillable = [
        'id', 'city_code', 'is_price', 'city_name', 'address', 'level', 'parent', 'createtime', 'updatetime',
        'deletetime', 'orderby'
    ];

    /**
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchCityByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}