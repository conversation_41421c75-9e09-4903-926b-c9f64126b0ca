<?php


namespace App\Repositories;


use App\Http\Defines\CouponDefine;
use App\Models\CouponsModel;

class CouponRepository extends EloquentRepository
{

    protected $couponModel;

    public function __construct
    (
        CouponsModel $couponModel
    )
    {
        $this->couponModel = $couponModel;
    }

    /**
     * @param array $condition
     * @param $select
     * @param bool $withLock
     * @return array
     */
    public function getInfo(array $condition, $select, bool $withLock = false)
    {
        return $this->couponModel->getInfo($condition, $select, $withLock);
    }

    /**
     * @param array $condition
     * @param $pluck
     * @return array
     */
    public function getCouponsPluckByCondition(array $condition,$pluck)
    {
        return $this->couponModel->getCouponsPluckByCondition($condition,$pluck);
    }

    /**
     * 根据券标识返回券信息
     * @param $voucher
     * @return CouponsModel | null
     */
    public function getByVoucher($voucher)
    {
        if (empty($voucher))
            return null;

        return $this->couponModel->where('voucher', $voucher)->first();
    }

    public function updateByVoucher($voucher, $data)
    {
        if (empty($voucher) || empty($data))
            return 0;

        return $this->couponModel->where('voucher', $voucher)->update($data);
    }
}