<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OilActModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_act';

    protected $guarded = ["id"];

    protected $fillable = ['id','uniq_code','title','start_time',"end_time","prize_url","condition","createtime","updatetime"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

}