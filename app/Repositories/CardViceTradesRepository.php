<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\CardViceTradesModel;
use App\Models\CardViceTradesZbankModel;

class CardViceTradesRepository extends \App\Repositories\EloquentRepository
{
    protected $cardViceTradesModel;
    protected $cardViceTradesZbankModel;
    public function __construct(CardViceTradesModel $cardViceTradesModel,CardViceTradesZbankModel $cardViceTradesZbankModel)
    {
        $this->cardViceTradesModel = $cardViceTradesModel;
        $this->cardViceTradesZbankModel = $cardViceTradesZbankModel;
    }

    //查询
    public function getTradeById(array $condition)
    {
        $data = $this->cardViceTradesModel->select(['id','api_id','vice_no',"oil_com","oil_name","truck_no","cancel_sn","trade_time","oil_name","trade_money","service_money","total_money",
            "trade_num","trade_place","trade_price","station_code","createtime","org_id","pcode","qz_drivername"])
            ->withCondition($condition)
            ->with(['tradeExt'])
            ->first();
        return $data;
    }

    //查询扣款表的支付信息
    public function getTradeByIdWithZbank(array $condition)
    {
        $data = $this->cardViceTradesZbankModel->select(['id',"vice_no","oil_com","oil_name","truck_no","trade_time","oil_name","trade_money","service_money",
            "trade_num","trade_place","trade_price","createtime","money_type","truename","org_id","is_pay","driver_name"])
            ->withCondition($condition)
            ->first();
        return $data;
    }

    public function getTradeList(array $condition)
    {
        //$this->debugstart('mysql_foss');
        $obj = $this->cardViceTradesModel->leftJoin('oil_card_vice','oil_card_vice_trades.vice_no','=','oil_card_vice.vice_no')
            ->select(['oil_card_vice_trades.id','oil_card_vice_trades.api_id','oil_card_vice_trades.vice_no',"oil_card_vice_trades.oil_com",
                "oil_card_vice_trades.oil_name","oil_card_vice_trades.truck_no","oil_card_vice_trades.cancel_sn","oil_card_vice_trades.trade_time",
                "oil_card_vice_trades.oil_name","oil_card_vice_trades.trade_money","oil_card_vice_trades.service_money",
                "oil_card_vice_trades.total_money","oil_card_vice_trades.trade_num","oil_card_vice_trades.trade_place",
                "oil_card_vice_trades.trade_price","oil_card_vice_trades.station_code","oil_card_vice_trades.createtime",
                "oil_card_vice_trades.account_name","oil_card_vice_trades.pcode"])
            ->where("oil_card_vice_trades.createtime",">=",$condition['bill_time'])
            ->where("oil_card_vice_trades.createtime","<",$condition['bill_end_time'])
            ->where("oil_card_vice.card_level",1)
            ->whereIn('oil_card_vice_trades.qz_drivertel',$condition['driver_phone'])
            ->whereIn('oil_card_vice_trades.oil_com',[20,21]);

        if(isset($condition['card_no']) && $condition['card_no'] != ''){
            $obj->where("oil_card_vice_trades.vice_no",$condition['card_no']);
        }
        if(isset($condition['account_type']) && $condition['account_type'] != ''){
            if($condition['account_type'] == 2){
                $obj->whereNotIn("oil_card_vice_trades.account_name", ["现金账户","储值账户","卡现金账号","储值账号","余额",""]);
            }else {
                $obj->whereIn("oil_card_vice_trades.account_name", ["现金账户","储值账户","卡现金账号","储值账号","余额",""]);
            }
        }
        $data = $obj->orderBy('oil_card_vice_trades.createtime', 'desc')
            ->paginate($condition['limit'],['*'],'page',$condition['page']);
        //$sql = $this->debugend('mysql_foss');
        //print_r($sql);
        return $data;
    }
}