<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

class TripartiteCouponModel extends Model
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'foss_tripartite_coupon';

    protected $guarded = ["id"];

    protected $fillable = ["voucher", 'coupon_type_alias', 'start_time', 'end_time', 'tripartite_voucher',
        'distribute_time', 'charge_off_time', 'tripartite_status', 'updated_time', 'pcode'];

    public $incrementing = false;

    public $timestamps = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return mixed
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id'])) {
            if (is_array($params['id']) && !empty($params['id'])) {
                $query->whereIn('id', $params['id']);
            } elseif ($params['id']) {
                $query->where('id', '=', $params['id']);
            }
        }

        //Search By alias
        if (isset($params['coupon_type_alias'])) {
            if (is_array($params['coupon_type_alias']) && !empty($params['coupon_type_alias'])) {
                $query->whereIn('coupon_type_alias', $params['coupon_type_alias']);
            } elseif ($params['coupon_type_alias']) {
                $query->where('coupon_type_alias', 'like', '%' . $params['coupon_type_alias'] . '%');
            }
        }

        //Search By tripartite_voucher
        if (isset($params['tripartite_voucher']) && $params['tripartite_voucher']) {
            $query->where('tripartite_voucher', 'like', '%' . $params['tripartite_voucher'] . '%');
        }

        //Search By voucher
        if (isset($params['voucher']) && $params['voucher']) {
            $query->where('voucher', 'like', '%' . $params['voucher'] . '%');
        }

        if (!empty($params['charge_off_time_ge'])) {
            $query->where('charge_off_time', '>=', $params['charge_off_time_ge']);
        }

        if (isset($params['charge_off_time_is_not_null'])) {
            $query->whereNotNull('charge_off_time');
        }

        if (isset($params['pcode']) && $params['pcode']) {
            $query->where('pcode', '=', $params['pcode']);
        }

        if (isset($params['tripartite_status']) && $params['tripartite_status']) {
            $query->where('tripartite_status', '=', $params['tripartite_status']);
        }

        return $query;
    }
}