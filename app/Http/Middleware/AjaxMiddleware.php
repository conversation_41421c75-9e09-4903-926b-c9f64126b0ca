<?php

namespace App\Http\Middleware;

use App\Library\Helper\Common;
use App\Servitization\FossUser;
use Closure;
use Ramsey\Uuid\Uuid;

class AjaxMiddleware
{
    protected $fossUser;

    public function __construct
    (
        FossUser $fossUser
    )
    {
        $this->fossUser = $fossUser;
    }

    /**
     * 权限控制中间件
     *
     * @param $request
     * @param Closure $next
     * @return array|mixed
     * @throws \Exception
     */
    public function handle($request, Closure $next)
    {
        Common::log('info','记录header信息', ['param' => $request->header()]);
        //用户信息
        $uid = $request->header('X-G7-Api-Uid');
        $requestSource = $request->header('appkey');
        $appKeyInfo = $this->fossUser->getAppKeyConfig();
        $source = $appKeyInfo['source'][$requestSource] ?? '';
        $roleSource = $appKeyInfo['role_source'][$source] ?? '';

        if (empty($uid) || empty($roleSource)) {
            $data = ['code' => config('errorcode.STATUSCODE_NOTACCESS'), 'msg' => 'FOSS-USER授权失败，无权限访问', 'success' => false, 'data' => []];
            return response()->json($data);
        }
        $request->offsetSet('uid', $uid);
        $requestIp = !empty($request->header('X-Original-Forwarded-For')) ? $request->header('X-Original-Forwarded-For') : (!empty($request->header('X-Real-Ip')) ? $request->header('X-Real-Ip') : ($_SERVER['REMOTE_ADDR'] ?? ''));
        $request->offsetSet('ip', $requestIp);

        //校验用户路由权限
        $headers = [
            'X-G7-Api-Uid' => $uid,
            'appkey' => $requestSource
        ];
        $routeAs = $request->route()->getName();
        $user = $this->fossUser->getAdminBaseInfoByUid(['uid' => $uid,'status' => 1, 'source' => $roleSource], $headers);
        $userName = isset($user['nick_name']) && !empty($user['nick_name']) ? $user['nick_name'] : '系统';
        $request->offsetSet('user_name', $userName);

        $gmsMenu = $this->fossUser->getMenuConfigBySource(['source' => $roleSource]);
        $gmsMenu = collect($gmsMenu)->keyBy('route_as')->toArray();
        if (!empty($routeAs) && !is_null($routeAs)) {
            $routeLegal = $this->fossUser->checkUserPermissionByUid(['route_id' => $gmsMenu[$routeAs]['id'],'source' => $roleSource], $headers);
            if (!$routeLegal) {
                $data = ['code' => config('errorcode.STATUSCODE_DISABLED'), 'msg' => 'FOSS-USER授权失败，无权限访问', 'success' => false, 'data' => []];
                return response()->json($data);
            }

            if (!isset($user['role_id'])) {
                $data = ['code' => config('errorcode.STATUSCODE_NOTACCESS'), 'msg' => 'FOSS-USER授权失败，无权限访问', 'success' => false, 'data' => []];
                return response()->json($data);
            }
        }

        /**
         * 下单渠道枚举
         */
        $sourceNameEnum = [];
        foreach ($appKeyInfo['role_source'] as $key => $value) {
            if (array_key_exists($key, $appKeyInfo['source_name'])) {
                $sourceNameEnum[$value] = $appKeyInfo['source_name'][$key];
            }
        }
        $request->offsetSet('order_channel', $appKeyInfo['role_source'][$source] ?? '');
        $request->offsetSet('order_channel_enum', $sourceNameEnum);

        /**
         * 对接kafka日志
         */
        $headerInfo = [
            'source' => $source,
            'request_id' => !empty($request->header('X-Request-Id')) ? $request->header('X-Request-Id') : Common::uuid(),
            'trace_id' => Uuid::uuid1()->getHex(),
            'uid' => $uid,
            'user_name' => $userName,
            'request_real_ip' => $requestIp,
        ];
        $request->offsetSet('header_info', $headerInfo);
        //lp_do获取用户绑定的组织规则
        $rule=(new FossUser())->getUserRoleRuleByWhere(['id'=>$user['id']],$headers);
        if($rule && isset($rule[0]['rule_id_list']) && count($rule[0]['rule_id_list'])){
            $rule_val=$rule[0]['rule_id_list'][0]['rule_val'];
            $rule_type=$rule[0]['rule_id_list'][0]['rule_type'];
        }else{
            $data = ['code' => config('errorcode.STATUSCODE_NOTACCESS'), 'msg' => 'FOSS-USER授权失败，未绑定组织，无权限访问', 'success' => false, 'data' => []];
            return response()->json($data);
        }
        $request->offsetSet('rule_val', isset($rule_val)?$rule_val:'');
        $request->offsetSet('rule_type', isset($rule_type)?$rule_type:'');
        $request->offsetSet('routes', isset($user['routes'])?$user['routes']:'');
        $request->offsetSet('_user_type', 'employee');
        //end

        return $next($request);
    }
}
