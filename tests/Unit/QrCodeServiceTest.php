<?php

namespace Tests\Unit;

use App\Repositories\QrCodeParseRepository;
use Tests\TestCase;

use RuntimeException;
use App\Http\Defines\CommonError;

class QrCodeServiceTest extends TestCase
{

    public function testQrCodeParseRepositoryFossUser()
    {
        throw new RuntimeException("", CommonError::QR_CODE_INVALID);
        exit;

        $parameters = [
            'qr_code' => 'HT_ddb6fB1NQU1FRBFICAQxRV14DU2gVDBRPV1JVDQdRBQ1bUw'
        ];

        $parseResult = QrCodeParseRepository::fossUser($parameters['qr_code']);

        print_r($parseResult);
    }

}
