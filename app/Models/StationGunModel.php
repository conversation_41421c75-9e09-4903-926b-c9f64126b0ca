<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationGunModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_gun';

    protected $guarded = ["id"];

    protected $fillable = ['id','name',"is_lock","brand","gpsno"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function station()
    {
        return $this->belongsTo(StationModel::class, 'station_id', 'id');
    }

    public function tank()
    {
        return $this->belongsTo(StationTank::class, 'tank_id', 'id');
    }

    /**
     * 获取1条枪信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneGunByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 获取多条枪信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchGunByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}