<?php


namespace App\Repositories;


use App\Library\Request;
use App\Models\OrderExtModel;
use App\Models\OrderModel;

class NewOrderExtRepository
{
    protected $orderExtModel;

    public function __construct
    (
        OrderExtModel $ext
    )
    {
        $this->orderExtModel = $ext;
    }

    /**
     * 新增数据
     *
     * @param array $insertArray
     * @return mixed
     */
    public function insertOrderExt(array $insertArray)
    {
        return $this->orderExtModel->insertOrderExt($insertArray);
    }

    /**
     * 修改订单扩展信息
     *
     * @param $orderId
     * @param $updateArray
     * @return mixed
     */
    public function updateOrderExtByOrderId($orderId, $updateArray)
    {
        return $this->orderExtModel->updateOrderExtByOrderId($orderId, $updateArray);
    }

    public function getInfoByOrderId($orderId)
    {
        return $this->orderExtModel->getOrderExtInfo($orderId);
    }
}