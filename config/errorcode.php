<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/24
 * Time: 21:50
 **/
return [
    'STATUSCODE_SUCCESS'                   => 0,
    'STATUSCODE_FAILED'                    => 100,
    'STATUSCODE_ERROR'                     => 101,
    'STATUSCODE_WITHDRAW_ERROR'            => 102,
    'STATUSCODE_DISABLED'                  => 403,//禁用
    'STATUSCODE_NOTLOGIN'                  => 400,
    'STATUSCODE_NOTACCESS'                 => 401,//无权限
    'STATUSCODE_PASSWDERROR'               => 402,//用户名或密码错误
    'STATUSCODE_PASSWDERROR_UPDATE'        => 403,//
    'STATUSCODE_INTERFACE_NOT_FOUND'       => 404,
    'STATUSCODE_CONFIRM_PASSWD'            => 405,
    'STATUSCODE_ACCOUNT_EXISTS'            => 406,
    'STATUSCODE_ROLE_NOTEXISTS'            => 407,
    'STATUSCODE_ROLE_EXISTS'               => 408,
    'STATUSCODE_ONESELF_NO_DEL'            => 409,

    /**
     * 规则补充
     */
    'SERVICE_CODE' => '31', // foss-user服务码
    'BUSINESS_CODE' => [ // 业务码
        'BUSINESS_CARD' => '01', // 油卡
        'BUSINESS_WECHAT' => '02', // 微信端
        'BUSINESS_ORDER' => '03', // 订单
        'BUSINESS_LOG' => '04', // 交易流水
        'ADDITIONAL_RECORD' => '05', // 自营站点补单记录
        'ORDER' => '06', // 订单
    ],
    'CHILD_CODE' => [ // 子码,建议以接口功能拆分
        '01' => [
            'DOPER_CHECK_CARD' => '1001', // PDA验码
            'DOPER_GET_GOODS' => '1002', // 加油员扫码后获取商品信息
        ],
        '02' => [
            'WECHAT_CHECK_CODE' => '1001', // 获取微信auth验证信息
        ],
        '03' => [
            'CREATE_ORDER' => '1001',
        ],
        '04' => [
            'APPROVE_SELECT' => '1001', // 审核列表查询
            'APPROVE_INSERT_UPDATE' => '1002', // 创建｜编辑审核单
            'APPROVE' => '1003', // 审核
        ],
        '05' => [
            'HANDLE_ADDITIONAL_RECORD' => '1001', // 创建|编辑 补单
            'HANDLE_ITEM_RECORD' => '1002', // 补单详情
        ],
        '06' => [
            'CREATE_ORDER' => '1001',
            'CLOSE_ORDER' => '1002',
            'GOTO_PAY' => '1003',
            'GOTO_REFUND' => '1004',
            'GET_GOODS' => '1005',
        ]
    ],
];
/**
 *
 *  200 ok: 服务器成功返回用户请求的数据;
 *         401 Unauthorized：表示用户没有权限（令牌、用户名、密码错误）
 *         403 Forbidden: 表示用户得到授权，但是访问是被禁止的
 *         429 Too Many Requests：请勿频繁请求
 *         500 INTERNAL SERVER ERROR：服务器发生错误
 */
