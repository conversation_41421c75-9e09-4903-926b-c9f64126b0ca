<?php


namespace App\Services;

use App\Exceptions\ParamInvalidException;
use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Jobs\OrderTimeOut;
use App\Library\Helper\Common;
use App\Library\Helper\NumberUtil;
use App\Library\Helper\ProvinceAndCityUtil;
use App\Library\Request;
use App\Models\BaseModel;
use App\Models\CityModel;
use App\Models\DayOrderReconciliationSnapshotModel;
use App\Models\DriverRefundApplicationRecordModel;
use App\Models\HistoryModel;
use App\Models\OilOrgModel;
use App\Models\OrderExtendModel;
use App\Models\OrderModel;
use App\Repositories\CardRepository;
use App\Repositories\CityRepository;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrderExtendRepository;
use App\Repositories\StationLimitConfigRepository;
use App\Repositories\StationRepository;
use App\Repositories\SupplierRepositories;
use App\Servitization\Adapter;
use App\Servitization\FeiShu;
use App\Servitization\Foss;
use App\Servitization\FossApi;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use App\Servitization\Gos;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Library\Monitor\Falcon;
use RuntimeException;
use Throwable;

class NewOrderService
{
    protected $redisService;
    protected $errorCodeService;
    protected $stationService;
    protected $paymentService;
    protected $messageService;
    protected $truckCardService;
    protected $stationRepository;
    protected $supplierRepositories;
    protected $cityRepository;
    protected $newOrderRepository;
    protected $newOrderExtRepository;
    protected $cardRepository;
    protected $orderExtendRepository;
    protected $fossUser;
    protected $fossStation;
    protected $foss;
    protected $adapter;
    protected $feishu;
    protected $fossApi;
    protected $provinceAndCityUtil;
    protected $numberUtil;
    protected $cardService;
    protected $refundService;
    protected $couponSrv;
    protected $truckSrv;
    protected $limitRepository;

    public function __construct
    (
        RedisService          $redisService,
        ErrorCodeService      $errorCodeService,
        StationService        $stationService,
        PaymentService        $paymentService,
        MessageService        $messageService,
        TruckCardService      $truckCardService,
        StationRepository     $stationRepository,
        CityRepository        $cityRepository,
        SupplierRepositories  $supplierRepositories,
        NewOrderRepository    $newOrderRepository,
        CardRepository        $cardRepository,
        OrderExtendRepository $orderExtendRepository,
        FossUser              $fossUser,
        FossStation           $fossStation,
        Foss                  $foss,
        Adapter               $adapter,
        FeiShu                $feishu,
        FossApi               $fossApi,
        ProvinceAndCityUtil   $provinceAndCityUtil,
        NumberUtil            $numberUtil,
        NewOrderExtRepository $ext,
        CardService           $card,
        RefundService         $refundService,
        CouponService         $couponSrv,
        TruckCardService      $truckSrv,
        StationLimitConfigRepository $limitRepository
    )
    {
        $this->redisService = $redisService;
        $this->errorCodeService = $errorCodeService;
        $this->stationService = $stationService;
        $this->paymentService = $paymentService;
        $this->messageService = $messageService;
        $this->truckCardService = $truckCardService;
        $this->stationRepository = $stationRepository;
        $this->supplierRepositories = $supplierRepositories;
        $this->cityRepository = $cityRepository;
        $this->newOrderRepository = $newOrderRepository;
        $this->cardRepository = $cardRepository;
        $this->orderExtendRepository = $orderExtendRepository;
        $this->fossUser = $fossUser;
        $this->fossStation = $fossStation;
        $this->foss = $foss;
        $this->adapter = $adapter;
        $this->feishu = $feishu;
        $this->fossApi = $fossApi;
        $this->provinceAndCityUtil = $provinceAndCityUtil;
        $this->numberUtil = $numberUtil;
        $this->newOrderExtRepository = $ext;
        $this->cardService = $card;
        $this->refundService = $refundService;
        $this->couponSrv = $couponSrv;
        $this->truckSrv = $truckSrv;
        $this->limitRepository = $limitRepository;
    }

    /**
     * 校验站点上笔订单状态
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function checkStationLatestOrder($params)
    {
        $stationId = $params['station_id'];
        $result = $this->newOrderRepository->getLatestOrder(['station_id' => $stationId]);

        if (!empty($result) && ($result['order_status'] == OrderModel::WAIT_PAY)) {
            throw new Exception('该站点有一笔订单收款未成功!请先完成上笔订单!', $this->errorCodeService->checkGoods(40305));
        }

        return true;
    }

    /**
     * 获取商品信息
     *
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getGoodsLimit($params)
    {
        $stationId = $params['station_id'];
        $orderToken = $params['order_token'];
        /**
         * 权限验证
         */
        $stationIds = Request::get('station_id_array');
        if (!in_array($stationId, $stationIds)) {
            throw new Exception('获取商品信息失败,无权限使用该站点,请联系管理员!', $this->errorCodeService->checkGoods(40301));
        }

        $orderTokenInfo = $this->redisService->parseCreateOrderToken($orderToken);

        /**
         * 拼接数据
         */
        // 站
        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $stationId]);
        if (empty($stationInfo)) {
            throw new Exception('获取商品信息失败,无效的站点ID!', $this->errorCodeService->checkGoods(40302));
        }
        // 价
        $cardAndAccount = $this->fossUser->getCardInfoWithCurrAccount(['card_no' => $orderTokenInfo['card_no']]);
        if (empty($cardAndAccount)) {
            throw new Exception('获取商品信息失败,无效的账号!', $this->errorCodeService->checkGoods(40303));
        }
        // 20 G7能源储值账户(储值卡) 21 G7能源共享账户(共享卡)
        $cardBalance = $cardAndAccount['card_balance'];
        // 限额处理
        if ($cardAndAccount['paylimit'] == 1) {
            $minMoney = min($cardAndAccount['day_top'], $cardAndAccount['month_top'], $cardAndAccount['oil_top']);
            $cardBalance = ($cardBalance > $minMoney) ? $minMoney : $cardBalance;
        }
        $showCardBalance = 1;
        if (strcmp($cardAndAccount['card_type'], 20) && ($cardBalance > 10000)) {
            $cardBalance = "0.00";
            $showCardBalance = 0;
        }

        $orgcode = $cardAndAccount['orgcode'];
        $limitOrgCode = $orgcode;
        //针对二维码信息已埋订单号
        $order_no = array_get($orderTokenInfo, 'order_no', '');
        if (!empty($order_no)) {
            $orderInfo = $this->newOrderRepository->getLatestOrder(['order_id' => $order_no], ["order_id", "order_flag"], true);
            //如果是卡车订单，需要取通用客户订单
            if (array_get($orderInfo, "order_flag", '') == CardTradeConf::TRUCKFLAG) {
                $orgcode = "";
            }
        }

        // 商品
        $orgcode = $this->newOrderRepository::getSpecialPriceOrg($stationId, $orgcode, substr($orgcode, 0, 6));
        $stationPriceInfo = $this->fossStation->getOilListAndPrice(['station_id' => $stationId, 'orgcode' => $orgcode, 'limit_orgcode' => $limitOrgCode]);
        if (empty($stationPriceInfo)) {
            throw new Exception('车队长禁止司机在此站加油,请联系车队长', $this->errorCodeService->createOrderError(40317));
        }
        $goods = [];
        foreach ($stationPriceInfo as $item) {
            $mac_price = array_get($item, "mac_price", 0);
            if ($mac_price < 2) {
                $mac_price = array_get($item, "platform_price", '--');
            }
            $goods[] = [
                'goods_name'  => $item['oil_title_name'],
                'goods_price' => $item['platform_price'] ?? "--", // 油品不可用时
                'mac_price'   => $mac_price,
                'can_use'     => empty($item['can_use']) ? 0 : 1,
                'oil_type'    => $item['oil_type'],
                'oil_name'    => $item['oil_name'],
                'oil_level'   => $item['oil_level'],
            ];
        }

        return [
            'station_name'              => empty($stationInfo['remark_name']) ? $stationInfo['station_name'] : $stationInfo['remark_name'],
            'truck_no'                  => $orderTokenInfo['truck_no'],
            'is_access_platform_driver' => empty($orderTokenInfo['is_access_platform_driver']) ? 0 : 1, //是否平台司机
            'show_service_price'        => empty(strcmp($cardAndAccount['account_type'], 20)) ? 1 : 0, //授信账户存在服务费
            'is_check_password'         => empty(strcmp($cardAndAccount['ischeck'], 1)) ? 1 : 0, //是否验密
            'show_card_balance'         => $showCardBalance,
            'card_balance'              => $cardBalance,
            'goods'                     => $goods,
            'service_price'             => empty($cardAndAccount['service_rate']) ? 0.00 : $cardAndAccount['service_rate']
        ];
    }

    /**
     * 下单
     *
     * @param $params
     * @return array
     * @throws Exception
     */
    public function createOrder($params)
    {
        $token = array_get($params, 'order_token', '');
        $cardNo = array_get($params, 'card_no', 0);
        $orderType = array_get($params, 'order_type', 0);
        $orderChannel = Request::get('order_channel');
        $stationId = array_get($params, 'station_id', '');
        $oilType = array_get($params, 'oil_type', '');
        $oilName = array_get($params, 'oil_name', '');
        $oilLevel = array_get($params, 'oil_level', '');
        $oilUnit = array_get($params, 'oil_unit', 0);
        $oilNum = array_get($params, 'oil_num', 0);
        $oilPrice = array_get($params, 'oil_price', 0);
        $oilMoney = array_get($params, 'oil_money', 0);
        $servicePrice = array_get($params, 'service_price', 0);
        $serviceMoney = array_get($params, 'service_money', 0);
        $operator = Request::get('user_name');
        $operatorId = Request::get('uid');

        /**
         * token获取账号等信息
         */
        $tokenInfo = [];
        if (empty($cardNo)) {
            $tokenInfo = $this->redisService->parseCreateOrderToken($token);
            $cardNo = array_get($tokenInfo, 'card_no', 0);
        }

        try {
            /**
             * 基础参数校验
             */
            if (!array_key_exists($orderType, OrderModel::$ORDER_TYPE)) {
                throw new Exception('下单失败,无效的订单类型,请参考:' . implode(',', OrderModel::$ORDER_TYPE), $this->errorCodeService->createOrderError(40301));
            }
            if (!array_key_exists($oilUnit, OrderModel::$OIL_UNIT)) {
                throw new Exception('下单失败,无效的商品单位,请参考:' . implode(',', OrderModel::$OIL_UNIT), $this->errorCodeService->createOrderError(40303));
            }
            // 定金额(加油升数小数点后舍去)
            if ($oilUnit == OrderModel::FIXED_MONEY) {
                $realOilNum = bcdiv($oilMoney, $oilPrice, 6);
                if (abs($this->numberUtil->formatNumber($realOilNum, 2) - $oilNum)) {
                    throw new Exception('下单失败,指定金额加油:加油升数' . $oilNum . '和可加升数' . round($realOilNum, 2) . '不一致,请核对!', $this->errorCodeService->createOrderError(40304));
                }
                // 定升数(加油金额四舍五入)
            } else {
                $realOilNum = $oilNum;
                if (abs(round(bcmul($oilNum, $oilPrice, 3), 2) - $oilMoney)) {
                    throw new Exception('下单失败,指定升数加油:加油金额' . $oilMoney . '和可加金额' . round(bcmul($oilNum, $oilPrice, 3), 2) . '不一致,请核对!', $this->errorCodeService->createOrderError(40305));
                }
            }
            // 服务费
            if (abs(round(bcmul($oilNum, $servicePrice, 3), 2) - $serviceMoney)) {
                throw new Exception('下单失败,加油服务费' . $serviceMoney . '和理论服务费' . round(bcmul($oilNum, $oilPrice, 3), 2) . '不一致,请核对!', $this->errorCodeService->createOrderError(40306));
            }

            /**
             * G7能源账户存在校验账户相关(账户、扣款账户机构特殊定价)
             */
            if (!empty($cardNo)) {
                $checkCardAndGetArray = $this->cardService->checkCardAndGetArray($cardNo, $servicePrice, $oilMoney, $serviceMoney);

                //灰度方式验证新下单流程
                $orgCode = $checkCardAndGetArray['insert_card_array']['org_code'];
                if (CommonDefine::grayOrderOrg($orgCode)) {
                    $params['operator'] = $operator;
                    $params['operator_id'] = $operatorId;
                    return $this->proxyOrder($params);
                }

                // 特殊定价
                $checkStationAndGetArray = $this->checkStationAndGetArray($stationId, $oilType, $oilName, $oilLevel, $oilPrice, $checkCardAndGetArray['insert_card_array']['org_code']);
            } else {
                $checkCardAndGetArray = [
                    'insert_card_array'   => '',
                    'insert_card_mirror'  => '',
                    'extends_driver_info' => ''
                ];
                // 公共定价
                $checkStationAndGetArray = $this->checkStationAndGetArray($stationId, $oilType, $oilName, $oilLevel, $oilPrice);
            }

            /**
             * 重复下单校验
             */
            $whereParams = [
                'station_id'     => $stationId,
                'oil_type'       => $oilType,
                'oil_name'       => $oilName,
                'oil_level'      => $oilLevel,
                'card_no'        => $cardNo,
                'driver_phone'   => array_get($tokenInfo, 'driver_phone', array_get($checkCardAndGetArray, 'extends_driver_info.driver_phone', '')),
                'oil_price'      => $oilPrice,
                'oil_num'        => $oilNum,
                'order_status'   => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
                'ge_create_time' => date('Y-m-d H:i:s', time() - 60),
                'le_create_time' => date('Y-m-d H:i:s'),
            ];
            $existsOrder = $this->newOrderRepository->getOneOrderByParams($whereParams);
            if (!empty($existsOrder)) {
                throw new Exception('订单已存在,请勿重复下单!', $this->errorCodeService->createOrderError(40322));
            }

            /**
             * 下单
             */
            $orderId = $this->redisService->makeOrderId();
            $driverSource = isset($tokenInfo['is_access_platform_driver']) ? (empty($tokenInfo['is_access_platform_driver']) ? OrderModel::OWN_DRIVER : OrderModel::THIRD_DRIVER) : OrderModel::OWN_DRIVER;

            // trade_mode 处理
            if ($driverSource == OrderModel::THIRD_DRIVER) {
                $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_XYS_SMP;
            } else {
                $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_ZYS_SMP;
            }

            $makeOrder = [
                'order_id'      => $orderId,
                'order_sn'      => (string)$orderId,
                'order_type'    => $orderType,
                'order_channel' => $orderChannel,
                'trade_mode'    => $tradeMode,
                'trade_no'      => $this->redisService->makeSerialNum(),
                'order_status'  => OrderModel::WAIT_PAY,
                'station_id'    => $stationId,
                'station_code'  => '',
                'province_code' => '',
                'city_code'     => '',
                'supplier_code' => '',
                'org_code'      => '',
                'card_no'       => $cardNo,
                'card_type'     => '',
                'card_level'    => '',
                'driver_name'   => ($checkCardAndGetArray['insert_card_array']['card_level'] == OrderModel::CAR_CARD) ? array_get($checkCardAndGetArray, 'extends_driver_info.driver_name', $tokenInfo['driver_name']) : $tokenInfo['driver_name'],
                'driver_source' => $driverSource,
                'driver_phone'  => ($checkCardAndGetArray['insert_card_array']['card_level'] == OrderModel::CAR_CARD) ? array_get($checkCardAndGetArray, 'extends_driver_info.driver_phone', $tokenInfo['driver_phone']) : $tokenInfo['driver_phone'],
                'truck_no'      => array_get($tokenInfo, 'truck_no', array_get($checkCardAndGetArray, 'extends_driver_info.truck_no', '')),
                'oil_type'      => $oilType,
                'oil_name'      => $oilName,
                'oil_level'     => $oilLevel,
                'oil_unit'      => $oilUnit,
                'oil_num'       => $oilNum,
                'oil_price'     => $oilPrice,
                'oil_money'     => $oilMoney,
                'real_oil_num'  => $realOilNum,
                'service_price' => $servicePrice,
                'service_money' => $serviceMoney,
                'mirror'        => '',
                'order_flag'    => 'G7',
                'creator'       => $operator,
                'updator'       => $operator
            ];

            $makeOrder = array_merge($makeOrder, $checkCardAndGetArray['insert_card_array'], $checkStationAndGetArray['insert_station_array']);

            $mirror = [
                'order_id'           => $orderId,
                'order_channel_name' => Request::get('order_channel_enum')[$orderChannel],
                'org_name'           => '',
                'card_type_name'     => '',
                'card_level_name'    => '',
                'account_type_name'  => '',
                'account_name'       => '',
                'station_name'       => '',
                'remark_name'        => '',
                'station_address'    => '',
                'rebate_grade'       => '',
                'supplier_name'      => '',
                'province_name'      => '',
                'city_name'          => '',
                'oil_type_name'      => '',
                'oil_name_name'      => '',
                'oil_level_name'     => '',
                'goods'              => '',
                'gun_id'             => '',
                'gun_name'           => '',
                'tank_id'            => '',
                'tank_name'          => '',
                'supplier_price'     => '',
                'platform_price'     => '',
                'price_id'           => '',
                'list_price'         => '',
                'is_check_password'  => '',
                'lng'                => '',
                'lat'                => '',
                'order_token'        => $token,
                'operator_id'        => $operatorId,
                'oil_time'           => empty($params['oil_time']) ? date('Y-m-d H:i:s') : $params['oil_time'],
            ];
            $mirror = array_merge($mirror, $checkCardAndGetArray['insert_card_mirror'], $checkStationAndGetArray['insert_station_mirror']);

            $this->addOrderNew($makeOrder);
            $this->addOrderExt($mirror);
            $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
//            $result = $this->newOrderRepository->insertOrder($makeOrder);
//            if (!$result) {
//                throw new \Exception('下单失败,请重试!', $this->errorCodeService->createOrderError(40323));
//            }
            /**
             * 下单成功,token失效
             */
            $this->redisService->delCreateOrderToken($token);
            /**
             * 下单成功,5min超时关单
             */
            dispatch(new OrderTimeOut($orderId))->delay(300)->onQueue('order-queue');
        } catch (Exception $e) {
            throw $e;
        }

        $data = [
            'order_id'      => (string)$orderId,
            'station_name'  => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
            'truck_no'      => $makeOrder['truck_no'],
            'driver_phone'  => $makeOrder['driver_phone'],
            'card_no'       => $makeOrder['card_no'],
            'goods'         => $mirror['goods'],
            'oil_price'     => $makeOrder['oil_price'],
            'oil_num'       => $makeOrder['oil_num'],
            'oil_money'     => $makeOrder['oil_money'],
            'service_price' => $makeOrder['service_price'],
            'service_money' => $makeOrder['service_money'],
            'total_money'   => round(bcadd($makeOrder['service_money'], $makeOrder['oil_money'], 3), 2),
            'update_time'   => time(),
            'creator'       => $makeOrder['creator'],
        ];

        /**
         * 支付结果
         */
        try {
            if ($driverSource == OrderModel::THIRD_DRIVER) {
                // 下游司机
                $extends = array_get($tokenInfo, 'extends', '');
                $this->oaCreateOrder($makeOrder, $extends);
                // push消息
                $code = 201;
                $msg = '等待客户机构处理订单中';
                $this->messageService->pushMsgToDoper($token, [
                    'code' => $code,
                    'msg'  => $msg
                ]);
            } else {
                // 自营司机
                // 验密
                if ($mirror['is_check_password']) {
                    // 司机端小程序
                    $this->driverCheckPassword($cardNo, $orderId);
                    // 微信卡包
                    $this->cardDriverCheckPassword($cardNo, $orderId);
                    $code = 201;
                    $msg = '请和司机确认是否需要输入密码。若无密码，请耐心等待支付结果。';
                    // push消息
                    $this->messageService->pushMsgToDoper($token, [
                        'code' => $code,
                        'msg'  => $msg
                    ]);
                } else {
                    $makeOrder['card_password'] = '';
                    $makeOrder['original_order_id'] = $orderId;
                    $this->paymentService->fossPay($makeOrder, HistoryModel::COST_SUCCESS);
                    $code = 200;
                    $msg = '扣款成功';

                    $this->messageService->driverPaySuccess($cardNo, $orderId);
                }
            }
        } catch (Exception $e) {
            $code = 403;
            $msg = $e->getMessage();
            // push消息
            if ($mirror['is_check_password'] || ($driverSource == OrderModel::THIRD_DRIVER)) {
                $this->messageService->pushMsgToDoper($token, [
                    'code' => $code,
                    'msg'  => $msg,
                    'data' => $data,
                ]);
            }
        }

        return [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ];
    }

    /**
     * OA主动下单
     *
     * @param $params
     * @return string
     * @throws Exception
     */
    public function createOrderByOA($params)
    {
        $cardNo = array_get($params, 'card_no', 0);
        $stationId = array_get($params, 'station_id', '');
        $oilType = array_get($params, 'oil_type', '');
        $oilName = array_get($params, 'oil_name', '');
        $oilLevel = array_get($params, 'oil_level', '');
        $oilUnit = array_get($params, 'oil_unit', 0);
        $oilNum = array_get($params, 'oil_num', 0);
        $oilPrice = array_get($params, 'oil_price', 0);
        $oilMoney = array_get($params, 'oil_money', 0);
        $operator = array_get($params, 'creator', '');
        // todo 非必填
        $driver_name = array_get($params, 'driver_name', '');
        $driver_phone = array_get($params, 'driver_phone', '');
        $truck_no = array_get($params, 'truck_no', '');
        // todo 系统默认
        $orderType = array_get($params, 'order_type', 1);// 司机主动付款
        $orderChannel = array_get($params, 'order_channel', 6);// OA
        $servicePrice = array_get($params, 'service_price', 0);// 无服务费
        $serviceMoney = array_get($params, 'service_money', 0);

        // todo 加锁
        $this->redisService->setOaCreateOrderLock($cardNo . $params['third_order_id']);
        try {
            /**
             * 参数校验
             */
            if (!array_key_exists($orderType, OrderModel::$ORDER_TYPE)) {
                throw new Exception('下单失败,无效的订单类型,请参考:' . implode(',', OrderModel::$ORDER_TYPE), $this->errorCodeService->createOrderError(40301));
            }
            // 定金额(加油升数小数点后舍去)
            if ($oilUnit == OrderModel::FIXED_MONEY) {
                $realOilNum = bcdiv($oilMoney, $oilPrice, 6);
                if (abs($this->numberUtil->formatNumber($realOilNum, 2) - $oilNum)) {
                    throw new Exception('下单失败,指定金额加油:加油升数' . $oilNum . '和可加升数' . round($realOilNum, 2) . '不一致,请核对!', $this->errorCodeService->createOrderError(40304));
                }
                // 定升数(加油金额四舍五入)
            } else {
                $realOilNum = $oilNum;
                if (abs(round(bcmul($oilNum, $oilPrice, 3), 2) - $oilMoney)) {
                    throw new Exception('下单失败,指定升数加油:加油金额' . $oilMoney . '和可加金额' . round(bcmul($oilNum, $oilPrice, 3), 2) . '不一致,请核对!', $this->errorCodeService->createOrderError(40305));
                }
            }

            $checkCardAndGetArray = $this->cardService->checkCardAndGetArray($cardNo, $servicePrice, $oilMoney, $serviceMoney);
            // 特殊定价
            $checkStationAndGetArray = $this->checkStationAndGetArray($stationId, $oilType, $oilName, $oilLevel, $oilPrice, $checkCardAndGetArray['insert_card_array']['org_code']);

            /**
             * 重复下单校验
             */
            $whereParams = [
                'third_order_id' => $params['third_order_id'],
                'org_code'       => $checkCardAndGetArray['insert_card_array']['org_code'],
                'order_status'   => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
            ];
            $existsOrder = $this->newOrderRepository->getOneOrderByParams($whereParams);
            if (!empty($existsOrder)) {
                throw new Exception('订单已存在,请勿重复下单!', $this->errorCodeService->createOrderError(40322));
            }

            /**
             * 下单
             */
            $orderId = $this->redisService->makeOrderId();
            $makeOrder = [
                'order_id'       => $orderId,
                'order_sn'       => (string)$orderId,
                'order_type'     => $orderType,
                'order_channel'  => $orderChannel,
                'trade_mode'     => CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP,
                'trade_no'       => $this->redisService->makeSerialNum(),
                'order_status'   => OrderModel::WAIT_PAY,
                'station_id'     => $stationId,
                'station_code'   => '',
                'province_code'  => '',
                'city_code'      => '',
                'supplier_code'  => '',
                'org_code'       => '',
                'card_no'        => $cardNo,
                'card_type'      => '',
                'card_level'     => '',
                'driver_name'    => $driver_name,
                'driver_source'  => OrderModel::THIRD_DRIVER,
                'driver_phone'   => $driver_phone,
                'truck_no'       => $truck_no,
                'oil_type'       => $oilType,
                'oil_name'       => $oilName,
                'oil_level'      => $oilLevel,
                'oil_unit'       => $oilUnit,
                'oil_num'        => $oilNum,
                'oil_price'      => $oilPrice,
                'oil_money'      => $oilMoney,
                'real_oil_num'   => $realOilNum,
                'service_price'  => $servicePrice,
                'service_money'  => $serviceMoney,
                'mirror'         => '',
                'creator'        => $operator,
                'updator'        => $operator,
                'third_order_id' => $params['third_order_id'],
            ];

            $makeOrder = array_merge($makeOrder, $checkCardAndGetArray['insert_card_array'], $checkStationAndGetArray['insert_station_array']);

            $mirror = [
                'order_id'           => $orderId,
                'order_channel_name' => 'OA',
                'org_name'           => '',
                'card_type_name'     => '',
                'card_level_name'    => '',
                'account_type_name'  => '',
                'account_name'       => '',
                'station_name'       => '',
                'lng'                => '',
                'lat'                => '',
                'remark_name'        => '',
                'station_address'    => '',
                'rebate_grade'       => '',
                'supplier_name'      => '',
                'province_name'      => '',
                'city_name'          => '',
                'oil_type_name'      => '',
                'oil_name_name'      => '',
                'oil_level_name'     => '',
                'goods'              => '',
                'gun_id'             => '',
                'gun_name'           => '',
                'tank_id'            => '',
                'tank_name'          => '',
                'supplier_price'     => '',
                'platform_price'     => '',
                'price_id'           => '',
                'list_price'         => '',
                'is_check_password'  => '',
            ];
            $mirror = array_merge($mirror, $checkCardAndGetArray['insert_card_mirror'], $checkStationAndGetArray['insert_station_mirror']);

            $this->addOrderNew($makeOrder);
            $this->addOrderExt($mirror);
            $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
//            $result = $this->newOrderRepository->insertOrder($makeOrder);
//            if (!$result) {
//                throw new \Exception('下单失败,请重试!', $this->errorCodeService->createOrderError(40323));
//            }

            /**
             * 下单成功,5min超时关单
             */
            dispatch(new OrderTimeOut($orderId))->delay(300)->onQueue('order-queue');
            // todo 解锁
            $this->redisService->delOaCreateOrderLock($cardNo . $params['third_order_id']);
            return $orderId;
        } catch (Exception $e) {
            $this->redisService->delOaCreateOrderLock($cardNo . $params['third_order_id']);
            throw $e;
        }
    }

    /**
     * 客户查看自己的历史订单记录
     */
    public function getNewOrderPaginate($params)
    {
        $userList = (new FossUser())->getUserInfo(['uid' => $params['user_id']]);
        if (!isset($userList['mobile']) || empty($userList) || empty($userList['history_mobiles'])) {
            throw new \RuntimeException('', CommonError::USER_ERROR);
        }
        $params['driver_phone'] = $userList['history_mobiles'];
        $result = self::getOrderPaginate($params);
        return $result;
    }

    /**
     * 订单列表
     *
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getOrderPaginate($params)
    {
        $page = array_get($params, 'page', 1);
        $limit = array_get($params, 'limit', 10);

        $selectParams = $this->getOrderSelectParams($params, $limit);
        if (($selectParams['select'] == true) && ($selectParams['hit'] == false)) {
            return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
        }
        $selectParams['selectParams']['client_source'] = array_get($params, "appkey", "");
        $result = $this->newOrderRepository->getOrderPaginate($selectParams['selectParams'], ['*'], $page, $limit, true);
        if (empty($result)) {
            return [
                'data'                 => [],
                'count'                => 0,
                'page'                 => $page,
                'limit'                => $limit,
                'total_oil_num'        => "0.00",
                'total_oil_money'      => "0.00",
                "total_service_money"  => "0.00",
                "total_supplier_money" => "0.00",
            ];
        }

        $ids = [];
        foreach ($result['data'] as $item) {
            $ids[] = $item['order_id'];
        }
        $viceCardInfos = (new Foss())->getViceInfoByOrderIds(['order_ids' => $ids]);
        $orgInfo = (new OilOrgModel())->getBatchOrgByParams([
            'orgcode' => array_column(
                $result['data'],
                'org_code'
            )
        ], ['is_system_orgcode', 'orgcode'])->keyBy('orgcode');
        $needArray = [];
        foreach ($result['data'] as $item) {
            if (!empty($item['mirror'])) {
                $mirror = json_decode($item['mirror'], true);
                $channelName = $mirror['order_channel_name'];
            } else {
                $mirror = $item['ext'];
                $channelName = CardTradeConf::getFrom($item['order_channel']);
            }

            $mirror['upstream_settle_price'] = $viceCardInfos[$item['order_id']]['upstream_settle_price'] ?? '0.00';
            $mac_amount = array_get($mirror, "mac_amount", 0);
            if (empty($mac_amount) || bccomp($mac_amount, 0, 2) == 0) {
                $mac_amount = $item['oil_money'];
            }

            $tmp = [
                'order_id'                => (string)$item['order_id'],
                'order_status'            => $item['order_status'],
                'order_status_name'       => OrderModel::$ORDER_STATUS[$item['order_status']],
                'station_name'            => $mirror['station_name'],
                'remark_name'             => $mirror['remark_name'],
                'station_code'            => $item['station_code'],
                'supplier_name'           => $mirror['supplier_name'],
                'goods'                   => $mirror['goods'],
                'total_money'             => sprintf('%.2f', round(bcadd($item['oil_money'], $item['service_money'], 3), 2)),
                'oil_money'               => $item['oil_money'],
                'supplier_money'          => sprintf('%.2f', round(bcmul($item['real_oil_num'], $mirror['supplier_price'], 3), 2)), // 站点应收
                'service_money'           => $item['service_money'],
                'oil_price'               => $item['oil_price'],
                'supplier_price'          => $mirror['supplier_price'],
                'service_price'           => $item['service_price'],
                'oil_num'                 => $item['oil_num'],
                'card_no'                 => (strtoupper(array_get(Request::get('header_info'), 'source', 'PDA-H5')) == 'PDA-H5') ? $this->numberUtil->formatPhoneNo($item['card_no'], 4, 6) : $item['card_no'],
                'org_name'                => $mirror['org_name'],
                'driver_name'             => empty($item['driver_name']) ? '--' : $item['driver_name'],
                'driver_phone'            => empty($item['driver_phone']) ? '--' : (strtoupper(array_get(Request::get('header_info'), 'source', 'PDA-H5')) == 'PDA-H5' ? $this->numberUtil->formatPhoneNo($item['driver_phone'], 3, 4) : $item['driver_phone']),
                'truck_no'                => empty($item['truck_no']) ? '--' : $item['truck_no'],
                'history_id'              => $item['history_id'],
                'remark'                  => empty($item['remark']) ? '--' : $item['remark'],
                'order_channel_name'      => $channelName,
                'third_order_id'          => $item['third_order_id'],
                'province_name'           => $mirror['province_name'],
                'city_name'               => $mirror['city_name'],
                'gun_name'                => $mirror['gun_name'],
                'creator'                 => $item['creator'],
                'updator'                 => $item['updator'],
                'create_time'             => $item['create_time'],
                'update_time'             => $item['update_time'],
                'pay_time'                => $item['pay_time'],
                'third_actual_fee'        => $item['third_actual_fee'],
                'is_special_refund_pcode' => in_array($item['supplier_code'], explode(',', env('PCODE_OA_CHECK_BEFORE_REFUND'))) ? 1 : 0,
                'unit' => CommonDefine::getOilUnit($mirror['goods']),
                'third_actual_price' => $item['third_actual_price'],
                'original_order_id' => empty($mirror['original_order_id']) ? '--' : (string)$mirror['original_order_id'],
                'approve_remark' => empty($mirror['approve_remark']) ? '--' : $mirror['approve_remark'],
                'third_coupon_flag' => array_get($mirror, "third_coupon_flag", ""),
                'g7_coupon_flag' => array_get($mirror, "g7_coupon_flag", ""),
                'g7_coupon_status' => isset($mirror['coupons']) ? array_get($mirror['coupons'], 'status', "") : "",
                'g7_coupon_status_val' => isset($mirror['coupons']) ? CouponDefine::getMap(array_get($mirror['coupons'], 'status', "")) : "",
                'supplier_code' => $item['supplier_code'],
                'gun_money' => $mac_amount,
                'ndrc_price' => array_get($mirror, "ndrc_price", ''),
                'mac_price' => array_get($mirror, "mac_price", ''),
                'order_sale_type' => $item['order_sale_type'] ?? '',
                'order_sale_type_name' => $item['order_sale_type_name'] ?? '',
                'is_system_org' => $orgInfo[$item['org_code']]->is_system_orgcode,
            ];
            if (in_array($item['supplier_code'], CommonDefine::showOriginalSupplierMoney())) {
                $tmp['supplier_money'] = $mirror['original_supplier_money'];
            }
            if ($item['order_status'] == OrderModel::REFUND) {
                $tmp['plummet_rebate_money'] = "0.00";
                $tmp['later_rebate_money'] = "0.00";
                $tmp['charge_rebate_money'] = "0.00";

                $tmp['fanli_jifen'] = "0.00";
                $tmp['fanli_money'] = "0.00";

                $tmp['g7_coupon_status_val'] = "";
            } else {
                // 上游
                $tmp['plummet_rebate_money'] = $viceCardInfos[$item['order_id']]['plummet_rebate_money'] ?? '--';
                $tmp['later_rebate_money'] = $viceCardInfos[$item['order_id']]['later_rebate_money'] ?? '--';
                $tmp['charge_rebate_money'] = $viceCardInfos[$item['order_id']]['mark_rebate'] ?? '--';

                // 下游
                $tmp['fanli_money'] = $viceCardInfos[$item['order_id']]['fanli_money'] ?? '--';
                $tmp['fanli_jifen'] = $viceCardInfos[$item['order_id']]['fanli_jifen'] ?? '--';

                if (isset($viceCardInfos[$item['order_id']])) {
                    // 上游直降成本
                    $tmp['down_cost_money'] = $viceCardInfos[$item['order_id']]['down_cost_money'];
                    $tmp['down_cost_money_price'] = $viceCardInfos[$item['order_id']]['down_cost_money_price'];

                    // 上游最终成本
                    $tmp['cal_cost_money'] = $viceCardInfos[$item['order_id']]['cal_cost_money'];
                    $tmp['cal_cost_price'] = $viceCardInfos[$item['order_id']]['cal_cost_price'];

                    $tmp['direct_total'] = $viceCardInfos[$item['order_id']]['direct_total'];//下游直降总额 = 应付金额 - 结算金额
                    $tmp['back_cost_money'] = $viceCardInfos[$item['order_id']]['back_cost_money'];//下游成本总额 = 结算总额 - 下游返利总额
                    $tmp['back_cost_price'] = $viceCardInfos[$item['order_id']]['back_cost_price'];

                    $tmp['real_money'] = $viceCardInfos[$item['order_id']]['real_money'];//实际盈利 = 下游成本总额 - 上游成本总额
                } else {
                    // 上游直降成本
                    $tmp['down_cost_money'] = $mac_amount;
                    $tmp['down_cost_money_price'] = $item['real_oil_num'] > 0 ? bcdiv($tmp['down_cost_money'], $item['real_oil_num'], 6) : 0.00;
                    $tmp['down_cost_money_price'] = sprintf('%.2f', round($tmp['down_cost_money_price'], 2));

                    // 上游最终成本
                    $tmp['cal_cost_money'] = $mac_amount;
                    $tmp['cal_cost_price'] = $tmp['down_cost_money_price'];

                    $tmp['direct_total'] = sprintf('%.2f', round(bcsub($tmp['supplier_money'], $tmp['total_money'], 6),2));//下游直降总额 = 应付金额 - 结算金额

                    $tmp['back_cost_money'] = $tmp['oil_money'];//下游成本总额 = 结算总额 - 下游返利总额
                    $tmp['back_cost_price'] = $item['real_oil_num'] > 0 ? round($tmp['back_cost_money']/$item['real_oil_num'], 2) : '0.00';
                    $tmp['back_cost_price'] = sprintf('%.2f', $tmp['back_cost_price']);

                    $tmp['real_money'] = sprintf('%.2f',round(bcsub($tmp['back_cost_money'], $tmp['cal_cost_money'], 6),2));//实际盈利 = 下游成本总额 - 上游成本总额
                }
            }

            $needArray[] = $tmp;
        }
        $result['data'] = $needArray;
        return $result;
    }

    public static function formatUpstreamPriceMoney($data, $mirror, $key, $defaultKey)
    {
        $v = array_get($mirror, $key, 0);
        if (bccomp($v, 0, 6) == 0) {
            return $data[$defaultKey];
        }

        return sprintf('%.2f', round($v, 2));
    }

    /**
     * 获取订单详情
     *
     * @param $params
     * @return array
     */
    public function getOrderItem($params)
    {
        $queryParams = [];
        if (!empty($params['order_id'])) {
            $queryParams['order_id'] = $params['order_id'];
        }
        if (!empty($params['third_order_id']) and !empty($params['org_code'])) {
            $queryParams['third_order_id'] = $params['third_order_id'];
            $queryParams['org_code'] = $params['org_code'];
        }
        if (empty($queryParams)) {
            return [];
        }

        $result = $this->newOrderRepository->getOneOrderByParams($queryParams, ['*'], true);
        if (empty($result)) {
            return [];
        }

        $result['order_id'] = strval($result['order_id']); // JS最大支持15位长整形数字,超过长度限制后,截断补0
        $result['trade_no'] = strval($result['trade_no']);
        $result['order_status_name'] = OrderModel::$ORDER_STATUS[$result['order_status']];
        $result['payment_id'] = empty($result['payment_id']) ? '' : $result['payment_id'];
        //$mirror = json_decode($result['mirror'], true);
        if (!empty($result['mirror'])) {
            $mirror = json_decode($result['mirror'], true);
        } else {
            $mirror = $result['ext'];
            unset($mirror['order_id']);
            $mirror['update_time'] = $result['update_time'];
            $mirror['order_channel_name'] = CardTradeConf::getFrom($result['order_channel']);
        }
        $result['total_money'] = sprintf('%.2f', round(bcadd($result['oil_money'], $result['service_money'], 3), 2));
        $result['supplier_money'] = round(bcmul($mirror['supplier_price'], $result['real_oil_num'], 3), 2);
        if (in_array($result['supplier_code'], CommonDefine::showOriginalSupplierMoney())) {
            $result['supplier_money'] = $mirror['original_supplier_money'];
        }
        $result['driver_name'] = empty($result['driver_name']) ? '--' : $result['driver_name'];
        $result['_driver_phone'] = $result['driver_phone'];
        $result['driver_phone'] = empty($result['driver_phone']) ? '--' : (strtoupper(array_get(Request::get('header_info'), 'source', 'PDA-H5')) == 'PDA-H5' ? $this->numberUtil->formatPhoneNo($result['driver_phone']) : $result['driver_phone']);
        $result['truck_no'] = empty($result['truck_no']) ? '--' : $result['truck_no'];
        $result['card_no'] = empty($result['card_no']) ? '--' : (strtoupper(array_get(Request::get('header_info'), 'source', 'PDA-H5')) == 'PDA-H5' ? $this->numberUtil->formatPhoneNo($result['card_no'], 4, 6) : $result['card_no']);
        $result['card_no'] = Common::secretString($result['card_no'], 4, 6);
        $result['driver_phone'] = $result['driver_phone'] != '--' ? Common::secretString($result['driver_phone'], 3, 4) : $result['driver_phone'];
        $result['settlement_type'] = self::getOrderSettlementType($result['org_code']);
        unset($mirror['extends']);
        if (!empty($result['mirror'])) {
            unset($result['mirror']);
        } else {
            unset($result['ext']);
        }

        return array_merge($result, is_array($mirror) ? $mirror : []);
    }

    /**
     * 订单关闭
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function orderTimeOut($params)
    {
        $orderId = array_get($params, 'order_id');
        $orderItem = $this->newOrderRepository->getOneOrderByParams(
            ['order_id' => $orderId],
            ['order_id', 'order_status', 'card_level', 'driver_phone', 'card_no'],
            true
        );
        if (empty($orderItem)) {
            throw new Exception('订单超时修改状态失败:无效的订单号,请核实!', $this->errorCodeService->closeOrderError(40302));
        }
        if (in_array($orderItem['order_status'], [OrderModel::REFUNDING, OrderModel::REFUND, OrderModel::SUCCESS_PAY, OrderModel::FAIL_PAY])) {
            throw new Exception('订单超时修改状态失败:订单状态为' . OrderModel::$ORDER_STATUS[$orderItem['order_status']] . ',不允许修改,请核实!');
        }
        // 幂等
        if ($orderItem['order_status'] == OrderModel::CANCEL) {
            return true;
        }
        $result = $this->redisService->tryGetLock("pay-" . $orderId, 1, 5);
        if (!$result) {
            throw new \Exception('订单处理中，请稍后重试!', $this->errorCodeService->gotoPay(40303));
        }

        $canceled = false;

        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderInfo = $this->newOrderRepository->getOrderInfoByLock(['order_id' => $orderId], ['order_id', 'order_status'], true);
            if (empty($orderInfo)) {
                throw new Exception('订单超时修改状态失败:无效的订单号,请核实!', $this->errorCodeService->closeOrderError(40302));
            }
            if (in_array($orderInfo['order_status'], [OrderModel::REFUNDING, OrderModel::REFUND, OrderModel::SUCCESS_PAY, OrderModel::FAIL_PAY])) {
                throw new Exception('订单超时修改状态失败:订单状态为' . OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . ',不允许修改,请核实!');
            }
            // 幂等
            if ($orderInfo['order_status'] == OrderModel::CANCEL) {
                return true;
            }

            $coupon_flag = array_get($orderInfo, "ext.g7_coupon_flag", "");
            $upCoupon = -1;
            //todo 需要判断当前时间与创建时间是否间隔5分钟
            // 关单
            if ($orderInfo['order_status'] == OrderModel::WAIT_PAY) {
                // 查询支付单状态(异常报警)
                $payResult = $this->fossUser->queryPayResult(['order_no' => $orderId]);
                if (empty($payResult)) {
                    $updateArr = [
//                    'order_status' => OrderModel::FAIL_PAY,
'order_status' => OrderModel::CANCEL,
'remark'       => '订单超时',
'updator'      => '系统',
                    ];

                    $canceled = true;

                    $upCoupon = 1;
                } else {
                    // 支付成功
                    if ($payResult['pay_status'] == 1) {
                        $updateArr = [
                            'order_status' => OrderModel::SUCCESS_PAY,
                            'payment_id'   => $payResult['payment_id'],
                            'updator'      => '系统',
                        ];
                        $upCoupon = 2;
                    } else {
                        throw new Exception('订单异常,支付单状态异常,请联系管理员!', $this->errorCodeService->closeOrderError(40303));
                    }
                }
                // 关单
                $result = $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArr);
                //更改电子券状态
                if ($upCoupon > 0) {
                    if ($upCoupon == 1) {
                        $this->couponSrv->updateCoupon($coupon_flag, ['status' => CouponDefine::NO_USE]);
                        $this->newOrderExtRepository->updateOrderExtByOrderId($orderId, ['g7_coupon_flag' => NULL]);
                    }
                    if ($upCoupon == 2) {
                        //TODO 派发电子券
                        //$this->couponSrv->sendCoupon($orderInfo,false);
                    }
                }
                if (empty($result)) {
                    throw new Exception('关单失败,请稍后重试!', $this->errorCodeService->closeOrderError(40304));
                }
            }
            DB::connection('mysql_gas')->commit();
            $this->redisService->releaseLock("pay-" . $orderId, 1);
            if ($canceled && $orderItem['card_level'] == OrderModel::CAR_CARD && $orderItem['driver_phone'] && $orderItem['card_no']) {
                $this->truckSrv->dispatchTruckCardReturnJob($orderItem['driver_phone'], $orderItem['card_no']);
            }
        } catch (\Exception $e) {
            DB::connection('mysql_gas')->rollBack();
            $this->redisService->releaseLock("pay-" . $orderId, 1);
            throw new Exception($e->getMessage(), $this->errorCodeService->closeOrderError($e->getCode()));
        }
        return true;
    }

    /**
     * 查看小票
     *
     * @param $params
     * @return array
     */
    public function getTicket($params)
    {
        // 支付成功的订单会有小票
        $orderItem = $this->newOrderRepository->getOneOrderByParams([
            'order_id'     => $params['order_id'],
            'order_status' => [OrderModel::SUCCESS_PAY],
        ]);
        if (empty($orderItem)) {
            return [];
        }
        // 司机签名
        $orderExtend = $this->orderExtendRepository->getOneExtendInfoByParams([
            'order_id' => $params['order_id']
        ]);
        $mirror = json_decode($orderItem['mirror'], true);
        return [
            'order_id'         => $orderItem['order_id'],
            'station_name'     => $mirror['station_name'],
            'remark_name'      => $mirror['remark_name'],
            'truck_no'         => $orderItem['truck_no'],
            'driver_phone'     => $this->numberUtil->encryptNumber($orderItem['driver_phone'], 3, 4),
            'card_no'          => $this->numberUtil->encryptNumber($orderItem['card_no'], 4, 6),
            'goods'            => $mirror['goods'],
            'oil_price'        => sprintf('%.2f', $orderItem['oil_price']),
            'oil_num'          => sprintf('%.2f', $orderItem['oil_num']),
            'oil_money'        => sprintf('%.2f', $orderItem['oil_money']),
            'service_price'    => sprintf('%.2f', $orderItem['service_price']),
            'service_money'    => sprintf('%.2f', $orderItem['service_money']),
            'total_money'      => sprintf('%.2f', bcadd($orderItem['oil_money'], $orderItem['service_money'], 3)),
            'pay_time'         => $orderItem['pay_time'],
            'creator'          => $orderItem['creator'],
            'driver_signature' => empty($orderExtend) ? '' : $orderExtend['driver_signature']
        ];
    }

    /**
     * 订单列表查询参数汇总
     *
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getOrderSelectParams($params, $limit = 10)
    {
        $selectParams = [];

        // 渠道ID,站点编码
        $stationCode = [];
        if (!empty($params['channel_id']) && !empty($params['station_code'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
            $stationCode = array_intersect($channelStationCode, [$params['station_code']]);
            if (empty($stationCode)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
        } elseif (!empty($params['channel_id'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
            $stationCode = $channelStationCode;
        } elseif (!empty($params['station_code'])) {
            $stationCode = [$params['station_code']];
        }
        // 站点名称,站点编码
        if (!empty($params['station_id']) && !empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }

            $station_id_array = explode(',', $params['station_id']);
            $arrayIntersectStation = array_intersect($station_id_array, $stationIds);
            if (empty($arrayIntersectStation)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
            $selectParams['station_id'] = $arrayIntersectStation;
        } elseif (!empty($params['station_id'])) {
            $selectParams['station_id'] = explode(',', $params['station_id']);
        } elseif (!empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
            $selectParams['station_id'] = $stationIds;
        }
        // 订单号
        if (!empty($params['order_id'])) {
            $selectParams['order_id'] = $params['order_id'];
        }
        // 三方订单号
        if (!empty($params['third_order_id'])) {
            $selectParams['third_order_id'] = $params['third_order_id'];
        }
        // 订单状态
        if (!empty($params['order_status'])) {
            $selectParams['order_status'] = explode(',', $params['order_status']);
        }
        // 司机姓名
        if (!empty($params['driver_name'])) {
            $selectParams['like_driver_name'] = $params['driver_name'];
        }
        // 司机手机号
        if (!empty($params['driver_phone'])) {
            if (is_array($params['driver_phone'])) {
                $selectParams['driver_phone'] = $params['driver_phone'];
            } else {
                $selectParams['left_driver_phone'] = $params['driver_phone'];
            }
        }
        // 司机车牌号
        if (!empty($params['truck_no'])) {
            $selectParams['like_truck_no'] = $params['truck_no'];
        }
        // 账号
        if (!empty($params['card_no'])) {
            $selectParams['card_no'] = $params['card_no'];
        }
        // 站点供应商
        if (!empty($params['supplier_code'])) {
            $selectParams['supplier_code'] = $params['supplier_code'];
        }
        // 司机机构(查看子机构)
        if (!empty($params['org_code']) && !empty($params['need_relevant_org_order'])) {
            $selectParams['left_org_code'] = $params['org_code'];
        } else if (!empty($params['org_code'])) {
            $selectParams['org_code'] = $params['org_code'];
        }
        // 商品(name、type、level)
        if (!empty($params['oil_type'])) {
            $selectParams['oil_type'] = $params['oil_type'];
        }
        if (!empty($params['oil_name'])) {
            $selectParams['oil_name'] = $params['oil_name'];
        }
        if (!empty($params['oil_level'])) {
            $selectParams['oil_level'] = $params['oil_level'];
        }
        // 所属省市
        if (!empty($params['province_code'])) {
            $selectParams['province_code'] = $params['province_code'];
        }
        if (!empty($params['city_code'])) {
            $selectParams['city_code'] = $params['city_code'];
        }
        // 创建时间
        if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
            // 不能超过180天
            $selectParams['ge_create_time'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
            $selectParams['le_create_time'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }
        // 支付时间
        if (!empty($params['ge_pay_time']) && !empty($params['le_pay_time'])) {
            $selectParams['ge_pay_time'] = date('Y-m-d H:i:s', strtotime($params['ge_pay_time']));
            $selectParams['le_pay_time'] = date('Y-m-d H:i:s', strtotime($params['le_pay_time']));
        }
        // 支付单ID
        if (!empty($params['history_id'])) {
            $selectParams['history_id'] = $params['history_id'];
        }
        // 下单标识
        if (!empty($params['order_flag'])) {
            $selectParams['order_flag'] = $params['order_flag'];
        }
        //订单销售类型
        if (isset($params['order_sale_type']) && !empty($params['order_sale_type'])) {
            $selectParams['order_sale_type'] = $params['order_sale_type'];
        }

        if (isset($params['order_channel_cooperate']) && !empty($params['order_channel_cooperate'])) {
            $selectParams['order_channel'] = $params['order_channel_cooperate'];
        }

        // G7券标识
        if (isset($params['g7_coupon_flag']) && !empty($params['g7_coupon_flag'])) {
            $extendParams['like_g7_coupon_flag'] = $params['g7_coupon_flag'];
        }

        // 三方券标识
        if (isset($params['third_coupon_flag']) && !empty($params['third_coupon_flag'])) {
            $extendParams['like_third_coupon_flag'] = $params['third_coupon_flag'];
        }
        if (isset($extendParams) && !empty($extendParams)) {
            $orderIds = $this->orderExtendRepository->getPluckExtendByParams($extendParams, 'order_id');
            if (empty($orderIds)) {
                return ['select' => true, 'hit' => false, 'selectParams' => []];
            }
            $selectParams['order_id'] = $orderIds;
        }

        if (empty($selectParams)) {
            return ['select' => false, 'hit' => false, 'selectParams' => []];
        }

        return ['select' => true, 'hit' => true, 'selectParams' => $selectParams];
    }

    /**
     * 获取交易流失日期最大范围
     * @return int
     */
    public function getMaxDay()
    {
        $maxDay = 90; //天

        try{
            //请求foss配置
            $data = $this->foss->getStationTimeLimit([]);
            if($data['time_limit'])
            {
                $maxDay = $data['time_limit'];
            }
        }catch (\Exception $e){
            Common::log('info','getMaxDay获取交易流失日期最大范围值失败：'.$e->getMessage(),[]);
        }

        return $maxDay;
    }

    /**
     * 校验站、价并获取信息
     *
     * @param $stationId
     * @param $oilType
     * @param $oilName
     * @param $oilLevel
     * @param $payPrice
     * @param string $orgCode
     * @param string $gunId
     * @param string $oilTime 加油时间
     * @return array
     * @throws Exception
     * $checkCommon 由于卡车宝贝给司机优化，因此不能校验司机的特殊单价，需要校验通用客户单价
     */
    public function checkStationAndGetArray($stationId, $oilType, $oilName, $oilLevel, $payPrice, $orgCode = '', $gunId = '', $oilTime = '', $checkCommon = false, $limitOrgCode = '')
    {
        // 获取站点的基本信息
        $select = [
            'id', 'station_name', 'pcode', 'isstop', 'card_classify', 'provice_code', 'city_code', 'station_code',
            'remark_name', 'address', 'rebate_grade', 'app_station_id', 'lng', 'lat', 'trade_type'
        ];
        // 站信息
        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $stationId], $select);
        if ($stationInfo['card_classify'] != 2) {
            throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已下线!', $this->errorCodeService->createOrderError(40322));
        }
        if ($stationInfo['isstop'] == 1) {
            throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已停用!', $this->errorCodeService->createOrderError(40323));
        }
        if (in_array($stationInfo['pcode'], CommonDefine::needAssign())) {
            $stationExtData = $this->stationRepository->getOneStationExtByParams(
                ['station_id' => $stationInfo['id']],
                ['master_card_no', 'supplementary_card_no', 'card_user_id']
            );
            if (!$stationExtData or empty($stationExtData->master_card_no) or
                empty($stationExtData->supplementary_card_no) or
                empty($stationExtData->card_user_id)) {
                throw new RuntimeException('该油站未做主副卡映射，请到油站信息管理维护。', '40336');
            }
            $stationInfo['master_card_no'] = $stationExtData->master_card_no;
            $stationInfo['supplementary_card_no'] = $stationExtData->supplementary_card_no;
            $stationInfo['card_user_id'] = $stationExtData->card_user_id;
        }

        // 站点加油信息
        $needParams = [
            'station_id' => $stationId,
        ];
        if (!empty($orgCode)) {
            $needParams['orgcode'] = $orgCode;
            if (!empty($limitOrgCode)) {
                $needParams['limit_orgcode'] = $limitOrgCode;
            } else {
                $limitOrgCode = $orgCode;
            }
            $this->checkInstitutionSpecificAndLowYieldStation($limitOrgCode, $stationId);
        }
        $stationPriceInfo = $this->fossStation->getOilListAndPrice($needParams);
        if (empty($stationPriceInfo)) {
            throw new RuntimeException('车队长禁止司机在此站加油,请联系车队长', $this->errorCodeService->createOrderError(40317));
        }

        $insertStationMirror = [];
        foreach ($stationPriceInfo as $item) {
            if (strcmp($item['oil_type'], $oilType)) {
                continue;
            }
            if (strcmp($item['oil_name'], $oilName)) {
                continue;
            }
            if (strcmp($item['oil_level'], $oilLevel)) {
                continue;
            }
            if ($item['can_use'] == false) {
                throw new RuntimeException('验价失败,站点指定油品不可用!', $this->errorCodeService->createOrderError(40318));
            }
            // 实时下单取当前油品进价、销价
            if (empty($oilTime)) {
                // 销价验证
                $settlementType = 1;
                if (!empty($orgCode)) {
                    $settlementType = self::getOrderSettlementType($orgCode);
                }
                //折扣模式不校验销价
                if ($settlementType == 1 && abs(bcsub($payPrice, $item['platform_price'], 2)) && !$checkCommon) {
                    throw new RuntimeException('验价失败,油品销价' . $payPrice . '和实际销价' . $item['platform_price'] . '不同,请核对!', $this->errorCodeService->createOrderError(40319));
                }
                // 进价验证
                if (empty($item['supplier_price'])) {
                    throw new RuntimeException('验价失败,价格异常,油品进价为0,请联系管理员!', $this->errorCodeService->createOrderError(40320));
                }
            } else {
                // 补录历史订单取历史油品进价、销价，只能对在生效的油品进行补录
                /**
                 * 价格校验
                 */
                $selectPriceArray = [
                    'spcode'     => $stationInfo['pcode'],
                    'station_id' => $stationId,
                    'oil_name'   => $oilName,
                    'oil_type'   => $oilType,
                    'oil_level'  => $oilLevel,
                    'time'       => $oilTime,
                ];

                $salePrice = $this->fossStation->getSalePrice($selectPriceArray);
                if (empty($salePrice)) {
                    throw new RuntimeException('补单选择的油站、油品不存在销价,请核对', $this->errorCodeService->editApproveError(40308));
                }
                if (empty($salePrice['supplier_price'])) {
                    throw new RuntimeException('站点应收单价为0,价格异常,无法补单,请核对', $this->errorCodeService->editApproveError(40310));
                }
                // 历史价格替换
                $item['supplier_price'] = $salePrice['supplier_price'];
                $item['platform_price'] = $payPrice; // 补单价格认可用户录入的价格
                $item['price_id'] = $salePrice['id'];
            }

            $gunTankInfo = $item['gun'];
            $insertStationMirror = [
                'oil_type_name'  => $item['oil_type_val'],
                'oil_name_name'  => $item['oil_name_val'],
                'oil_level_name' => $item['oil_level_val'],
                'goods'          => $item['oil_type_val'] . $item['oil_name_val'] . $item['oil_level_val'],
                'gun_id'         => empty($gunTankInfo[0]['gun_id']) ? '' : $gunTankInfo[0]['gun_id'],
                'gun_name'       => empty($gunTankInfo[0]['gun_name']) ? '' : $gunTankInfo[0]['gun_name'],
                'tank_id'        => empty($gunTankInfo[0]['tank_id']) ? '' : $gunTankInfo[0]['tank_id'],
                'tank_name'      => empty($gunTankInfo[0]['tank_name']) ? '' : $gunTankInfo[0]['tank_name'],
                'supplier_price' => $item['supplier_price'],
                'platform_price' => $payPrice ? $payPrice : $item['platform_price'],
                'mac_price'      => array_get($item, 'mac_price', 0),
                'ndrc_price'     => array_get($item, 'ndrc_price', null) ?? null,
                'price_id'       => $item['price_id'],
                //【满帮】站价信息以折扣模式推送G7WALLET-4858
                'discount_rate'  => $item['discount_rate'],
            ];
            if (!empty($gunTankInfo) && !empty($gunId)) {
                foreach ($gunTankInfo as $value) {
                    if (!strcmp($value['gun_id'], $gunId)) {
                        $insertStationMirror['gun_id'] = $value['gun_id'];
                        $insertStationMirror['gun_name'] = $value['gun_name'];
                        $insertStationMirror['tank_id'] = $value['tank_id'];
                        $insertStationMirror['tank_name'] = $value['tank_name'];
                        break 2;
                    }
                }
                throw new RuntimeException('验价失败,指定油枪无法使用指定油品加油!', $this->errorCodeService->createOrderError(40321));
            }
            break;
        }

        if (empty($insertStationMirror)) {
            throw new RuntimeException('验价失败,未查询到有效油品,请联系管理员或重新下单!', $this->errorCodeService->createOrderError(40324));
        }

        // 供应商
        $supplierInfo = $this->supplierRepositories->getSupplierByScodes([$stationInfo['pcode']], ['supplier_name', 'scode'], false)->keyBy('scode')->toArray();
        // 省市信息
        $provinceCityDict = (new CityModel())->whereIn('city_code', [
            array_get($stationInfo, 'provice_code', ''),
            array_get($stationInfo, 'city_code', ''),
        ])->get()->keyBy('city_code')->toArray();
//        // 挂牌价
//        $provinceCode = $this->provinceAndCityUtil->getMunicipalities($stationInfo['provice_code']);
//        $listPriceArray = array_get($this->fossStation->getTodayListPrice(['city_code' => $provinceCode]),$provinceCode, []);
        $listPrice = '';
//        foreach ($listPriceArray as $item) {
//            if (strcmp($insertStationMirror['oil_type_name'], $item['oil_type_value'])) {
//                continue;
//            }
//            if (strcmp($insertStationMirror['oil_name_name'], $item['oil_name_value'])) {
//                continue;
//            }
//            $listPrice = $item['oil_price'];
//        }
        $insertStationArray = [
            'station_code'          => $stationInfo['station_code'],
            'supplier_code'         => $stationInfo['pcode'],
            'province_code'         => $stationInfo['provice_code'],
            'city_code'             => $stationInfo['city_code'],
            'master_card_no'        => $stationInfo['master_card_no'] ?? '',
            'supplementary_card_no' => $stationInfo['supplementary_card_no'] ?? '',
            'card_user_id'          => $stationInfo['card_user_id'] ?? '',
        ];

        $insertStationMirror = array_merge($insertStationMirror, [
            'list_price'      => $listPrice,
            'station_name'    => $stationInfo['station_name'],
            'remark_name'     => $stationInfo['remark_name'],
            'station_address' => $stationInfo['address'],
            'rebate_grade'    => $stationInfo['rebate_grade'],
            'supplier_name'   => empty($supplierInfo[$stationInfo['pcode']]['supplier_name']) ? '' : $supplierInfo[$stationInfo['pcode']]['supplier_name'],
            'province_name'   => empty($provinceCityDict[$stationInfo['provice_code']]['city_name']) ? '' : $provinceCityDict[$stationInfo['provice_code']]['city_name'],
            'city_name'       => empty($provinceCityDict[$stationInfo['city_code']]['city_name']) ? '' : $provinceCityDict[$stationInfo['city_code']]['city_name'],
            'lng'             => array_get($stationInfo, "lng", ''),
            'lat'             => array_get($stationInfo, "lat", ''),
            'app_station_id'  => array_get($stationInfo, "app_station_id", ''),
            'trade_type'      => array_get($stationInfo, "trade_type", ''),
        ]);

        return [
            'insert_station_array'  => $insertStationArray,
            'insert_station_mirror' => $insertStationMirror
        ];
    }

    /**
     * 取消订单
     * @param $params
     * @param string $updator
     * @return bool
     * @throws RuntimeException
     * @throws Exception
     */
    public function cancelOrder(array $params, $updator = '系统')
    {
        $orderId = $params['order_id'];
        $result = $this->redisService->tryGetLock("pay-" . $orderId, 1, 5);
        if (!$result) {
            throw new \Exception('订单处理中，请稍后重试!', $this->errorCodeService->gotoPay(40303));
        }
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $orderInfo = $this->newOrderRepository->getOrderInfoByLock(['order_id' => $orderId]);
            if (empty($orderInfo)) {
                throw new RuntimeException('无效的订单号,请核对!', $this->errorCodeService->gotoRefund(40301));
            }
            // 三方订单无退款中状态  支持幂等
            if (!in_array($orderInfo['order_status'], [OrderModel::WAIT_PAY, OrderModel::CANCEL])) {
                throw new RuntimeException('取消订单失败,非待支付订单!', $this->errorCodeService->gotoRefund(40403));
            }
            // 修改订单状态
            $updateData = [
                'order_status' => OrderModel::CANCEL,
                'updator'      => $updator
            ];
            if (!empty($params['pay_reason'])) {
                $updateData['remark'] = $params['pay_reason'];
            }
            $result = $this->newOrderRepository->updateOrderByOrderId($orderId, $updateData);
            if (empty($result) && $orderInfo['order_status'] == OrderModel::WAIT_PAY) {
                throw new RuntimeException('取消订单失败,修改订单状态失败!', $this->errorCodeService->gotoRefund(40305));
            }
            $coupon_flag = array_get($orderInfo, "ext.g7_coupon_flag", "");
            $upCoupon = -1;
            if ($orderInfo['order_status'] == OrderModel::WAIT_PAY) {
                $payResult = $this->fossUser->queryPayResult(['order_no' => $orderId]);
                if (empty($payResult)) {
                    $updateArr = [
                        'order_status' => OrderModel::CANCEL,
                        'remark'       => '订单超时',
                        'updator'      => '系统',
                    ];
                    $canceled = true;
                    $upCoupon = 1;
                } else {
                    if ($payResult['pay_status'] == 1) {
                        $updateArr = [
                            'order_status' => OrderModel::SUCCESS_PAY,
                            'payment_id'   => $payResult['payment_id'],
                            'updator'      => '系统',
                        ];
                        $upCoupon = 2;
                    } else {
                        throw new Exception('订单异常,支付单状态异常,请联系管理员!', $this->errorCodeService->closeOrderError(40303));
                    }
                }
                // 关单
                $result = $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArr);
                //更改电子券状态
                if ($upCoupon == 1) {
                    $this->couponSrv->updateCoupon($coupon_flag, ['status' => CouponDefine::NO_USE]);
                    $this->newOrderExtRepository->updateOrderExtByOrderId($orderId, ['g7_coupon_flag' => NULL]);
                }
                if (empty($result)) {
                    throw new Exception('订单取消失败,请稍后重试!', $this->errorCodeService->closeOrderError(40304));
                }
            }
            DB::connection('mysql_gas')->commit();
            $this->redisService->releaseLock("pay-" . $orderId, 1);
        } catch (RuntimeException $e) {
            DB::connection('mysql_gas')->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        } finally {
            $this->redisService->releaseLock("pay-" . $orderId, 1);
        }
        if (isset($params['pushMsgToDoper']) and $params['pushMsgToDoper'] and
            !empty($orderInfo['ext']['order_token']) and
            $orderInfo['order_status'] != OrderModel::CANCEL) {

            $this->messageService->pushMsgToDoper($orderInfo['ext']['order_token'], [
                'code' => 500,
                'msg'  => empty($params['pay_reason']) ? '订单失败，请司机重新支付' : $params['pay_reason'],
                'data' => [
                    'order_id' => strval($orderInfo['order_id']),
                ],
            ]);
        }
        return true;
    }

    /**
     * 批量获取订单详情
     *
     * @param $params
     * @return array
     */
    public function getBatchOrderItem($params)
    {
        $whereParams = [];
        if (!empty($params['order_ids'])) {
            $whereParams['order_id'] = $params['order_ids'];
        }
        if (!empty($params['other_order_ids'])) {
            $whereParams['other_order_ids'] = $params['other_order_ids'];
        }
        if (!empty($params['org_code'])) {
            $whereParams['org_code'] = $params['org_code'];
        }
        if (!empty($params['order_status'])) {
            $whereParams['order_status'] = $params['order_status'];
        }
        if (!empty($params['ge_create_time'])) {
            $whereParams['ge_create_time'] = $params['ge_create_time'];
        }
        if (!empty($params['le_create_time'])) {
            $whereParams['le_create_time'] = $params['le_create_time'];
        }
        if (empty($whereParams)) {
            return [];
        }

        $orderInfo = $this->newOrderRepository->getBatchOrderByParams($whereParams);
        foreach ($orderInfo as &$item) {
            if (!empty($item['mirror'])) {
                $mirror = json_decode($item['mirror'], true);
                unset($item['mirror']);
            } else {
                $mirror = $item['ext'];
                unset($mirror['order_id']);
                $mirror['update_time'] = $item['update_time'];
                $mirror['order_channel_name'] = CardTradeConf::getFrom($item['order_channel']);
            }
            unset($item['ext']);
            $mirror['is_special_refund_pcode'] = in_array($item['supplier_code'], explode(',', env('PCODE_OA_CHECK_BEFORE_REFUND'))) ? 1 : 0;
            $item = array_merge($item, $mirror);
        }

        return $orderInfo;
    }

    /**
     * 通知OA下单
     *
     * @param $params
     * @param $extends
     * @return bool
     * @throws RuntimeException
     */
    protected function oaCreateOrder($params, $extends)
    {
        $mirror = json_decode(array_get($params, 'mirror', "{}"), true);
        if (empty($mirror)) {
            throw new RuntimeException('参数异常:请按照格式传参!', $this->errorCodeService->gotoPay(40301));
        }
        try {
            $oaCreateOrder['data'] = [
                'id'               => $params['order_id'],
                'truck_no'         => $params['truck_no'],
                'station_id'       => $params['station_id'],
                'station_name'     => $mirror['station_name'],
                'oil_num'          => $params['oil_num'],
                'price'            => $params['oil_price'],
                'money'            => $params['oil_money'],
                'pay_price'        => $params['oil_price'],
                'pay_money'        => $params['oil_money'],
                'xpcode_pay_price' => $mirror['supplier_price'], // 进价
                'xpcode_pay_money' => round(bcmul($mirror['supplier_price'], $params['real_oil_num'], 3), 2),
                'card_no'          => $params['card_no'],
                'orgcode'          => $params['org_code'],
                'oil_name_val'     => $mirror['oil_name_name'],
                'oil_type_val'     => $mirror['oil_type_name'],
                'oil_level_val'    => $mirror['oil_level_name'],
                'oil_name'         => $params['oil_name'],
                'oil_type'         => $params['oil_type'],
                'oil_level'        => $params['oil_level'],
                'extends'          => $extends,
                'oil_time'         => date('Y-m-d H:i:s')
            ];
            $oaCreateOrder['message_type'] = 'PAY_LOG';
            // 平台司机下单
            $result = $this->adapter->sendCreateOrderMsg($oaCreateOrder);
            return true;
        } catch (RuntimeException $e) {
            $updateArray = [
                'order_status' => OrderModel::FAIL_PAY,
                'remark'       => $e->getMessage(),
                'updator'      => Request::get('user_name')
            ];
            $this->newOrderRepository->updateOrderByOrderId($params['order_id'], $updateArray);
            // 飞书报警
            $this->feishu->sendExceptionToFeiShu($e);

            throw $e;
        }
    }

    /**
     * 发送验密通知到司机端小程序
     *
     * @param $cardNo
     * @param $orderId
     * @throws RuntimeException
     */
    protected function driverCheckPassword($cardNo, $orderId)
    {
        try {
            $this->messageService->driverCheckPassword($cardNo, $orderId);
        } catch (RuntimeException $e) {
//            $updateArray = [
//                'order_status' => OrderModel::FAIL_PAY,
//                'remark' => $e->getMessage(),
//                'updator' => Request::get('user_name') ?? '系统'
//            ];
//            $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
//            throw $e;
        }
    }

    /**
     * 司机微信卡包支付的消息推送
     *
     * @param $cardNo
     * @param $orderId
     * @throws RuntimeException
     */
    protected function cardDriverCheckPassword($cardNo, $orderId)
    {
        try {
            // 获取gas_card表的id
            $cardInfo = $this->cardRepository->getOneCardByParams(['card_no' => $cardNo]);
            if ($cardId = array_get($cardInfo, 'id', '')) {
                $this->messageService->cardDriverCheckPassword($cardId, $orderId);
            }
        } catch (RuntimeException $e) {
//            $updateArray = [
//                'order_status' => OrderModel::FAIL_PAY,
//                'remark' => $e->getMessage(),
//                'updator' => Request::get('user_name') ?? '系统'
//            ];
//            $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
//            throw $e;
        }
    }

    /**
     * 中转订单
     * @param array $params
     * @return array
     * @throws ParamInvalidException|Exception
     */
    public function proxyOrder($params = [])
    {
        try {
            //通用校验
            $stationId = array_get($params, "station_id", "");
            // 站信息proxy
            $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $stationId, 'isdel' => 0], "*");
            if (!$stationInfo) {
                throw new RuntimeException('验站失败，站点和商品不存在或已删除', $this->errorCodeService->createOrderError(40321));
            }
            if ($stationInfo['card_classify'] != 2) {
                throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已下线!', $this->errorCodeService->createOrderError(40322));
            }
            if ($stationInfo['isstop'] != 0) {
                throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已停用!', $this->errorCodeService->createOrderError(40323));
            }
            $oilPrice = array_get($params, "oil_price", 0);
            $oilMoney = $oilNum = $realOilNum = 0;
            $oilUnit = array_get($params, 'oil_unit', '');
            if (empty($oilUnit)) {
                $oilUnit = $stationInfo['station_oil_unit'];
            }

            //支持卡包的老吕运营商的支付按升加油
            if (!in_array(array_get($params, "order_channel", ''), [CardTradeConf::WECHAT_ON_LINE, CardTradeConf::WECHAT_TICKET])) {
                //三方站点强制更改油站加注方式
                if (in_array($stationInfo['pcode'], CommonDefine::forceOilUnitPcode())) {
                    $oilUnit = 2;
                }
            }

            if (!array_key_exists($oilUnit, OrderModel::$OIL_UNIT)) {
                throw new RuntimeException('下单失败,无效的商品单位,请参考:' . implode(',', OrderModel::$OIL_UNIT), $this->errorCodeService->createOrderError(40303));
            }

            //站点加注模式 1按金额 2按升
            if ($oilUnit == 2) {
                if (!isset($params['oil_num']) || empty($params['oil_num'])) {
                    throw new RuntimeException('请输入加油升数', $this->errorCodeService->createOrderError(40324));
                }
                $oilMoney = round(bcmul($params['oil_num'], $oilPrice, 3), 2);
                #$oilMoney = $oilPrice * $params['oil_num'];
                $realOilNum = $oilNum = $params['oil_num'];
                $originOilNum = $params['oil_num']; // 原始升数
                $originOilMoney = (isset($params['oil_money']) && $params['oil_money'] > 0) ? $params['oil_money'] : $oilMoney; // 原始金额
            } else {
                if (!isset($params['oil_money']) || empty($params['oil_money'])) {
                    throw new RuntimeException('请输入加油金额', $this->errorCodeService->createOrderError(40325));
                }
                $oilMoney = $params['oil_money'];
                $realOilNum = bcdiv($oilMoney, $oilPrice, 6);
                $oilNum = $this->numberUtil->formatNumber($realOilNum, 2);
                $originOilNum = (isset($params['oil_num']) && $params['oil_num'] > 0) ? $params['oil_num'] : $realOilNum; // 原始升数
                $originOilMoney = $params['oil_money']; // 原始金额
            }
            if (bccomp($oilNum, 0.00, 2) < 1) {
                throw new RuntimeException('下单失败，单次交易升数小于0.01升', '40335');
            }
            $driver_name = array_get($params, 'driver_name', '');
            $driver_phone = array_get($params, 'driver_phone', '');
            $truck_no = array_get($params, 'truck_no', '');
            $ocr_truck_no_id = array_get($params, 'ocr_truck_no_id', '');
            $operator = array_get($params, "operator", "");
            $operator_id = array_get($params, "operator_id", "");
            $channel = array_get($params, "order_channel", 202);

            $coupon_type = array_get($params, "coupon_type", "");

            if (bccomp(0, $oilMoney, 2) >= 0) {
                throw new RuntimeException('加油金额必须大于0', $this->errorCodeService->createOrderError(40425));
            }

            $orderFlag = array_get($params, "orderFlag", 'G7');

            $cardNo = $orderSn = $order_no = "";
            $tokenInfo = [];
            //oa下单标识
            $message_type = "ONLINE_PAY_ORDER";
            $extends = '';
            $pushOA = false;


            //兼容pda的加油员有可能绑定主动付款的站点,优先使用扫码的方式进行支付
            $TradeType = $_tradeType = $stationInfo['trade_type'];
            if (($channel == 5 || !empty(array_get($params, 'order_token', ""))) && in_array($stationInfo['trade_type'], [3, 4, 5])) {
                $TradeType = $_tradeType = 1;
            }
            //如果是强制主动付款模式下单的订单来源，需要是主动付款的方式
            if (in_array($orderFlag, CardTradeConf::$force_active_payment_order_flag)) {
                $_tradeType = 3;
            }
            $isCoupon = false;
            //todo 需要兼容车辆卡
            //1:扫一扫|2:感应卡|3:主动付款|4:小票机|5:支付+扫码
            switch ($_tradeType) {
                case 2:
                    throw new RuntimeException('油站加油方式设置有误', $this->errorCodeService->createOrderError(40326));
                    break;
                //3、4下单支付逻辑均一致，当支付方式为4时交易完需触发小票机打印小票
                case 3:
                case 4:
                case 5:
                    $orderSn = array_get($params, "order_sn", "");
                    $cardNo = array_get($params, "card_no", 0);
                    $orderNo = array_get($params, "order_no", "");

                    //G7WALLET-1504 下单检查升数
                    $this->checkOrderNum($stationInfo, $params, $oilNum);

                    //车主邦校验参数，下单时，没有卡号
                    if ((!in_array($stationInfo['pcode'], CommonDefine::getPayMentPcode())
                            and (
                                in_array($stationInfo['pcode'], CommonDefine::getSpecialPcode())
                                || in_array($stationInfo['pcode'], CommonDefine::getPcodeList())
                        ))
                        || in_array($stationInfo['pcode'], CommonDefine::getYjfAndZdpPcodes())
                    ) {
                        $pushOA = true;
                        // 惠运通默认给1号枪
                        if (in_array($stationInfo['pcode'], CommonDefine::getSpecialPcode()) && empty($params['gunNumber'])) {
                            $params['gunNumber'] = 1;
                        }
                        $rule = [
                            #'actucal_money' => 'required|numeric', //实付金额
                            'gunNumber' => 'required|numeric',
                            'priceGun'  => 'required|numeric', //油枪价格
                            'amountGun' => 'required|numeric', //输入金额
                            #'price' => 'required|numeric', //结算价
                        ];
                        $messages = [
                            #'actucal_money.numeric' => '请输入实付金额',
                            'gunNumber.required' => '请输入枪号',
                            'amountGun.required' => '请输入支付金额',
                            'priceGun.required'  => '油枪单价不能为空',
                            #'price.required' => '结算单价不能为空',
                        ];
                        $validator = Validator::make($params, $rule, $messages);
                        if ($validator->fails()) {
                            throw new ParamInvalidException($validator->getMessageBag()->first());
                        }
                    }

                    if (in_array($stationInfo['pcode'], CouponDefine::couponPcode())) {
                        if (empty($coupon_type)) {
                            throw new RuntimeException('请选择电子券', $this->errorCodeService->createOrderError(40339));
                        }
                        $isCoupon = true;
                    }

                    break;
                default:
                    $message_type = "PAY_LOG";
                    // 根据站点供应商来区分以哪个字段来判断是否重复下单
                    $orderSn = array_get($params, 'order_token', "");
                    //由于有1号卡联合站子级运营商
                    if (!in_array($stationInfo['pcode'], CardTradeConf::$ownner_station_pcode)) {
                        if (!isset($params['order_sn']) || empty($params['order_sn'])) {
                            throw new RuntimeException('唯一流水号不能为空', $this->errorCodeService->createOrderError(40329));
                        }
                        $orderSn = $params['order_sn'];
                        $cardNo = array_get($params, "card_no", 0);
                    } else {
                        if (empty($operator_id) || empty($operator)) {
                            throw new RuntimeException('下单人必传', $this->errorCodeService->createOrderError(40330));
                        }
                        $tokenInfo = $this->redisService->parseCreateOrderToken($orderSn);
                        $cardNo = array_get($tokenInfo, 'card_no', 0);
                        if (empty($driver_name)) {
                            $driver_name = array_get($tokenInfo, 'driver_name', "");
                        }
                        if (empty($driver_phone)) {
                            $driver_phone = array_get($tokenInfo, 'driver_phone', "");
                        }
                        if (empty($truck_no)) {
                            $truck_no = array_get($tokenInfo, 'truck_no', "");
                        }
                        if (empty($ocr_truck_no_id)) {
                            $ocr_truck_no_id = array_get($tokenInfo, 'ocr_truck_no_id', '');
                        }
                        $extends = array_get($tokenInfo, 'extends', '');
                        $orderNo = array_get($tokenInfo, 'order_no', '');
                    }
                    if (empty($cardNo)) {
                        throw new RuntimeException('请选择支付卡号', $this->errorCodeService->createOrderError(40328));
                    }
                    $channel = array_get($tokenInfo, 'qr_code_source', 201);
            }

            //处理油站是扫码付款，pda扫码后，把油站收银方式更改为主动付款后，order_sn为空的报错
            if (in_array($channel, [CardTradeConf::WECHAT_TICKET, CardTradeConf::H5_PAY_CODE, CardTradeConf::WMP_PAY_CODE, CardTradeConf::THIRD_DOWN_PAY_CODE]) && empty($orderSn)) {
                $orderSn = 'online-' . time();
            }
            if (empty($orderSn)) {
                throw new RuntimeException('唯一流水号不能为空', $this->errorCodeService->createOrderError(40327));
            }

            $servicePrice = array_get($params, 'service_price', 0);
            $serviceMoney = array_get($params, 'service_money', 0);

            $oilType = array_get($params, 'oil_type', null);
            $oilName = array_get($params, 'oil_name', null);
            $oilLevel = array_get($params, 'oil_level', null);

            $is_sync = array_get($params, "is_sync", 2);
            $autoPay = array_get($params, "auto_pay", 2);

            $third_order_id = array_get($params, 'third_order_id', "");


            $checkCardAndGetArray = $checkStationAndGetArray = [];
            $oilTime = array_get($params, 'oil_time', "");

            if (!empty($cardNo)) {
                //校验卡信息
                $checkCardAndGetArray = $this->cardService->checkCardAndGetArray($cardNo, $servicePrice, $oilMoney, $serviceMoney);
            }

            $orgCode = array_get($checkCardAndGetArray, 'insert_card_array.org_code', "");
            $cardOrgCode = $orgCode;

            //print_r($checkCardAndGetArray);exit;
            // 特殊定价 //校验站价信息
            $checkCommon = false;
            //卡车宝贝下单时，需要校验通用客户进价
            if (in_array($orderFlag, CardTradeConf::$check_common_sale_price_order_flag) || !empty($orderNo)) {
                $checkCommon = true;
            }
            $limitOrgCode = $orgCode;
            $orgCode = $this->newOrderRepository::getSpecialPriceOrg($stationId, $orgCode, substr($orgCode, 0, 6));
            $checkStationAndGetArray = $this->checkStationAndGetArray($stationId, $oilType, $oilName, $oilLevel, $oilPrice, $orgCode, '', $oilTime, $checkCommon, $limitOrgCode);

            //处理针对圆通添加特殊定价后，由于pda缓存，没有把 amountGun枪金额 传到后端
            if ($message_type == "PAY_LOG") {
                //枪价
                $_macPrice = $checkStationAndGetArray['insert_station_mirror']['mac_price'];
                //销价
                $_platPrice = $checkStationAndGetArray['insert_station_mirror']['platform_price'];

                if (bccomp($_macPrice, 0, 2) <= 0) {
                    $_macPrice = $_platPrice;
                }

                //修复供应商：浙江舟山道科能源有限公司，销价为0的问题
                if (bccomp($_macPrice, 0, 2) <= 0) {
                    throw new RuntimeException('销价为0，请通知G7运营人员调整价格!', $this->errorCodeService->createOrderError(40401));
                }

                //计算方式
                //司机实付 = 枪金额/枪单价 * 销价
                if (isset($params['amountGun']) && $params['amountGun'] > 0) {
                    $_tmpNum = $this->numberUtil->formatNumber(bcdiv($params['amountGun'], $_macPrice, 2), 2);
                    //todo 如果前端传枪金额，后端做计算对比，是否会出现小数保留位数不一致的问题
                    $diffNum = abs(bcsub($_tmpNum, $oilNum, 2));
                    if (bccomp($diffNum, 0.03, 2) > 0) {
                        throw new RuntimeException('油机价有变更，请通知G7运营人员调整价格!', $this->errorCodeService->createOrderError(40401));
                    }
                }

                //站点加注模式 1按金额 2按升
                if ($oilUnit == 2) {
                    $oilNum = $realOilNum = $params['oil_num'];
                    $params['amountGun'] = round(bcmul($realOilNum, $_macPrice, 3), 2);
                    $oilMoney = round(bcmul($realOilNum, $_platPrice, 3), 2);
                } else {
                    $params['amountGun'] = (isset($params['amountGun']) && $params['amountGun'] > 0) ? $params['amountGun'] : $params['oil_money'];
                    $realOilNum = bcdiv($params['amountGun'], $_macPrice, 6);
                    $oilNum = $this->numberUtil->formatNumber($realOilNum, 2);
                    $oilMoney = round(bcmul($realOilNum, $_platPrice, 3), 2);
                }
            }

            //卡车宝贝司机实付金额
            $third_money = array_get($params, "third_pay_money", 0);
            $originThirdPayMoney = $third_money;
            if (empty($third_money)) {
                $third_money = $oilMoney;
            }

            //卡车司机实际单价
            $third_price = array_get($params, "third_price", 0);
            if (empty($third_price)) {
                $third_price = $oilPrice;
            }

            $whereParams = [
                'third_order_id' => $third_order_id,
                'org_code'       => $orgCode,
                'order_status'   => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
                'order_sn'       => $orderSn,
            ];
            $cacheExpire = 10;
            //自有站，校验重复下单
            if (in_array($stationInfo['pcode'], CardTradeConf::$ownner_station_pcode) && in_array($stationInfo['trade_type'], [1, 5])) {
                $whereParams = [
                    'station_id'   => $stationId,
                    'oil_type'     => $oilType,
                    'oil_name'     => $oilName,
                    'oil_level'    => $oilLevel,
                    'card_no'      => $cardNo,
                    'driver_phone' => $driver_phone,
                    'oil_price'    => $oilPrice,
                    'oil_num'      => $oilNum,
                    'order_status' => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
                ];
                $cacheExpire = 1 * 60;
            }
            $redisCache = json_encode($whereParams);
            $existsOrder = $this->redisService->tryGetLock("order-" . md5($redisCache), $redisCache, $cacheExpire);
            if (!$existsOrder) {
                throw new RuntimeException('订单已存在,请勿重复下单!', $this->errorCodeService->createOrderError(41301));
            }

            $driverSource = isset($tokenInfo['is_access_platform_driver']) ? (empty($tokenInfo['is_access_platform_driver']) ? OrderModel::OWN_DRIVER : OrderModel::THIRD_DRIVER) : OrderModel::OWN_DRIVER;

            if ($driverSource == 2) {
                $channel = CardTradeConf::THIRD_DOWN_PAY_CODE;
            }

            if (empty($driver_name)) {
                $driver_name = array_get($checkCardAndGetArray, 'extends_driver_info.driver_name', "");
            }
            if (empty($driver_phone)) {
                $driver_phone = array_get($checkCardAndGetArray, 'extends_driver_info.driver_phone', "");
            }
            if (empty($truck_no)) {
                $truck_no = array_get($checkCardAndGetArray, 'extends_driver_info.truck_no', "");
            }

        } catch (RuntimeException $e) {
            $msg = $e->getMessage();
            $code = $e->getCode();
            throw new RuntimeException($msg, $code);
        }

        $this->checkGray($driver_phone);

        $orderToken = array_get($params, 'order_token', "");
        $orderId = "";
        // 获取tradeMode
        $tradeMode = $this->getTradeMode($TradeType, $stationInfo['pcode'], $driverSource);

        if (!in_array($tradeMode, array_keys(CardTradeConf::$trade_mode))) {
            throw new RuntimeException('未知的交易模式', '4040011');
        }
        // 一键付需检测站点数据中的供应商系统站点ID值是否有效
        if (in_array($tradeMode, CardTradeConf::$push2OA)) {

            if (!isset($stationInfo['app_station_id']) or empty($stationInfo['app_station_id'])) {

                throw new RuntimeException(CommonError::$codeMsg[CommonError::APP_STATION_ID_INVALID], CommonError::APP_STATION_ID_INVALID);
            }
        }
        // 主动付款需验证升数及金额 G7WALLET-1966
        if (in_array($tradeMode, CardTradeConf::$trade_mode_zdp)) {
            $filterParams = [
                'mac_price'        => $checkStationAndGetArray['insert_station_mirror']['mac_price'] ?? 0.00,
                'platform_price'   => $checkStationAndGetArray['insert_station_mirror']['platform_price'],
                'amountGun'        => array_get($params, "amountGun", 0),
                'priceGun'         => array_get($params, "priceGun", 0),
                'origin_oil_money' => $originOilMoney,
                'origin_oil_num'   => $originOilNum,
                'oil_price'        => $params['oil_price'],
                'oil_unit'         => $oilUnit,
                //                'card_no'          => $args['card_no'] ?? '',
                //G7WALLET-4858 满帮对接支持站价信息以折扣模式
                'orgcode'         =>  $cardOrgCode,
                'discount_rate'   => $checkStationAndGetArray['insert_station_mirror']['discount_rate'] ?? null,
                'trade_model'     => $tradeMode,
            ];
            $resCheckNumAndPrice = $this->validateZdpOilNumAndPrice($filterParams);
            // 重新计算后赋值
            if (!empty($resCheckNumAndPrice)) {
                $realOilNum = $resCheckNumAndPrice['real_oil_num'];
                $oilNum = $resCheckNumAndPrice['oil_num'];
                $oilMoney = $resCheckNumAndPrice['oil_money'];
                $third_money = empty($originThirdPayMoney) ? $resCheckNumAndPrice['oil_money'] : $originThirdPayMoney;
            }
        }

        //如果是折扣结算重新计算结算金额
        if(in_array($tradeMode, CardTradeConf::$trade_mode_smp)) {
            $settlementType = self::getOrderSettlementType($cardOrgCode);
            if($settlementType == 2) {
                //重新计算结算金额
                $amountGun = array_get($params, "amountGun", 0);
                $discountRate = $checkStationAndGetArray['insert_station_mirror']['discount_rate'] ?? null;
                $oilMoney = round(bcmul($amountGun, bcdiv($discountRate, 100, 4), 3), 2);
            }

        }

        $checkPwd = array_get($checkCardAndGetArray, 'insert_card_mirror.is_check_password', "");
        $kcCallBack = 0;

        $coupon_flag = $third_coupon_flag = NULL;

        try {
            if( !empty($cardOrgCode) && !empty($cardNo) ) {
                //G7WALLET-5119
                $limitCondition = $checkPayLimit = [];
                $limitCondition['oil_time'] = !empty($oilTime) ? $oilTime : date("Y-m-d H:i:s", time());
                $limitCondition['orgcode'] = $cardOrgCode;
                $limitCondition['card_no'] = $cardNo;
                $limitCondition['money'] = $oilMoney;
                $limitCondition['drivertel'] = $driver_phone;
                $limitCondition['supplier_code'] = $stationInfo['pcode'];
                $checkPayLimit = $this->foss->checkPayLimit($limitCondition);
                if (!isset($checkPayLimit['pay_limit'])) {
                    throw new RuntimeException('下单失败，超过日限次数或单次限额！！！', $this->errorCodeService->createOrderError(42504));
                }
                if ($checkPayLimit['pay_limit'] != 1) {
                    throw new RuntimeException('下单失败，超过日限次数或单次限额！！！', $this->errorCodeService->createOrderError(42505));
                }
            }
        }catch (RuntimeException $exception){
            throw new RuntimeException($exception->getMessage(), $exception->getCode());
        }

        //针对卡车宝贝,不用下单
        if (!empty($orderNo)) {
            $orderId = $orderNo;
            $info = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $orderNo]);
            $orderFlag = $info['order_flag'];
            if (!$info) {
                throw new RuntimeException('该笔订单不存在：' . $orderNo, $this->errorCodeService->createOrderError(40501));
            }
            if ($info['order_status'] != 1) {
                throw new RuntimeException('订单状态已变更：' . $orderNo, $this->errorCodeService->createOrderError(40501));
            }

            $msg = '';
            /*//todo 需要产品确定对比那些参数
            if ($oilName != $info['oil_name'] and empty($msg)) {
                $msg = '油品不一致';
            }*/
            if ($params['station_id'] != $info['station_id'] and empty($msg)) {
                $msg = "站点不一致！";
            }
            //todo 需要产品确定对比那些参数
            $_pOil = $oilName . $oilLevel . $oilType;
            $_hOil = $info['oil_name'] . array_get($info, "oil_level", null) . array_get($info, "oil_type", null);
            if ($_pOil != $_hOil and empty($msg)) {
                $msg = '商品不一致！';
            }
            if (bccomp($oilPrice, $info['oil_price'], 2) != 0 and empty($msg)) {
                $msg = "价格不一致！";
            }
            if (bccomp(abs(bcsub($oilMoney, $info['oil_money'], 2)), 0.03, 2) > 0 and empty($msg)) {
                $msg = '司机、加油员输入金额不一致！';
            }
            $_key = $orderNo . "-errorNum";
            $errNum = $this->redisService->getValByKey($_key);
            if (!empty($msg)) {
                $code = '510031';
                if ($errNum >= 1) {
                    $code = '510032';
                }
                $this->redisService->addKeyNum($_key);
                //通知司机错误消息
                if ($info['order_flag'] == CardTradeConf::OPEN_API_FLAG) {

                    $this->messageService::createKafkaMessage(json_encode([
                        'order_no' => (string)$info['order_id'],
                        'status'   => 2,
                        'reason'   => $msg,
                        'orgcode'  => $info['ext']['open_api_org_code'],
                    ]));
                    throw new RuntimeException($msg, $this->errorCodeService->createOrderError(40501));
                }
                //$this->messageService->driverPayFail($info['card_no'], $orderNo, $msg, $code,$info['driver_phone']);
                $this->messageService->pushMsg2H5([
                    'id'      => $orderId,
                    'card_no' => $info['card_no'],
                    'mobile'  => $info['driver_phone'],
                    'code'    => $code,
                    'msg'     => $msg
                ], 3);
                throw new RuntimeException($msg, $this->errorCodeService->createOrderError(40501));
            }
            //卡车扫码的方式不需要验密直接支付
            if (in_array($info['order_flag'], CardTradeConf::$force_active_payment_order_flag)) {
                $checkPwd = 0;
                $kcCallBack = 1;
            }
            $mirror = $info['ext'];
            $makeOrder = $info;
            $cardNo = $info['card_no'];
            $this->redisService->delValByKey($_key);

            $this->newOrderExtRepository->updateOrderExtByOrderId($orderId, ['operator_id' => $operator_id, 'update_time' => date("Y-m-d H:i:s", time())]);
        } else {
            DB::connection('mysql_gas')->beginTransaction();
            try {
                //由于gas_history表中的stream_no是唯一索引，因此下单时，先检查是否已使用
                /*$info = $this->newOrderRepository->getOrderInfoByLock(['order_sn' => $orderSn],['*'],false);
                if ($info) {
                    throw new RuntimeException('该订单号已使用:' . $orderSn, $this->errorCodeService->createOrderError(40401));
                }*/

                $info = $this->redisService->tryGetLock("order-sn" . $orderSn, $orderSn, 5);
                if (!$info) {
                    throw new RuntimeException('该订单号已使用:' . $orderSn, $this->errorCodeService->createOrderError(41301));
                }

                $orderId = $this->redisService->makeOrderId();

                //ocr扫车牌校验开始
                $ocr_truck_no_url = '';
                if($orderFlag == 'G7' && $ocr_truck_no_id){
                    $ocrInfo = (new FossUser())->getOcrInfo(['id'=>$ocr_truck_no_id]);
                    if(!$ocrInfo){
                        throw new RuntimeException('请拍车牌号ID无效' . $orderSn, $this->errorCodeService->createOrderError(40501));
                    }
                    $ocr_truck_no_url = $ocrInfo['oss_url'];
                }
                //ocr扫车牌校验结束

                //获取可用的券
                if ($isCoupon && !empty($coupon_type)) {
                    $couponModel = $this->couponSrv->getCanUseCoupon($stationInfo['pcode'], $coupon_type);
                    $coupon_flag = $couponModel->voucher;
                    $third_coupon_flag = $couponModel->tripartite_voucher;
                    if (empty($coupon_flag)) {
                        throw new RuntimeException('没有可使用的券:' . $orderSn, $this->errorCodeService->createOrderError(40411));
                    }
                }

                $order = [
                    'order_id'           => $orderId,
                    'order_sn'           => (strlen($orderSn) > 50) ? md5($orderSn) : $orderSn,
                    'order_status'       => OrderModel::WAIT_PAY,
                    'order_type'         => $message_type == "PAY_LOG" ? 2 : 1,
                    'order_channel'      => $channel,
                    'station_id'         => $stationId,
                    'card_no'            => $cardNo,
                    'oil_type'           => $oilType,
                    'oil_name'           => $oilName,
                    'oil_level'          => $oilLevel,
                    'oil_unit'           => $oilUnit,
                    'oil_num'            => $oilNum,
                    'oil_price'          => $oilPrice,
                    'oil_money'          => $oilMoney,
                    'third_actual_fee'   => $third_money,
                    'third_actual_price' => $third_price,
                    'real_oil_num'       => $realOilNum,
                    'service_price'      => $servicePrice,
                    'service_money'      => $serviceMoney,
                    'mirror'             => '',
                    'third_order_id'     => $third_order_id,
                    'creator'            => $operator,
                    'driver_source'      => $driverSource,
                    'driver_name'        => $driver_name,
                    'driver_phone'       => $driver_phone,
                    'truck_no'           => $truck_no,
                    'updator'            => $operator,
                    'station_code'       => '',
                    'province_code'      => '',
                    'city_code'          => '',
                    'supplier_code'      => '',
                    'org_code'           => '',
                    'card_type'          => 0,
                    'card_level'         => 0,
                    'trade_no'           => $this->redisService->makeSerialNum(),
                    'trade_mode'         => $tradeMode,//todo待写入
                    'order_flag'         => $orderFlag
                ];
                $oilTime = array_get($params, "oil_time", null);
                if (empty($oilTime)) {
                    $oilTime = date("Y-m-d H:i:s", time());
                }
                //todo 生成订单扩展表(gas_order_new_extend)
                $mirror = [
                    'order_id'              => $orderId,
                    'is_sync'               => array_get($params, "auto_pay", 2),
                    'org_name'              => '',
                    'oil_time'              => $oilTime,
                    'account_type_name'     => '',
                    'account_no'            => '',
                    'account_name'          => '',
                    'station_name'          => '',
                    'station_address'       => '',
                    'rebate_grade'          => '',
                    'province_name'         => '',
                    'city_name'             => '',
                    'oil_type_name'         => '',
                    'oil_name_name'         => '',
                    'oil_level_name'        => '',
                    'mac_price'             => '',
                    'supplier_price'        => '',
                    'platform_price'        => '',
                    'order_channel_name'    => CardTradeConf::getFrom($channel),
                    'operator_id'           => $operator_id,
                    'order_token'           => $orderToken,
                    'mac_amount'            => array_get($params, "amountGun", 0),
                    'app_station_id'        => array_get($stationInfo, "app_station_id", ""),
                    'trade_type'            => array_get($stationInfo, "trade_type", 1),
                    'lng'                   => array_get($stationInfo, "lng", ''),
                    'lat'                   => array_get($stationInfo, "lat", ''),
                    'update_time'           => date("Y-m-d H:i:s", time()),
                    'open_api_org_code'     => $params['open_api_org_code'] ?? '',
                    'g7_coupon_flag'        => $coupon_flag,
                    'third_coupon_flag'     => $third_coupon_flag,
                    'gun_number'            => array_get($params, 'gunNumber', ''),
                    'original_order_id'     => $orderId,
                    'master_card_no'        => $checkStationAndGetArray['insert_station_array']['master_card_no'],
                    'supplementary_card_no' => $checkStationAndGetArray['insert_station_array']['supplementary_card_no'],
                    'card_user_id'          => $checkStationAndGetArray['insert_station_array']['card_user_id'],
                    'ocr_truck_no_url'  => $ocr_truck_no_url ?? '', //ocr车牌加油图片地址
                ];
                // 移除卡分配相关信息，避免下方代码合并数据造成订单入库失败
                unset(
                    $checkStationAndGetArray['insert_station_array']['master_card_no'],
                    $checkStationAndGetArray['insert_station_array']['supplementary_card_no'],
                    $checkStationAndGetArray['insert_station_array']['card_user_id']
                );
                if (empty((float)$mirror['mac_amount'])) {
                    $mirror['mac_amount'] = round(bcmul($checkStationAndGetArray['insert_station_mirror']['mac_price'],
                        $oilNum, 3), 2);
                }
                if (!empty($checkCardAndGetArray)) {
                    $mirror = array_merge($mirror, $checkCardAndGetArray['insert_card_mirror'], $checkStationAndGetArray['insert_station_mirror']);
                    //$order['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);

                    $makeOrder = array_merge($order, $checkCardAndGetArray['insert_card_array'], $checkStationAndGetArray['insert_station_array']);
                } else {
                    $mirror = array_merge($mirror, [], $checkStationAndGetArray['insert_station_mirror']);
                    //$order['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);

                    $makeOrder = array_merge($order, [], $checkStationAndGetArray['insert_station_array']);
                }

                $this->addOrderExt($mirror);

                if ($isCoupon && !empty($coupon_type)) {
                    //todo 更改电子券状态
                    $this->couponSrv->updateCoupon($coupon_flag, ['status' => CouponDefine::USING, 'order_id' => $orderId]);
                }

                if ($pushOA) {
                    $amountGun = array_get($params, 'amountGun', 0);
                    $priceGun = array_get($params, 'priceGun', 0);
                    $gunNumber = array_get($params, 'gunNumber', '');
                    $supplierPrice = array_get($mirror, 'supplier_price', 0);
                    //主动付款与扫码付款推送参数不一致
                    $oaData = [
                        'id'             => $orderId,
                        'truck_no'       => $truck_no,
                        'drivertel'      => $driver_phone,
                        'driver_name'      => $driver_name,
                        'pcode'          => $stationInfo['pcode'],//运营商编码
                        'oil_type_id'    => $oilType,
                        'oil_name_id'    => $oilName,
                        'oil_level_id'   => $oilLevel,
                        'trade_money'    => $oilMoney,//消费金额
                        'trade_price'    => $oilPrice,//油品单价
                        'supplier_price' => $supplierPrice,//进价
                        'supplier_money' => round(bcmul($supplierPrice, $realOilNum, 3), 2),//进价计算金额
                        'station_id'     => $stationId,//站点id
                        'pushExtends'    => json_encode([
                            'amountGun' => $amountGun,
                            'price'     => array_get($mirror, 'supplier_price', 0), //运营商价格
                            'priceGun'  => $priceGun,
                            'gunNumber' => $gunNumber
                        ]),
                        'oil_unit'       => $oilUnit,
                        'oil_num'        => $oilNum,
                        'oil_name'       => $mirror['goods'] ?? '',
                        'trade_place'    => $mirror['station_name'] ?? '',
                    ];
                    // 车主帮或者惠运通会返回三方订单号 @todo
                    $resThOrder = $this->messageService->pushData2OA($oaData, $message_type);
                    $makeOrder['third_order_id'] = array_get($resThOrder, "order_id", '');

                } elseif ($driverSource == OrderModel::THIRD_DRIVER) {
                    $oaData = [
                        'id'               => $orderId,
                        'truck_no'         => $truck_no,
                        'station_id'       => $stationId,
                        'station_name'     => $stationInfo['station_name'],
                        'oil_num'          => $oilNum,
                        'price'            => $oilPrice,
                        'money'            => $oilMoney,
                        'pay_price'        => $oilPrice,
                        'pay_money'        => $oilMoney,
                        'xpcode_pay_price' => $checkStationAndGetArray['insert_station_mirror']['supplier_price'], // 进价
                        'xpcode_pay_money' => round(bcmul($checkStationAndGetArray['insert_station_mirror']['supplier_price'], $realOilNum, 3), 2),
                        'card_no'          => $cardNo,
                        'orgcode'          => $cardOrgCode,
                        'oil_name_val'     => $checkStationAndGetArray['insert_station_mirror']['oil_name_name'],
                        'oil_type_val'     => $checkStationAndGetArray['insert_station_mirror']['oil_type_name'],
                        'oil_level_val'    => $checkStationAndGetArray['insert_station_mirror']['oil_level_name'],
                        'oil_name'         => $oilName,
                        'oil_type'         => $oilType,
                        'oil_level'        => $oilLevel,
                        'extends'          => $extends,
                        'oil_time'         => date('Y-m-d H:i:s'),
                        'mac_price'        => $checkStationAndGetArray['insert_station_mirror']['mac_price'],
                        'mac_money'        => $mirror['mac_amount'],
                        'oil_unit'         => $oilUnit,
                    ];
                }
                $makeOrder['order_sale_type'] = in_array(
                    $makeOrder['supplier_code'],
                    CommonDefine::getReservationRefuelSupplier()
                ) ? OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL : OrderModel::ORDER_SALE_TYPE_REFUEL;
                $this->addOrderNew($makeOrder);
                DB::connection('mysql_gas')->commit();
                // PAY_LOG:扫码付款
                if ($message_type == "PAY_LOG") {
                    /**
                     * 下单成功,token失效
                     */
                    $this->redisService->delCreateOrderToken($orderToken);
                }

                /**
                 * 下单成功,5min超时关单
                 */
                if ($orderFlag == CardTradeConf::G7FLAG) { //卡车下单不能关单
                    dispatch(new OrderTimeOut($orderId))->delay(
                        config("orderTimeOut." . ($makeOrder["org_code"] ?? ''), 300)
                    )->onQueue('order-queue');
                }
                // 推送OA及PDA
                if ($driverSource == OrderModel::THIRD_DRIVER) {
                    try{

                        $this->messageService->pushData2OA($oaData, $message_type);
                    } catch (Throwable $throwable) {

                        if ($throwable->getCode() == 5000999) {

                            throw new RuntimeException("三方提示：" . $throwable->getMessage(),
                                $throwable->getCode());
                        }
                        throw $throwable;
                    }

                    // push消息
                    $code = 201;
                    $msg = '等待客户机构处理订单中';
                    $this->messageService->pushMsgToDoper($orderToken, [
                        'code' => $code,
                        'msg'  => $msg
                    ]);
                }

                /**
                 * 卡车订单，需推送给卡车服务器
                 */
                if ($orderFlag == CardTradeConf::TRUCKFLAG) {
                    $kcData['id'] = strval($orderId);
                    $kcData['siteId'] = $stationInfo['station_code'];
                    $kcData['siteName'] = $stationInfo['station_name'];
                    $kcData['payable'] = floatval($oilMoney);
                    $kcData['payAmount'] = floatval($third_money);
                    $kcData['sitePrice'] = floatval($oilPrice);
                    $kcData['g7KcPrice'] = floatval($third_price);
                    $kcData['num'] = floatval($oilNum);
                    if (stripos($mirror['oil_name_name'], "尿素") !== false) {
                        $kcData['type'] = $mirror['oil_type_name'];
                        $kcData['gasOil'] = 4;
                    } elseif (stripos($mirror['oil_name_name'], "柴油") !== false) {
                        $kcData['type'] = $mirror['oil_type_name'];
                        $kcData['gasOil'] = 3;
                    } elseif (stripos($mirror['oil_name_name'], "天然气") !== false) {
                        $kcData['type'] = $mirror['oil_name_name'] == '液化天然气' ? 'LNG' : 'CNG';
                        $kcData['gasOil'] = 2;
                    } else {
                        $kcData['type'] = $mirror['oil_type_name'];
                        $kcData['gasOil'] = 1;
                    }
                    $kcData['userPhone'] = $driver_phone;
                    try {
                        $this->messageService->pushOrder2KC($kcData);
                    } catch (Exception $e) {
                        $this->feishu->sendExceptionToFeiShu($e);
                    }
                }

            } catch (RuntimeException $e) {
                DB::connection('mysql_gas')->rollBack();

                // 飞书报警
                $this->feishu->sendExceptionToFeiShu($e);
                throw new RuntimeException($e->getMessage(), $e->getCode());
            } finally {
                //todo 清空下单缓存
                $this->redisService->releaseLock("order-" . md5($redisCache), $redisCache);
            }
        }


        $res = [
            'order_id'      => strval($orderId),
            'history_id'    => '',
            'station_name'  => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
            'truck_no'      => $makeOrder['truck_no'],
            'driver_phone'  => $makeOrder['driver_phone'],
            'card_no'       => $makeOrder['card_no'],
            'goods'         => $mirror['goods'],
            'oil_price'     => $makeOrder['oil_price'],
            'oil_num'       => $makeOrder['oil_num'],
            'oil_money'     => $makeOrder['oil_money'],
            'service_price' => $makeOrder['service_price'],
            'service_money' => $makeOrder['service_money'],
            'total_money'   => round(bcadd($makeOrder['service_money'], $makeOrder['oil_money'], 3), 2),
            'update_time'   => date("Y-m-d H:i:s", time()),
            'creator'       => $makeOrder['creator'],
            'ndrc_price'    => array_get($mirror, "ndrc_price", 0),
        ];

        //挂牌价为空预警
        if (empty(array_get($mirror, "ndrc_price", 0))) {
            $this->alarmNdrcOrder([
                'order_id'     => strval($orderId),
                'station_name' => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
                'truck_no'     => $makeOrder['truck_no'],
                'card_no'      => $makeOrder['card_no'],
                'goods'        => $mirror['goods'],
                'oil_price'    => $makeOrder['oil_price'],
                'oil_num'      => $makeOrder['oil_num'],
                'oil_money'    => $makeOrder['oil_money'],
                'ndrc_price'   => array_get($mirror, "ndrc_price", 0),
                'oil_name'     => $makeOrder['oil_name'],
            ]);
        }

        try {
            if (!empty($orderId)) {
                if ($message_type == "PAY_LOG") {
                    if ($checkPwd == 0 && $driverSource == 1) {
                        $this->paymentService->finishOrder(['order_id' => $orderId, "third_order_id" => $orderSn, 'kcCallBack' => $kcCallBack], true);
                        //卡车订单需要给pda和司机下发长连接成功消息
                        if ($kcCallBack == 1) {
                            if (!in_array($orderFlag, CardTradeConf::$no_need_notify_driver_pay_result)) {

                                $this->messageService->driverPaySuccess($cardNo, $orderId, $makeOrder['driver_phone']);
                            }
                            $this->messageService->pushMsgToDoper($orderToken, [
                                'code' => 200,
                                'msg'  => '扣款成功',
                                'data' => $res,
                            ]);
                        }
                        return [
                            'code' => 200,
                            'msg'  => '扣款成功',
                            'data' => $res,
                        ];
                    } else {
                        if (in_array($channel, CardTradeConf::$normal_pay_channel)) {
                            if ($channel == CardTradeConf::H5_PAY_CODE) {
                                $this->messageService->pushMsg2H5([
                                    'id'      => $orderId,
                                    'card_no' => $cardNo,
                                    'mobile'  => $makeOrder['driver_phone'],
                                ], 1);
                            } else {
                                // 司机端小程序下发密码
                                $this->driverCheckPassword($cardNo, $orderId);
                            }

                        }
                        if (in_array($channel, CardTradeConf::$card_box_pay_channel)) {
                            // 微信卡包
                            $this->cardDriverCheckPassword($cardNo, $orderId);
                        }
                        $code = 201;
                        $msg = '请和司机确认是否需要输入密码。若无密码，请耐心等待支付结果。';
                        // push消息 (给新版pda下发消息)
                        $this->messageService->pushMsgToDoper($orderToken, [
                            'code' => $code,
                            'msg'  => $msg
                        ]);
                    }
                } else {
                    // 昆仑走车主帮模式，不会走到这里（无卡）
                    // 九鼎模式（下游）：原gas逻辑是直接生成流水，改为先下单并支付
                    // 下单并支付（主动付款并不验密时），须注意原卡包主动付款逻辑有验密时，
                    if ($autoPay == 1) { //如果是验密的卡，需要调用支付接口
                        // 验密（无长连接）

                        // 支付
                        return $this->paymentService->finishOrder(['order_id' => $orderId, "third_order_id" => $orderSn, "password" => array_get($params, 'password', '')], true);
                    }
                }
            } else {
                throw new RuntimeException('下单失败，请重试!', $this->errorCodeService->createOrderError(40405));
            }
        } catch (RuntimeException $e) {
            if ($message_type == "PAY_LOG") {
                $code = 403;
                $msg = $e->getMessage();
                // push消息
                if ($checkPwd || ($driverSource == OrderModel::THIRD_DRIVER)) {
                    $this->messageService->pushMsgToDoper($orderToken, [
                        'code' => $code,
                        'msg'  => $msg,
                        'data' => $res,
                    ]);
                }
            }
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return $res;
    }

    //生成待支付订单
    protected function addOrderNew($order)
    {
        $result = $this->newOrderRepository->insertOrder($order);
        if (!$result) {
            throw new RuntimeException('下单失败,请重试!', $this->errorCodeService->createOrderError(40323));
        }
        return $result;
    }

    //生成订单扩展表数据
    protected function addOrderExt($extData)
    {
        unset($extData['card_level_name']);
        unset($extData['is_check_password']);
        unset($extData['card_type_name']);
        unset($extData['list_price']);
        unset($extData['order_channel_name']);
        $result = $this->newOrderExtRepository->insertOrderExt($extData);
        if (!$result) {
            throw new RuntimeException('下单失败,请重试!', $this->errorCodeService->createOrderError(40323));
        }
        return $result;
    }

    /**
     * 统一下单入口
     * 【下单基础要素描述】
     * @@@@@ 订单来源(端) @@@@@
     * 1:微信小程序（主要来源）2:卡包（待废弃） 3:H5（给下游客户使用） 4:OA（三方先生成订单时）
     * @@@@@ 下单方式 @@@@@
     * 1:主动付款（客户选中油站后主动选择油品按升、金额付款）
     * 2:扫码（加油员用PDA扫客户付款码收款，无小票）
     * 3:小票机（G7给自营站发加油二维码，客户扫码后得到站信息，并主动付款，付款后调用云打印生成小票）
     * @@@@@ 司机来源 @@@@@
     * 1:自营 2:下游
     * @@@@@ 站来源 @@@@@
     * 1:自营 2:上游
     *
     * 【下单流程剖析】
     * @@@@@ 防并发（重复调用下单）@@@@@
     * redis根据请求参数加锁
     * @@@@@ 根据不同订单来源及下单方式生成订单 @@@@@
     * ##### 基础信息校验 #####
     * 1：油站、商品信息校验（包括限站规则，调用Gms）
     * 2：卡信息校验[无卡下单模式除外]，(包括卡信息、卡状态、卡余额、卡日次月限额，调用foss_user)
     * 3：三方特殊逻辑（a：找油价格比对 b：宝兑通、山高以传入价格为准）
     * ##### 下单模式组合 #####
     * 1：***自营站 + 自营司机 + 主动付款***
     *   下单流程描述
     *     1-1：无验密：a：生成订单 b：订单支付（仅传订单号）
     *     1-2：验密：a：生成订单 b：订单支付（订单号 + 密码）
     * 2：***自营站 + 下游司机 + 主动付款***
     *   下单流程描述
     *     1-1：下游生成订单并推送给OA，OA推送给foss_order下单（此时有三方订单号）
     *     1-2：下游订单支付成功后将结果推送OA，OA调用foss_order扣款
     * 3：***自营站 + 自营司机 + 扫码付款***
     *   下单流程描述
     *     1-1：pda扫码后得到加密串，请求OA解码，OA返回前端order_token秘钥（order_token内容存储在redis，包括卡号、司机信息）
     *     1-2：前端携带加油参数，包含order_token给后端，后端通过order_token拿到卡号、司机信息
     *     1-3：无验密：foss_order生成订单并扣款
     *     1-4：验密：a：foss_order生成订单后长连接下发消息通知前端，前端调起密码框 b：客户输入密码后请求foss_order支付订单（订单号 + 密码）
     * 4：***自营站 + 下游司机 + 扫码付款***
     *   下单流程描述
     *     1-1：pda扫码后得到加密串，请求OA解码，OA请求下游服务解码并得到解码内容，返回前端order_token秘钥（order_token内容存储在redis，包括三方司机信息）
     *     1-2：前端携带加油参数，包含order_token给后端，后端通过order_token拿到卡号、司机信息
     *     1-3：foss_order生成订单并推送给OA，OA推送给下游
     *     1-4：下游支付完成通知OA，OA调用foss_order扣款
     *     1-5：若下游未在规定时间内通知OA支付结果，foss_order将超时关单
     * 5：***上游站 + 自营司机 + 扫码付款***
     *   下单流程描述
     *     1-1：上游PDA扫G7客户付款码后，请求OA解码，OA返回上游解码标识，对应卡号及司机信息
     *     1-2：上游生成订单后推送给OA，OA把订单信息及解码信息一起推给foss_order下单
     *     1-3：a：无验密，下单并扣费 b：验密：下单后跟客户端建立长连接，付款后通知OA结果
     *     1-3：foss_order异步完成支付后通知OA支付结果，OA通知上游支付结果
     * 6：***上游站 + 自营司机 + 主动付款***（车主帮模式，下单时不带卡号，支付时带）
     *   下单流程描述
     *     1-1：用户提交订单至foss_order生成订单推送给OA，OA找车主帮下单
     *     1-2：用户确认付款，携订单号及卡号调用foss-order支付，支付时验密或不验密
     *     1-3：foss_order支付完成后同步通知OA支付结果，OA通知车主帮
     * 7：***上游站 + 自营司机 + 主动付款***（昆仑润滑油模式）
     *   下单流程描述
     *     1-1：昆仑将下单信息推送给OA，OA找foss_order下单（没有卡号，注意仅昆仑模式OA不传卡号下单）
     *     1-2：昆仑支付完成后通知OA（验密时会携带密码），OA请求foss_order扣费
     * 8：***上游站 + 自营司机 + 主动付款***（一键付模式）
     *   下单流程描述
     *     1-1：用户提交订单至foss_order生成订单
     *     1-2：无验密：用户确认付款，foss_order支付完成后给OA推送已支付订单，OA推已支付订单给上游客户
     *     1-3：验密：a：foss_order生成订单后长连接下发消息通知前端，前端调起密码框 b：客户输入密码后请求foss_order支付订单 c：foss_order支付完成后给OA推送已支付订单，OA推已支付订单给上游客户
     * 9：***上游站 + 下游司机 + 扫码付款***
     *   下单流程描述
     *     1-1：上游PDA扫G7客户付款码后，请求OA解码，OA请求下游解码，解码完成后返回上游解码标识，对应卡号及司机信息
     *     1-2：上游生成订单后推送给OA，OA把订单信息及解码信息一起推给foss_order下单
     *     1-3：OA将foss_order生成的订单推送给下游
     *     1-4：下游支付完成后通知OA，OA调用foss_order扣款，OA通知上游付款结果
     * 10：***上游站 + 下游司机 + 主动付款***（车主帮模式 暂未支持）
     *   下单流程描述
     *     1-1：下游生成订单并推送给OA，OA推送给foss_order下单（此时有三方订单号）
     *     1-2：OA推送foss_order生成的订单给上游
     *     1-3：下游订单支付成功后将结果推送OA，OA调用foss_order扣款
     *     1-4：OA通知上游付款结果
     * 11：***上游站 + 下游司机 + 主动付款***（一键付模式 暂未支持）
     *   下单流程描述
     *     1-1：下游生成订单并推送给OA，OA推送给foss_order下单（此时有三方订单号）
     *     1-2：下游订单支付成功后将结果推送OA，OA调用foss_order扣款
     *     1-3：OA推送已支付订单给上游
     * 12：***上游站 + 自营司机 + 补录（下单流程类似上游PDA扫码加油，上游发起）***
     *   下单流程描述
     *     1-1：上游发起补录，推送订单信息给OA
     *     1-2：OA调用foss_order补录下单
     *     1-3：foss_order异步扣款后通知OA，OA通知上游支付结果
     * 13：***自营站 + 自营司机 + 补录（Gms后台补录，流程类似自营站PDA扫码加油）***
     *   下单流程描述
     *     1-1：Gms发起补录申请，生成补录工单
     *     1-2：Gms审核补录工单，通过后调用foss_order补录下单并扣款（同步扣款）
     * 14：***自营站 + 下游司机 + 补录（Gms客户后台下单，类似自营站PDA扫下游司机付款码加油）***
     *   下单流程描述
     *     1-1：Gms发起客户后台下单生成工单
     *     1-2：Gms审核工单通过后调用OA推送审核单信息
     *     1-3：Gms通过审核单信息匹配下游司机、卡号等信息
     *     1-4：OA请求foss_order下单，OA推送订单给下游
     *     1-5：下游支付成功后通知OA，OA调用foss_order扣款
     * 备注
     * 【暂未支持的下单模式】
     * 1：[上游站 + 下游司机 + 补录]
     * 2：[上游站 + 下游司机 + 主动付款（车主帮模式）]
     * 3：[上游站 + 下游司机 + 主动付款（一键付模式）]
     * 【取消订单及退款】
     * G7已将标准取消订单及退款接口提供给上游对接
     * 下游基本未提供标准的取消订单及退款接口给G7，目前已知能退款的下游，可在OA手动操作（Gms未对接）
     * 自营站自营司机可在Gms退款（暂未设计主动取消订单功能，已有超时关单功能）
     * 【下单避坑点】
     * 1：卡包主动付款未按下单+付款两步走，直接下单并付款
     *
     * @param array $params
     * @return array
     * @throws RuntimeException
     */
    public function unifiedOrder($params = [])
    {
        // 通用必填参数校验 station_id、oil_name、oil_price
        Common::argumentCheck(['oil_name', 'oil_price'], $params);

        // 参数再组装，涉及下单所有入参处理
        $args = $this->packAllParams($params);
        $orderTokenInfo = !empty($args['order_token']) ? $this->redisService->parseCreateOrderToken($args['order_token']) : [];

        if($orderTokenInfo && !$args['ocr_truck_no_id']){
            $args['ocr_truck_no_id'] = array_get($orderTokenInfo,'ocr_truck_no_id','');
        }

        // 验油站信息
        $filterStation = ['isdel' => 0];
        if (!empty($args['station_id'])) {
            $filterStation['id'] = $args['station_id'];
        } else {
            if (empty($args['pcode']) || empty($args['app_station_id'])) {
                throw new RuntimeException('参数异常，缺少pcode或app_station_id', '4040021');
            }
            $filterStation['pcode'] = $args['pcode'];
            $filterStation['app_station_id'] = $args['app_station_id'];
        }
        $stationInfo = $this->checkStationInfo($filterStation);
        $args['station_id'] = $stationInfo['id'];

        // 获取trade_mode
        if (empty($args['trade_mode'])) {
            if (empty($args['driver_source'])) {
                $args['driver_source'] = !empty($orderTokenInfo['is_access_platform_driver']) ? OrderModel::THIRD_DRIVER : OrderModel::OWN_DRIVER;
                // 自营pda扫下游码
                if ($args['driver_source'] == OrderModel::THIRD_DRIVER && !$args['order_channel']) {
                    $args['order_channel'] = CardTradeConf::THIRD_DOWN_PAY_CODE;
                }
            }
            $args['trade_mode'] = $this->getTradeMode($stationInfo['trade_type'], $stationInfo['pcode'], $args['driver_source']);
        }
        if (!in_array($args['order_channel'], array_keys(CardTradeConf::$trade_from))) {
            throw new RuntimeException('未知的订单来源，illegal order channel', '4040021');
        }
        if (!in_array($args['driver_source'], [OrderModel::THIRD_DRIVER, OrderModel::OWN_DRIVER])) {
            throw new RuntimeException('未知的司机来源，illegal drvier source！', '4040010');
        }
        if (!in_array($args['trade_mode'], array_keys(CardTradeConf::$trade_mode))) {
            throw new RuntimeException('未知的交易模式', '4040011');
        }

        // 校验order_channel与站信息的付款方式是否吻合
        if (in_array($args['trade_mode'], CardTradeConf::$trade_mode_need_compare)) {
            $this->checkStationTradeTypeWithOrderChannel($stationInfo['trade_type'], $args['order_channel']);
        }
        // 自营PDA扫码交易，参数覆盖写
        if (in_array($args['trade_mode'], [CardTradeConf::TRADE_MODE_ZYZ_ZYS_SMP])) {
            if (empty($orderTokenInfo)) {
                throw new RuntimeException('缺少order_token信息！', '4050011');
            }
            $extends = array_get($orderTokenInfo, 'extends', '');
            $args['order_no'] = array_get($orderTokenInfo, 'order_no', '');
            $args['card_no'] = array_get($orderTokenInfo, 'card_no', 0);
            $args['driver_name'] = empty($args['driver_name']) ? array_get($orderTokenInfo, 'driver_name', '') : $args['driver_name'];
            $args['driver_phone'] = empty($args['driver_phone']) ? array_get($orderTokenInfo, 'driver_phone', '') : $args['driver_phone'];
            $args['truck_no'] = empty($args['truck_no']) ? array_get($orderTokenInfo, 'truck_no', '') : $args['truck_no'];
        }

        $this->checkGray($args['driver_phone']);

        // 获取各交易模式下的订单职责
        $orderScope = CardTradeConf::$trade_mode_scope[$args['trade_mode']]['make_order'];

        // 验卡信息（如有必要）
        $packCardData = [];
        if ($orderScope['verify_card'] > 0) {
            if (empty($args['card_no'])) {
                throw new RuntimeException('G7能源账号缺失！', '4050011');
            }
            $packCardData = $this->cardService->checkCardAndGetArray($args['card_no'], $args['service_price'], $args['oil_money'], $args['service_money']);
        }

        // 支持三方特殊客户定价
        $tmpOrgCode = array_get($packCardData, 'insert_card_array.org_code', '');
        $args['orgcode'] = $this->newOrderRepository::getSpecialPriceOrg($args['station_id'], $tmpOrgCode, substr($tmpOrgCode, 0, 6));
        // 限站和限价机构可能不同
        $args['limit_orgcode'] = $tmpOrgCode;

        // 特殊定价 //校验站价信息 insert_station_mirror
        $packStationData = $this->checkAndPackStationPriceData($args, $stationInfo);


        if ($args['orderFlag'] == CardTradeConf::G7FLAG) {
            $args['oil_price'] = $packStationData['price_data']['oil_price'];
        }

        // 按升或按金额计算加油
        $args['oil_unit'] = empty($args['oil_unit']) ? $stationInfo['station_oil_unit'] : $args['oil_unit'];
        $resCal = $this->caculateOilNumAndMoney($args['oil_unit'], $args);
        $originOilNum = (isset($args['oil_num']) && $args['oil_num'] > 0) ? $args['oil_num'] : $resCal['oil_num']; // 原始升数
        $originOilMoney = (isset($args['oil_money']) && $args['oil_money'] > 0) ? $args['oil_money'] : $resCal['oil_money']; // 原始金额

        if (in_array($args['trade_mode'], [CardTradeConf::TRADE_MODE_ZYZ_ZYS_SMP, CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP, CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP_HN,
                                           CardTradeConf::TRADE_MODE_ZYZ_XYS_SMP, CardTradeConf::TRADE_MODE_ZYZ_ZYS_ZDP])) {
            //G7WALLET-1504 下单检查升数
            $this->checkOrderNum($stationInfo, $args, $resCal['oil_num']);
        }

        $args = array_merge($args, $resCal);
        $originThirdPayMoney = $args['third_pay_money'];
        $args['third_money'] = empty($args['third_pay_money']) ? $resCal['oil_money'] : $args['third_pay_money'];

        // 防止重复提交
        $resCache = $this->generateCacheKey($args, $stationInfo);
        $startGetRedisLockTime = microtime(true);
        $existsOrder = $this->redisService->tryGetLock("order-" . md5($resCache['key_content']), $resCache['key_content'], $resCache['expire_time']);
        Log::debug("Finish to acquire redis lock", [
            'cost' => microtime(true) - $startGetRedisLockTime
        ]);
        if (!$existsOrder) {
            throw new RuntimeException('订单已存在,请勿重复下单!', $this->errorCodeService->createOrderError(41301));
        }

        // order_sn处理
        if (empty($args['order_sn'])) {
            if (!empty($args['third_order_id'])) {
                $args['order_sn'] = md5($args['third_order_id'] . $stationInfo['pcode']);
            } else {
                $args['order_sn'] = md5($resCache['key_content'] . time());
            }
        }

        $orderId = '';
        $oaMsgType = $orderScope['oa_msg_type'];
        $wsType = $orderScope['ws_type'];
        $needPwd = $orderScope['need_pwd'];
        $directPay = $orderScope['direct_pay'];
        $createOrder = true;
        $forceDeduct = 0; // 强制扣费，不验密
        if ($needPwd) {
            $checkPwd = array_get($packCardData, 'insert_card_mirror.is_check_password', "");
            $needPwd = ($checkPwd == 1) ? 1 : 0;
            $directPay = (!$needPwd && !$directPay) ? 1 : 0;
        }

        // 主动付款需验证升数及金额 G7WALLET-1966
        if (in_array($args['trade_mode'], CardTradeConf::$trade_mode_zdp)) {
            $filterParams = [
                'mac_price'        => $packStationData['insert_station_mirror']['mac_price'] ?? 0.00,
                'platform_price'   => $packStationData['insert_station_mirror']['platform_price'],
                'amountGun'        => $args['amountGun'],
                'priceGun'         => $args['priceGun'],
                'origin_oil_money' => $originOilMoney,
                'origin_oil_num'   => $originOilNum,
                'oil_price'        => $args['oil_price'],
                'oil_unit'         => $args['oil_unit'],
                //G7WALLET-4858 满帮对接支持站价信息以折扣模式
                'orgcode'         =>  $tmpOrgCode,
                'discount_rate'   => $packStationData['insert_station_mirror']['discount_rate'] ?? null,
                'trade_mode'      => $args['trade_mode'],
                //                'card_no'          => $args['card_no'] ?? '',
            ];
            $resCheckNumAndPrice = $this->validateZdpOilNumAndPrice($filterParams);
            // 重新计算后赋值
            if (!empty($resCheckNumAndPrice)) {
                $args['real_oil_num'] = $resCheckNumAndPrice['real_oil_num'];
                $args['oil_num'] = $resCheckNumAndPrice['oil_num'];
                $args['oil_money'] = $resCheckNumAndPrice['oil_money'];
                $args['third_money'] = empty($originThirdPayMoney) ? $resCheckNumAndPrice['oil_money'] : $originThirdPayMoney;
            }
        }

        //针对卡车宝贝,不用下单   卡车订单是否可以移走？
        if (!empty($args['order_no'])) {
            $orderId = $args['order_no'];
            $createOrder = false;
            $orderInfo = $this->newOrderRepository->getOrderInfoByLock(['order_id' => $orderId]);
            if (!$orderInfo) {
                throw new RuntimeException('该笔订单不存在：' . $orderId, $this->errorCodeService->createOrderError(40501));
            }
            if ($orderInfo['order_flag'] == CardTradeConf::TRUCKFLAG) {
                // 卡车特殊处理
                $needPwd = 0;
                $directPay = 1;
                $wsType = [];
                $forceDeduct = 1;

                // openApi 可另行处理
            } else {
                $needPwd = 0;
                $directPay = 1;
                $wsType = [];
                $forceDeduct = 1;
            }
            $this->orderCompare($args, $orderInfo);
            // 更新三方单号（如果有的话）
            if (!empty($args['third_order_id']) && empty($orderInfo['third_order_id'])) {
                $this->newOrderRepository->updateOrderByOrderId($orderId, ['third_order_id' => $args['third_order_id']]);
            }
            $mirror = $orderInfo['ext'];
            $makeOrder = $orderInfo;
        }

        // 下单
        if ($createOrder) {
            try {
                $startBeginTransactionTime = microtime(true);
                DB::connection('mysql_gas')->beginTransaction();
                Log::debug("Begin transaction finish", [
                    'cost' => microtime(true) - $startBeginTransactionTime
                ]);
                $startGetMysqlLockTime = microtime(true);
                //由于gas_history表中的stream_no是唯一索引，因此下单时，先检查是否已使用
                $checkRepeatOrderInfo = $this->newOrderRepository->getOrderInfoByLock(['order_sn' => $args['order_sn']]);
                Log::debug("finish to acquire mysql lock", [
                    'cost' => microtime(true) - $startGetMysqlLockTime
                ]);
                if ($checkRepeatOrderInfo) {
                    throw new RuntimeException('订单号已使用，请勿重复下单！', $this->errorCodeService->createOrderError(41301));
                }

//                $info = $this->redisService->tryGetLock("order-sn".$args['order_sn'], $args['order_sn'], 5);
//                if ( !$info ) {
//                    throw new RuntimeException('该订单号已使用:' . $args['order_sn'], $this->errorCodeService->createOrderError(40401));
//                }

                // 电子券匹配逻辑
                $isCoupon = false;
                $couponTypeId = '';
                if (in_array($stationInfo['pcode'], CouponDefine::couponPcode())) {

                    $isCoupon = true;
                    $coupons = $this->fossStation->getUseCoupon([
                        'supplier_code' => $stationInfo['pcode'],
                        'station_id'    => $stationInfo['id'],
                    ]);
                    foreach ($coupons as $cv) {

                        if (in_array($args['oil_name'], $cv['use_goods'])) {

                            switch ($stationInfo['station_oil_unit']) {

                                case 1:

                                    if (bccomp($args['amountGun'], $cv['amount'], 2) === 0) {

                                        $couponTypeId = $cv['coupon_type_id'];
                                        break 2;
                                    }
                                    break;
                                case 2:

                                    if (bccomp($args['oil_num'], $cv['amount'], 2) === 0) {

                                        $couponTypeId = $cv['coupon_type_id'];
                                        break 2;
                                    }
                                    break;
                            }
                        }
                    }
                    if (empty($couponTypeId)) {

                        throw new RuntimeException(CommonError::$codeMsg[CommonError::COUPONS_NOT_EXISTS],
                            CommonError::COUPONS_NOT_EXISTS);
                    }
                    $couponModel = $this->couponSrv->getCanUseCoupon($stationInfo['pcode'], $couponTypeId);
                    $args['g7_coupon_flag'] = $couponModel->voucher;
                    $args['third_coupon_flag'] = $couponModel->tripartite_voucher;
                }
                // 订单数据
                $stationInfo['price_data'] = $packStationData['insert_station_mirror'];
                list($orderId, $makeOrder, $mirror) = array_values($this->packCreateOrderData($args, $stationInfo));

                // 卡及站数据补充
                $insertCardMirror = empty($packCardData['insert_card_mirror']) ? [] : $packCardData['insert_card_mirror'];
                $insertCardArray = empty($packCardData['insert_card_array']) ? [] : $packCardData['insert_card_array'];
                $mirror = array_merge($mirror, $insertCardMirror, $packStationData['insert_station_mirror']);
                $makeOrder = array_merge($makeOrder, $insertCardArray, $packStationData['insert_station_array']);
                $makeOrder['order_sale_type'] = in_array(
                    $makeOrder['supplier_code'],
                    CommonDefine::getReservationRefuelSupplier()
                ) ? OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL : OrderModel::ORDER_SALE_TYPE_REFUEL;

                //OCR逻辑
                $ocr_truck_no_id = $args['ocr_truck_no_id'];
                if($ocr_truck_no_id){
                    $ocrInfo = (new FossUser())->getOcrInfo(['id'=>$ocr_truck_no_id]);
                    if(!$ocrInfo){
                        throw new RuntimeException('请拍车牌号ID无效', $this->errorCodeService->createOrderError(40501));
                    }
                    $ocr_truck_no_url = $ocrInfo['oss_url'];
                    $mirror['ocr_truck_no_url'] = $ocr_truck_no_url;
                }
                //OCR逻辑结束

                // 创建订单及其扩展表数据
                $this->addOrderNew($makeOrder);
                $this->addOrderExt($mirror);
                if ($isCoupon && !empty($args['g7_coupon_flag'])) {

                    $this->couponSrv->updateCoupon($args['g7_coupon_flag'], [
                        'status'   => CouponDefine::USING,
                        'order_id' => $orderId
                    ]);
                }
                $orderInfo = $makeOrder;
                $orderInfo['ext'] = $mirror;

                // 车主帮或者惠运通需先生成订单
                if ($oaMsgType == 'ONLINE_PAY_ORDER') {
                    // 惠运通默认给1号枪
                    if (in_array($stationInfo['pcode'], CommonDefine::getSpecialPcode()) && empty($args['gunNumber'])) {
                        $args['gunNumber'] = 1;
                    }
                    $supplierPrice = array_get($mirror, 'supplier_price', 0);
                    $oaData = [
                        'id'             => $orderId,
                        'truck_no'       => $args['truck_no'],
                        'drivertel'      => $args['driver_phone'],
                        'driver_name'      => $args['driver_name'] ?? '',
                        'pcode'          => $stationInfo['pcode'],//运营商编码
                        'oil_type_id'    => $args['oil_type'],
                        'oil_name_id'    => $args['oil_name'],
                        'oil_level_id'   => $args['oil_level'],
                        'trade_money'    => $args['oil_money'],//消费金额
                        'trade_price'    => $args['oil_price'],//油品单价
                        'supplier_price' => $supplierPrice,//进价
                        'supplier_money' => round(bcmul($supplierPrice, $args['real_oil_num'], 3), 2),//进价计算金额
                        'station_id'     => $stationInfo['id'],//站点id
                        'pushExtends'    => json_encode([
                            'amountGun' => $args['amountGun'],
                            'price'     => array_get($mirror, 'supplier_price', 0), //运营商价格
                            'priceGun'  => $args['priceGun'],
                            'gunNumber' => $args['gunNumber']
                        ]),
                        'oil_unit'       => $args['oil_unit'],
                        'oil_num'       => $args['oil_num'],
                        'oil_name'       => $mirror['goods'] ?? '',
                        'trade_place'    => $mirror['station_name'] ?? '',
                        'org_code'     => $orderInfo['org_code'],
                    ];
                    // 车主帮或者惠运通会返回三方订单号 @todo
                    $this->messageService->pushData2OA($oaData, $oaMsgType);
                }

                // 事务提交
                DB::connection('mysql_gas')->commit();

                // 扫码付款等下单成功后再推消息
                if ($oaMsgType == 'PAY_LOG') {
                    $oaData = [
                        'id'               => $orderId,
                        'truck_no'         => $args['truck_no'],
                        'station_id'       => $stationInfo['id'],
                        'station_name'     => $stationInfo['station_name'],
                        'oil_num'          => $args['oil_num'],
                        'price'            => $args['oil_price'],
                        'money'            => $args['oil_money'],
                        'pay_price'        => $args['oil_price'],
                        'pay_money'        => $args['oil_money'],
                        'xpcode_pay_price' => $packStationData['insert_station_mirror']['supplier_price'], // 进价
                        'xpcode_pay_money' => round(bcmul($packStationData['insert_station_mirror']['supplier_price'], $args['real_oil_num'], 3), 2),
                        'card_no'          => $args['card_no'],
                        'orgcode'          => $args['orgcode'],
                        'oil_name_val'     => $packStationData['insert_station_mirror']['oil_name_name'],
                        'oil_type_val'     => $packStationData['insert_station_mirror']['oil_type_name'],
                        'oil_level_val'    => $packStationData['insert_station_mirror']['oil_level_name'],
                        'oil_name'         => $args['oil_name'],
                        'oil_type'         => $args['oil_type'],
                        'oil_level'        => $args['oil_level'],
                        'extends'          => $extends ?? '',
                        'oil_time'         => date('Y-m-d H:i:s'),
                        'oil_unit'         => $args['oil_unit'],
                    ];
                    $this->messageService->pushData2OA($oaData, $oaMsgType);
                }

                // PDA长连接消息推送
                if (in_array('PDA', $wsType)) {
                    $code = 201;
                    $msg = '等待客户机构处理订单中';
                    $this->messageService->pushMsgToDoper($args['order_token'], [
                        'code' => $code,
                        'msg'  => $msg
                    ]);
                }

                // 下单成功,token失效
                if ($oaMsgType == "PAY_LOG") {
                    $this->redisService->delCreateOrderToken($args['order_token']);
                }

                // 下单成功,5min超时关单
                if ($args['orderFlag'] == 'G7') { //卡车下单不能关单

                    // 补录不能超时关单
                    if (!in_array($args['trade_mode'], CardTradeConf::$trade_mode_no_auto_cancel_order)) {
                        dispatch(new OrderTimeOut($orderId))->delay(
                            config('orderTimeOut.' . ($makeOrder['org_code'] ?? ''), 300)
                        )->onQueue('order-queue');
                    }
                }

            } catch (RuntimeException $e) {
                DB::connection('mysql_gas')->rollBack();
                // 飞书报警
                $this->feishu->sendExceptionToFeiShu($e);
                throw new RuntimeException($e->getMessage(), $e->getCode());
            } finally {
                //todo 清空下单缓存
                $this->redisService->releaseLock("order-" . md5($resCache['key_content']), $resCache['key_content']);
            }
        }

        if (empty($orderId)) {
            throw new RuntimeException('下单失败，请重试!', $this->errorCodeService->createOrderError(40405));
        }

        //挂牌价为空预警
        if (empty(array_get($mirror, "ndrc_price", 0))) {
            $this->alarmNdrcOrder([
                'order_id'     => strval($orderId),
                'station_name' => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
                'truck_no'     => $makeOrder['truck_no'],
                'card_no'      => $makeOrder['card_no'],
                'goods'        => $mirror['goods'],
                'oil_price'    => $makeOrder['oil_price'],
                'oil_num'      => $makeOrder['oil_num'],
                'oil_money'    => $makeOrder['oil_money'],
                'ndrc_price'   => array_get($mirror, "ndrc_price", 0),
                'oil_name'     => $makeOrder['oil_name'],
            ]);
        }

        // 下单成功后的动作
        try {
            if ($directPay == 1) {
                $payParams = [
                    'order_id'       => $orderId,
                    'third_order_id' => empty($args['third_order_id']) ? $orderId : $args['third_order_id'],
                    'kcCallBack'     => $forceDeduct,
                ];
                if ($needPwd) {
                    $payParams['password'] = $args['password'];
                }
                $this->paymentService->finishOrder($payParams);
            }

            // 密码下发
            if (in_array('CHECK_PWD', $wsType)) {
                if (in_array($args['order_channel'], CardTradeConf::$normal_pay_channel)) {
                    if ($args['order_channel'] == CardTradeConf::H5_PAY_CODE) {
                        $this->messageService->pushMsg2H5([
                            'id'      => $orderId,
                            'card_no' => $args['card_no'],
                            'mobile'  => $makeOrder['driver_phone'],
                        ], 1);
                    } else {
                        // 司机端小程序下发密码
                        $this->driverCheckPassword($args['card_no'], $orderId);
                    }
                }
                if (in_array($args['order_channel'], CardTradeConf::$card_box_pay_channel)) {
                    // 微信卡包
                    $this->cardDriverCheckPassword($args['card_no'], $orderId);
                }
            }

            if (in_array('PDA', $wsType)) {
                $code = 201;
                $msg = '请和司机确认是否需要输入密码。若无密码，请耐心等待支付结果。';
                // push消息 (给新版pda下发消息)
                $this->messageService->pushMsgToDoper($args['order_token'], [
                    'code' => $code,
                    'msg'  => $msg
                ]);
            }
        } catch (RuntimeException $e) {
            if (in_array('PDA', $wsType)) {
                $code = 403;
                $msg = $e->getMessage();
                // push消息
                $this->messageService->pushMsgToDoper($args['order_token'], [
                    'code' => $code,
                    'msg'  => $msg,
                    'data' => $args,
                ]);
            }
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        // 格式化下单返回数据
        return $this->formatMakeOrderReturnData($orderInfo, $args['trade_mode']);
    }

    /**
     * 下单入参检验及参数再组装
     * @param array $params
     * @return array
     */
    public function packAllParams(array $params)
    {
        $args = [
            'card_no'         => array_get($params, 'card_no', 0),
            'order_sn'        => array_get($params, 'order_sn', ''),
            'trade_mode'      => array_get($params, 'trade_mode', 0),
            'driver_source'   => array_get($params, 'driver_source', 0),
            'oil_unit'        => array_get($params, 'oil_unit', 0),
            'order_token'     => array_get($params, 'order_token', ''),
            'orderFlag'       => array_get($params, 'orderFlag', 'G7'),
            'order_no'        => array_get($params, 'order_no', ''),
            'driver_name'     => array_get($params, 'driver_name', ''),
            'driver_phone'    => array_get($params, 'driver_phone', ''),
            'truck_no'        => array_get($params, 'truck_no', ''),
            'ocr_truck_no_id' => array_get($params, 'ocr_truck_no_id', ''),
            'operator'        => array_get($params, 'operator', ''),
            'operator_id'     => array_get($params, 'operator_id', ''),
            'service_price'   => array_get($params, 'service_price', 0),
            'service_money'   => array_get($params, 'service_money', 0),
            'oil_type'        => array_get($params, 'oil_type', null),
            'oil_name'        => array_get($params, 'oil_name', null),
            'oil_num'         => array_get($params, 'oil_num', 0),
            'oil_level'       => array_get($params, 'oil_level', null),
            'oil_price'       => array_get($params, 'oil_price', 0),
            'oil_money'       => array_get($params, 'oil_money', 0),
            'pay_money'       => array_get($params, 'pay_money', 0),
            'oil_time'        => array_get($params, 'oil_time', ''),
            'auto_pay'        => array_get($params, 'auto_pay', 2), // 1下单并支付 2仅下单
            'third_order_id'  => array_get($params, 'third_order_id', ''),
            'third_pay_money' => array_get($params, 'third_pay_money', 0),
            'order_channel'   => array_get($params, 'order_channel', 0),
            'station_id'      => array_get($params, 'station_id', ''),
            'app_station_id'  => (string)array_get($params, 'app_station_id', ''),
            'pcode'           => array_get($params, 'pcode', ''),
            //            'amountGun'       => array_get($params, 'amountGun', 0),
            //            'gunNumber'       => array_get($params, 'gunNumber', 0),
            //            'priceGun'        => array_get($params, 'priceGun', 0),
            'password'        => array_get($params, 'password', ''),
            'ndrc_price'      => array_get($params, 'ndrc_price', null),
        ];

        if (!empty($params['trade_mode'])) {
            $args = $this->orderParamsValidate($params, $args);
        }

        // 特殊参数
        $args['amountGun'] = array_get($params, 'amountGun', 0);
        $args['gunNumber'] = array_get($params, 'gunNumber', 0);
        $args['priceGun'] = array_get($params, 'priceGun', 0);
        $args['supplier_price'] = array_get($params, 'supplier_price', 0);
        $args['supplier_money'] = array_get($params, 'supplier_money', 0);

        return $args;
    }

    /**
     * 各交易模式下单参数校验
     * @param array $params
     * @param array $args
     * @return bool
     * @throws ParamInvalidException
     */
    public function orderParamsValidate(array $params, array $args)
    {
        Common::argumentCheck(['trade_mode'], $params);
        switch ($params['trade_mode']) {
            // 车主帮模式
            case CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP:
            case CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP:
                $rule = [
                    #'actucal_money' => 'required|numeric', //实付金额
                    'gunNumber' => 'required|numeric',
                    'priceGun'  => 'required|numeric', //油枪价格
                    'amountGun' => 'required|numeric', //输入金额
                    #'price' => 'required|numeric', //结算价
                ];
                $messages = [
                    #'actucal_money.numeric' => '请输入实付金额',
                    'gunNumber.required' => '请输入枪号',
                    'amountGun.required' => '请输入支付金额',
                    'priceGun.required'  => '油枪单价不能为空',
                    #'price.required' => '结算单价不能为空',
                ];
                $validator = Validator::make($params, $rule, $messages);
                if ($validator->fails()) {
                    throw new ParamInvalidException($validator->getMessageBag()->first());
                }
                break;
            default:
        }

        return $args;
    }

    /**
     * 生成缓存key （下单防止重复提交）
     * @param array $params
     * @param $stationInfo
     * @return array
     */
    public function generateCacheKey(array $params, $stationInfo)
    {
        $keyContent = [
            'third_order_id' => $params['third_order_id'],
            'pcode'          => $params['pcode'],
            'org_code'       => $params['orgcode'],
            'card_no'        => $params['card_no'],
            'driver_phone'   => $params['driver_phone'],
            'order_status'   => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
        ];
        //自有站，校验重复下单
        if (in_array($stationInfo['pcode'], CardTradeConf::$ownner_station_pcode) && $stationInfo['trade_type'] == 1) {
            $keyContent = [
                'station_id'   => $params['station_id'],
                'oil_type'     => $params['oil_type'],
                'oil_name'     => $params['oil_name'],
                'oil_level'    => $params['oil_level'],
                'card_no'      => $params['card_no'],
                'driver_phone' => $params['driver_phone'],
                'oil_price'    => $params['oil_price'],
                'oil_num'      => $params['oil_num'],
                'order_status' => [OrderModel::SUCCESS_PAY, OrderModel::WAIT_PAY],
            ];
        }

        return [
            'key_content' => json_encode($keyContent),
            'expire_time' => 60,
        ];
    }

    /**
     * 卡车模式订单校验
     * @param array $params
     * @param $orderInfo
     * @return bool
     * @throws Exception
     */
    public function orderCompare(array $params, $orderInfo)
    {
        $msg = '';
        if ($orderInfo['order_status'] != OrderModel::WAIT_PAY) {
            $msg = '订单状态异常！';
        }
        /*//todo 需要产品确定对比那些参数
        if ($params['oil_name'] != $orderInfo['oil_name'] and empty($msg)) {
            $msg = '油品不一致';
        }*/
        if ($params['station_id'] != $orderInfo['station_id'] and empty($msg)) {
            $msg = "站点不一致！";
        }
        //todo 需要产品确定对比那些参数
        $_pOil = $params['oil_name'] . $params['oil_level'] . $params['oil_type'];
        $_hOil = $orderInfo['oil_name'] . array_get($orderInfo, "oil_level", null) . array_get($orderInfo, "oil_type", null);
        if ($_pOil != $_hOil and empty($msg)) {
            $msg = '商品不一致！';
        }
        if (bccomp($params['oil_price'], $orderInfo['oil_price'], 2) != 0 and empty($msg)) {
            $msg = "价格不一致！";
        }
        if (bccomp(abs((bcsub($params['oil_money'], $orderInfo['oil_money'], 2))), 0.03, 2) > 0 and empty($msg)) {
            $msg = '司机、加油员输入金额不一致！';
        }
        $_key = $orderInfo['order_id'] . "-errorNum";
        $errNum = $this->redisService->getValByKey($_key);
        if (!empty($msg)) {
            $code = '510031';
            if ($errNum >= 1) {
                $code = '510032';
            }
            $this->redisService->addKeyNum($_key);
            if ($orderInfo['order_flag'] == CardTradeConf::OPEN_API_FLAG) {

                $this->messageService::createKafkaMessage(json_encode([
                    'order_no' => (string)$orderInfo['order_id'],
                    'status'   => 2,
                    'reason'   => $msg,
                    'orgcode'  => $orderInfo['ext']['open_api_org_code'],
                ]));
                throw new RuntimeException($msg, $this->errorCodeService->createOrderError(40501));
            }
            //通知司机错误消息
            $this->messageService->pushMsg2H5([
                'id'      => $orderInfo['order_id'],
                'card_no' => $orderInfo['card_no'],
                'mobile'  => $orderInfo['driver_phone'],
                'code'    => $code,
                'msg'     => $msg
            ], 3);
            throw new RuntimeException($msg, $this->errorCodeService->createOrderError(40501));
        }
        $this->redisService->delValByKey($_key);

        return true;
    }

    /**
     * 打包订单信息
     * @param $params
     * @param $stationInfo
     * @return array
     */
    public function packCreateOrderData($params, $stationInfo)
    {
        $ret = [
            'order_id'   => $this->redisService->makeOrderId(),
            'make_order' => [],
            'mirror'     => [],
        ];

        // ============
        // 拼装订单信息
        // ============
        $ret['make_order'] = [
            'order_id'           => $ret['order_id'],
            'order_sn'           => $params['order_sn'],
            'order_status'       => OrderModel::WAIT_PAY,
            'order_type'         => ($stationInfo['trade_type'] == 1) ? 2 : 1,
            'order_channel'      => $params['order_channel'],
            'station_id'         => $stationInfo['id'],
            'card_no'            => $params['card_no'],
            'oil_type'           => $params['oil_type'],
            'oil_name'           => $params['oil_name'],
            'oil_level'          => $params['oil_level'],
            'oil_unit'           => $params['oil_unit'],
            'oil_num'            => $params['oil_num'],
            'oil_price'          => $params['oil_price'],
            'oil_money'          => $params['oil_money'],
            'third_actual_fee'   => $params['third_money'],
            'third_actual_price' => $params['oil_price'],
            'real_oil_num'       => $params['real_oil_num'],
            'service_price'      => $params['service_price'],
            'service_money'      => $params['service_money'],
            'mirror'             => '',
            'third_order_id'     => $params['third_order_id'],
            'creator'            => $params['operator'],
            'driver_source'      => $params['driver_source'],
            'driver_name'        => $params['driver_name'],
            'driver_phone'       => $params['driver_phone'],
            'truck_no'           => $params['truck_no'],
            'updator'            => $params['operator'],
            'trade_no'           => $this->redisService->makeSerialNum(),
            'trade_mode'         => $params['trade_mode'],
            'order_flag'         => $params['orderFlag'],
            'station_code'       => '',
            'province_code'      => '',
            'city_code'          => '',
            'supplier_code'      => '',
            'org_code'           => '',
            'card_type'          => 0,
            'card_level'         => 0,
        ];

        // ============
        // 拼装订单扩展表信息
        // ============
        $ret['mirror'] = [
            'order_id'              => $ret['order_id'],
            'is_sync'               => $params['auto_pay'],
            'oil_time'              => empty($params['oil_time']) ? date('Y-m-d H:i:s') : $params['oil_time'],
            'order_channel_name'    => CardTradeConf::getFrom($params['order_channel']),
            'operator_id'           => $params['operator_id'],
            'order_token'           => $params['order_token'],
            'mac_amount'            => empty((float)$params['amountGun']) ? round(
                bcmul(
                    $stationInfo['price_data']['mac_price'] ?? 0,
                    $params['real_oil_num'],
                    3
                ),
                2
            ) : $params['amountGun'],
            'app_station_id'        => $params['app_station_id'],
            'trade_type'            => $stationInfo['trade_type'],
            'lng'                   => $stationInfo['lng'],
            'lat'                   => $stationInfo['lat'],
            'update_time'           => date('Y-m-d H:i:s'),
            'org_name'              => '',
            'account_type_name'     => '',
            'account_no'            => '',
            'account_name'          => '',
            'station_name'          => '',
            'station_address'       => '',
            'rebate_grade'          => '',
            'province_name'         => '',
            'city_name'             => '',
            'oil_type_name'         => '',
            'oil_name_name'         => '',
            'oil_level_name'        => '',
            'mac_price'             => '',
            'supplier_price'        => '',
            'platform_price'        => '',
            'g7_coupon_flag'        => $params['g7_coupon_flag'] ?? null,
            'third_coupon_flag'     => $params['third_coupon_flag'] ?? null,
            'gun_number'            => $params['gunNumber'] ?? '',
            'original_order_id'     => $params['original_order_id'] ?? $ret['order_id'],
            'master_card_no'        => $stationInfo['master_card_no'] ?? '',
            'supplementary_card_no' => $stationInfo['supplementary_card_no'] ?? '',
            'card_user_id'          => $stationInfo['card_user_id'] ?? '',
        ];

        return $ret;
    }

    /**
     * 校验下单时站的付款方式和order_channel是否一致
     * @param $tradeType
     * @param $orderChannel
     * @return bool
     * @throws RuntimeException
     */
    public function checkStationTradeTypeWithOrderChannel($tradeType, $orderChannel)
    {
        //1:扫一扫|2:感应卡|3:主动付款|4:小票机
        switch ($tradeType) {
            case 1:
                if (!in_array($orderChannel, CardTradeConf::$pay_code_channel)) {
                    throw new RuntimeException('油站付款方式有变化，请重试！', '40326');
                }
                break;
            case 2:
                throw new RuntimeException('油站加油方式设置有误', '40326');
                break;
            //3、4下单支付逻辑均一致，当支付方式为4时交易完需触发小票机打印小票
            case 3:
            case 4:
            case 5:
                if (!in_array($orderChannel, CardTradeConf::$on_line_pay_channel)) {
                    throw new RuntimeException('油站付款方式有变化，请重试', '40326');
                }
                break;
            default:
                throw new RuntimeException('油站加油方式设置有误!', '40326');
        }

        return true;
    }

    /**
     * 获取交易模式
     * @param $pcode
     * @param $driverSource
     * @param $tradeType
     * @return int
     */
    public function getTradeMode($tradeType, $pcode, $driverSource)
    {
        $tradeMode = 0;
        switch ($tradeType) {
            case 1:
                if (in_array($pcode, CardTradeConf::$ownner_station_pcode)) {
                    if ($driverSource == OrderModel::OWN_DRIVER) {
                        $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_ZYS_SMP;
                    } else {
                        $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_XYS_SMP;
                    }

                } else {
                    if ($driverSource == OrderModel::OWN_DRIVER) {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_ZYS_SMP;
                    } else {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_XYS_SMP;
                    }
                }
                break;
            case 3:
            case 4:
            case 5:
                // 上游车主帮模式
                if (in_array($pcode, CommonDefine::getSpecialPcode()) || in_array($pcode, CommonDefine::getPcodeList())) {
                    if ($driverSource == OrderModel::OWN_DRIVER) {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_ZYS_ZDP;
                    } else {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP; // 暂不支持
                    }
                    if (in_array($pcode, CommonDefine::getPayMentPcode())) {
                        if ($driverSource == OrderModel::OWN_DRIVER) {
                            $tradeMode = CardTradeConf::TRADE_MODE_SYZ_ZYS_ZDP_YJF;
                        } else {
                            $tradeMode = CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP_YJF;
                        }
                    }

                    //自营站
                } elseif (in_array($pcode, CardTradeConf::$ownner_station_pcode)) {
                    if ($driverSource == OrderModel::OWN_DRIVER) {
                        $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_ZYS_ZDP;
                    } else {
                        $tradeMode = CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP;
                    }

                    // 一键付
                } elseif (in_array($pcode, CommonDefine::getPayMentPcode())) {
                    if ($driverSource == OrderModel::OWN_DRIVER) {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_ZYS_ZDP_YJF;
                    } else {
                        $tradeMode = CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP_YJF;
                    }
                }
                break;
            default:
                $tradeMode = 0;
        }

        return $tradeMode;
    }

    /**
     * 获取OA来的order_channel
     * @param $tradeMode
     * @param string $clientType
     * @return int
     */
    public function getOaOrderChannel($tradeMode, $clientType = '')
    {
        if (!$clientType) {
            if (in_array($tradeMode, [CardTradeConf::TRADE_MODE_SYZ_ZYS_BL, CardTradeConf::TRADE_MODE_ZYZ_XYS_BL])) {
                $orderChannle = CardTradeConf::AFTERWARDS_ADD;

                // 自营站下游主动付款、上游站下游主动付款
            } elseif (in_array($tradeMode, [
                CardTradeConf::TRADE_MODE_SYZ_XYS_SMP,
                CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP,
                CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP,
                CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP_HN,
                CardTradeConf::TRADE_MODE_SYZ_XYS_ZDP_YJF])) {

                $orderChannle = CardTradeConf::CROSS_PLATFORM;
            } else {
                throw new RuntimeException('交易模式为【' . CardTradeConf::$trade_mode[$tradeMode] . '】时，client_type必填', '40326');
            }
        } else {
            if (!in_array($clientType, array_keys(CardTradeConf::$qrCodeSourceFlagToEnumMapping))) {
                throw new RuntimeException('client_type值非法！', '40326');
            }
            if (in_array($tradeMode, [CardTradeConf::TRADE_MODE_SYZ_ZYS_SMP])) {
                $orderChannle = CardTradeConf::$qrCodeSourceFlagToEnumMapping[$clientType];
            } else {
                throw new RuntimeException('交易模式【' . CardTradeConf::$trade_mode[$tradeMode] . '】和client_type不匹配！', '40326');
            }
        }

        return $orderChannle;
    }

    /**
     * 检查油站信息
     * @param $filter
     * @return array
     * @throws RuntimeException
     */
    public function checkStationInfo(array $filter)
    {
        if (empty($filter)) {
            throw new RuntimeException('站点查询条件缺失' . __METHOD__, '40320');
        }

        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams($filter, "*");
        if (!$stationInfo) {
            throw new RuntimeException('验站失败，站点和商品不存在或已删除', '40321');
        }
        if ($stationInfo['card_classify'] != 2) {
            throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已下线!', '40322');
        }
        if ($stationInfo['isstop'] != 0) {
            throw new RuntimeException('验站失败,站点' . $stationInfo['station_name'] . '已停用!', '40323');
        }
        // 1号卡不支持感应卡模式加油
        if ($stationInfo['trade_type'] == 2) {
            throw new RuntimeException('油站加油方式设置有误', '40326');
        }
        if (in_array($stationInfo['pcode'], CommonDefine::needAssign())) {
            $stationExtData = $this->stationRepository->getOneStationExtByParams(
                ['station_id' => $stationInfo['id']],
                ['master_card_no', 'supplementary_card_no', 'card_user_id']
            );
            if (!$stationExtData or empty($stationExtData->master_card_no) or
                empty($stationExtData->supplementary_card_no) or
                empty($stationExtData->card_user_id)) {
                throw new RuntimeException('该油站未做主副卡映射，请到油站信息管理维护。', '40336');
            }
            $stationInfo['master_card_no'] = $stationExtData->master_card_no;
            $stationInfo['supplementary_card_no'] = $stationExtData->supplementary_card_no;
            $stationInfo['card_user_id'] = $stationExtData->card_user_id;
        }
        return $stationInfo;
    }

    /**
     * 计算加油升数及金额
     * @param $oilUnit
     * @param array $params
     * @return array
     * @throws RuntimeException
     */
    public function caculateOilNumAndMoney($oilUnit, array $params)
    {
        $ret = [
            'oil_money'    => 0,
            'oil_num'      => 0,
            'real_oil_num' => 0,
        ];

        $oilPrice = array_get($params, "oil_price", 0);
        if (!array_key_exists($oilUnit, OrderModel::$OIL_UNIT)) {
            throw new RuntimeException('下单失败,无效的商品单位,请参考:' . implode(',', OrderModel::$OIL_UNIT), '40303');
        }

        //站点加注模式 按升
        if ($oilUnit == OrderModel::FIXED_LITRE) {
            if (empty($params['oil_num'])) {
                throw new RuntimeException('请输入加油升数', '40324');
            }
            $ret['oil_num'] = $params['oil_num'];
//            $ret['oil_money']    = bcmul($oilPrice, $params['oil_num'],10);
            $ret['oil_money'] = round(bcmul($params['oil_num'], $oilPrice, 3), 2);
            $ret['real_oil_num'] = $params['oil_num'];

            // 按金额
        } else {
            if (empty($params['oil_money'])) {
                throw new RuntimeException('请输入加油金额', '40325');
            }
            $ret['oil_money'] = $params['oil_money'];
            $ret['real_oil_num'] = bcdiv($params['oil_money'], $oilPrice, 6);
            $ret['oil_num'] = $this->numberUtil->formatNumber($ret['real_oil_num'], 2);
        }
        if (bccomp($ret['oil_num'], 0.00, 2) < 1) {
            throw new RuntimeException('下单失败，单次交易升数小于0.01升', '40335');
        }
        return $ret;
    }


    /**
     * 主动付款校验加油升数及金额
     * @param array $params
     * @return array
     * @throws RuntimeException
     */
    public function validateZdpOilNumAndPrice(array $params)
    {
        Common::argumentCheck(['oil_unit', 'oil_price', 'origin_oil_num', 'origin_oil_money'], $params);
        $amountGun = array_get($params, "amountGun", 0.00);
        $priceGun = array_get($params, "priceGun", 0.00);
        $platformPrice = array_get($params, "platform_price", 0.00);
        $macPrice = array_get($params, "mac_price", 0.00);
//        $cardNo         = array_get($params,"card_no",'');

        // 一期仅针对顺丰做校验
//        $whiteCardList = explode(',',config('oa.white_card_list'));
//        if (!in_array($cardNo, $whiteCardList)) {
//            return [];
//        }

        // 原始升数及金额大于0时才校验
        if ($params['origin_oil_num'] <= 0 || $params['origin_oil_money'] <= 0) {
            return [];
        }

        //站点加注模式 按升
        if ($params['oil_unit'] == OrderModel::FIXED_LITRE) {
            // 根据G7销价对比金额
            $compareOilMoney = round(bcmul($params['origin_oil_num'], $platformPrice, 3), 2);
            $diffPrice = abs(bcsub($compareOilMoney, $params['origin_oil_money'], 2));
            if (in_array($params['orgcode'], explode(',', env("FORCE_VALIDATE_ORDER_AMOUNT_AND_OIL_VOLUME", ""))) and
                bccomp($compareOilMoney, $params['origin_oil_money'], 2) > 0) {
                throw new RuntimeException('结算价有更新，请获取最新价格后重新下单!!', $this->errorCodeService->createOrderError(40401));
            }
            if (bccomp($diffPrice, 0.30, 2) > 0) {
                throw new RuntimeException('结算价有更新，请获取最新价格后重新下单!!', $this->errorCodeService->createOrderError(40401));
            }
            // 验油机金额
            if ($amountGun > 0 and $macPrice > 0) {
                $compareMacMoney = round(bcmul($params['origin_oil_num'], $macPrice, 3), 2);
                $diffMoney = abs(bcsub($compareMacMoney, $amountGun, 2));
                if (bccomp($diffMoney, 0.30, 2) > 0) {
                    throw new RuntimeException('油机金额计算有误，请联系技术人员!!', $this->errorCodeService->createOrderError(40403));
                }
            }
            $ret = [
                'oil_num'      => $params['origin_oil_num'],
                'oil_money'    => $compareOilMoney, // 以计算价格为准
                'real_oil_num' => $params['origin_oil_num']
            ];
            // 按金额
        } else {
            $ret = ['oil_money' => $params['origin_oil_money']];
            // 接口枪金额及枪单价都存在，且G7枪金额大于0
            if ($amountGun > 0 && $priceGun > 0 && $macPrice > 0) {
                // 验枪标价
                if (bccomp($macPrice, $priceGun, 2) != 0) {
                    throw new RuntimeException('油机价有更新，请获取最新价格后重新下单!', $this->errorCodeService->createOrderError(40401));
                }
                $_tmpNum = $this->numberUtil->formatNumber(bcdiv($amountGun, $macPrice, 3), 3);
                $diffNum = abs(round(bcsub($_tmpNum, $params['origin_oil_num'], 3), 2));
                // 验升
                if (in_array($params['orgcode'], explode(',', env("FORCE_VALIDATE_ORDER_AMOUNT_AND_OIL_VOLUME", ""))) and
                    bccomp($_tmpNum, $params['origin_oil_num'], 2) > 0) {
                    throw new RuntimeException('结算升数计算有误，请联系技术人员', $this->errorCodeService->createOrderError(40401));
                }
                if (bccomp($diffNum, 0.03, 2) > 0) {
                    throw new RuntimeException('结算升数计算有误，请联系技术人员', $this->errorCodeService->createOrderError(40401));
                }
                $settlementType = self::getOrderSettlementType($params['orgcode']);
                switch ($settlementType) {
                    case 2: //按油机金额*折扣率=结算金额校验
                        if(empty($params['discount_rate'])) {
                            throw new RuntimeException('折扣率维护错误，请联系技术人员!!', $this->errorCodeService->createOrderError(40403));
                        }
                        //结算金额（money）=油机金额*折扣率，四舍五入，保留两位小数。
                        $compareOilMoney = round(bcmul($amountGun, bcdiv($params['discount_rate'], 100, 4), 3), 2);
                        //主动付款场景校验结算金额
                        if (bccomp($compareOilMoney, $params['origin_oil_money'], 2) != 0) {
                            throw new RuntimeException('结算金额计算有误，请联系技术人员!!', $this->errorCodeService->createOrderError(40404));
                        }
                        break;
                    case 1: //按结算单价*升数校验=结算金额校验
                    default:
                        // 验结算金额
                        $compareOilMoney = round(bcdiv(bcmul($amountGun, $platformPrice, 6), $macPrice, 3), 2);
                        $diffPrice = abs(bcsub($compareOilMoney, $params['origin_oil_money'], 2));
                        if (bccomp($diffPrice, 0.30, 2) > 0) {
                            throw new RuntimeException('结算金额计算有误，请联系技术人员!!', $this->errorCodeService->createOrderError(40402));
                        }
                        break;
                }
                $ret['real_oil_num'] = bcdiv($amountGun, $macPrice, 6);
            } else {
                $_tmpNum = $this->numberUtil->formatNumber(bcdiv($params['origin_oil_money'], $platformPrice, 3), 3);
                $diffNum = abs(round(bcsub($_tmpNum, $params['origin_oil_num'], 3), 2));
                if (in_array($params['orgcode'], explode(',', env("FORCE_VALIDATE_ORDER_AMOUNT_AND_OIL_VOLUME", ""))) and
                    bccomp($diffNum, 0.00, 2) > 0) {
                    throw new RuntimeException('结算升数计算有误，请联系技术人员', $this->errorCodeService->createOrderError(40401));
                }
                if (bccomp($diffNum, 0.03, 2) > 0) {
                    throw new RuntimeException('结算升数计算有误，请联系技术人员~', $this->errorCodeService->createOrderError(40401));
                }
                // 没必要验结算金额
                $ret['real_oil_num'] = bcdiv($params['origin_oil_money'], $platformPrice, 6);
            }

            // 以计算升数为准
            $ret['oil_num'] = $this->numberUtil->formatNumber($ret['real_oil_num'], 2);
        }

        return $ret;
    }

    /**
     * @Notes:G7WALLET-4858 折扣模式结算的机构
     * @Interface getDiscountSettlementTypeOrgCode
     * @return false|string[]
     * @author: yuanzhi
     * @Time: 2023/12/18   9:52 AM
     */
    public static function getDiscountSettlementTypeOrgCode()
    {
        return explode(",", env('DISCOUNT_SETTLEMENT_TYPE_ORGCODE'));
    }

    /**
     * @Notes:
     * G7WALLET-4858
     * 下游客户下单结算方式：
     * 1、按结算单价*加油升数；
     * 2、按折扣率*加油升数
     * @Interface getOrderSettlementType
     * @param $orgCode
     * @return int
     * @author: yuanzhi
     * @Time: 2023/12/18   9:56 AM
     */
    public static function getOrderSettlementType($orgCode): int
    {
        return in_array($orgCode, self::getDiscountSettlementTypeOrgCode()) ? 2 : 1;
    }

    /**
     * 格式化下单后返回数组
     * @param $orderInfo
     * @param $tradeMode
     * @return array
     */
    public function formatMakeOrderReturnData($orderInfo, $tradeMode)
    {
        if (in_array($tradeMode, CardTradeConf::$trade_mode_oa)) {
            $ret = [
                'order_id'         => strval($orderInfo['order_id']),
                'truck_no'         => $orderInfo['truck_no'],
                'station_id'       => $orderInfo['station_id'],
                'station_name'     => empty($orderInfo['ext']['remark_name']) ? $orderInfo['ext']['station_name'] : $orderInfo['ext']['remark_name'],
                'oil_num'          => $orderInfo['oil_num'],
                'price'            => $orderInfo['oil_price'],
                'money'            => $orderInfo['oil_money'],
                'pay_price'        => $orderInfo['oil_price'],
                'pay_money'        => $orderInfo['oil_money'],
                'xpcode_pay_price' => $orderInfo['ext']['supplier_price'], // 进价
                'xpcode_pay_money' => round(bcmul($orderInfo['ext']['supplier_price'], $orderInfo['real_oil_num'], 3), 2),
                'card_no'          => $orderInfo['card_no'],
                'orgcode'          => $orderInfo['org_code'],
                'oil_name_val'     => $orderInfo['ext']['oil_name_name'],
                'oil_type_val'     => $orderInfo['ext']['oil_type_name'],
                'oil_level_val'    => $orderInfo['ext']['oil_level_name'],
                'oil_name'         => $orderInfo['oil_name'],
                'oil_type'         => $orderInfo['oil_type'],
                'oil_level'        => $orderInfo['oil_level'],
                'oil_time'         => $orderInfo['ext']['oil_time']
            ];
        } else {
            $ret = [
                'order_id'      => strval($orderInfo['order_id']),
                'history_id'    => '',
                'station_name'  => empty($orderInfo['ext']['remark_name']) ? $orderInfo['ext']['station_name'] : $orderInfo['ext']['remark_name'],
                'truck_no'      => $orderInfo['truck_no'],
                'driver_phone'  => $orderInfo['driver_phone'],
                'card_no'       => $orderInfo['card_no'],
                'goods'         => $orderInfo['ext']['goods'],
                'oil_price'     => $orderInfo['oil_price'],
                'oil_num'       => $orderInfo['oil_num'],
                'oil_money'     => $orderInfo['oil_money'],
                'service_price' => $orderInfo['service_price'],
                'service_money' => $orderInfo['service_money'],
                'total_money'   => round(bcadd($orderInfo['service_money'], $orderInfo['oil_money'], 3), 2),
                'update_time'   => date("Y-m-d H:i:s", time()),
                'creator'       => $orderInfo['creator'],
            ];
        }

        return $ret;
    }

    /**
     * @param array $params
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 5:35 下午
     */
    public function getDriverRefundApplicationRecordList(array $params): array
    {
        return DriverRefundApplicationRecordModel::getList($params, $params['page'], $params['limit']);
    }

    /**
     * @param array $params
     * @throws Exception
     * @throws RuntimeException
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 7:17 下午
     */
    public function reviewDriverRefundApplicationRecord(array $params)
    {
        DB::connection("mysql_gas")->beginTransaction();
        $record = DriverRefundApplicationRecordModel::getOneRecord([
            'id' => $params['id'],
        ], ['*'], true);
        if (!$record) {

            throw new RuntimeException("", CommonError::APPROVAL_RECORD_NOT_EXISTS);
        }
        if ($record->approval_status != DriverRefundApplicationRecordModel::WAIT_REVIEW) {

            throw new RuntimeException("", CommonError::APPROVAL_RECORD_AlREADY_REVIEWED);
        }
        $record->approval_status = $params['approval_status'] == 1 ?
            DriverRefundApplicationRecordModel::REVIEW_PASSED_AND_REFUND_SUCCEED :
            DriverRefundApplicationRecordModel::REVIEW_REJECTED;
        $record->reviewer = $params['user_name'];
        $record->approval_reason = $params['approval_reason'] ?? '';
        $record->updated_at = date("Y-m-d H:i:s");
        $record->save();
        $orderRecord = $this->newOrderRepository->getOrderInfoByLock([
            'order_id' => $record->order_no,
        ], ['order_status', 'supplier_code', 'order_flag', 'order_id'], true);
        try {

            switch ($orderRecord['order_flag']) {

                case CardTradeConf::TRUCKFLAG:

                    $this->messageService->pushDriverRefundApprovalStatusToKc([
                        'refundedAuditor' => $params['user_name'],
                        'refundedCode'    => $params['approval_status'],
                        'refundedMsg'     => $params['approval_reason'],
                        'id'              => $record->platform_order_no,
                    ]);
                    break;
                case CardTradeConf::OPEN_API_FLAG:

                    $this->messageService::createKafkaMessage(json_encode([
                        'order_no' => (string)$orderRecord['order_id'],
                        'status'   => $params['msg_type'] == 1 ? 1 : 2,
                        'reason'   => $params['approval_reason'],
                        'orgcode'  => $orderRecord['ext']['open_api_org_code'],
                    ]), 'oil-refund-application-result');
                    break;
            }

        } catch (Throwable $throwable) {

            if ($params['approval_status'] == 2) {

                DB::connection("mysql_gas")->rollBack();
                throw $throwable;
            }
            $record->approval_status = DriverRefundApplicationRecordModel::REVIEW_PASSED_AND_REFUND_FAILED;
            $record->save();
            DB::connection("mysql_gas")->commit();
            throw $throwable;
        }
        if ($orderRecord['order_status'] == OrderModel::WAIT_PAY) {

            $this->newOrderRepository->updateOrderByOrderId($record->order_no, [
                'order_status' => OrderModel::CANCEL,
            ]);
        }
        DB::connection("mysql_gas")->commit();
        if ($orderRecord['order_status'] == OrderModel::SUCCESS_PAY and
            CommonDefine::checkIsG7SupplierCode($orderRecord['supplier_code'])) {

            $this->refundService->oaRefund([
                'order_id'       => $record->order_no,
                'third_order_id' => $record->platform_order_no,
            ]);
        }
    }

    /**
     * 校验站、价并获取信息
     * @param array $params
     * @param array $stationInfo
     * @return array
     */
    public function checkAndPackStationPriceData(array $params, $stationInfo = [])
    {
        Common::argumentCheck(['station_id', 'oil_price', 'oil_name'], $params);

        if (empty($stationInfo)) {
            $stationInfo = $this->checkStationInfo(['id' => $params['station_id'], 'isdel' => 0]);
        }

        // 站点加油信息
        $needParams = [
            'station_id' => $params['station_id'],
        ];
        if (!empty($params['orgcode'])) {
            $limitOrgCode = $params['limit_orgcode'] ?? $params['orgcode'];
            $this->checkInstitutionSpecificAndLowYieldStation($limitOrgCode, $params['station_id']);
            $needParams['orgcode'] = $params['orgcode'];
            if (!empty($params['limit_orgcode'])) {
                $needParams['limit_orgcode'] = $params['limit_orgcode'];
            }
        }
        $stationPriceInfo = $this->fossStation->getOilListAndPrice($needParams);
        if (empty($stationPriceInfo)) {
            throw new RuntimeException('车队长禁止司机在此站加油,请联系车队长', $this->errorCodeService->createOrderError(40317));
        }

        // 获取取价
        $priceMode = $this->getPriceMode($params, $stationInfo);
        $priceData = ['oil_price' => $params['oil_price']];
        $insertStationMirror = [];
        foreach ($stationPriceInfo as $item) {
            if (strcmp($item['oil_type'], $params['oil_type'])) {
                continue;
            }
            if (strcmp($item['oil_name'], $params['oil_name'])) {
                continue;
            }
            if (strcmp($item['oil_level'], $params['oil_level'])) {
                continue;
            }
            if ($item['can_use'] == false) {
                throw new RuntimeException('验价失败,站点指定油品不可用!', $this->errorCodeService->createOrderError(40318));
            }

            // 实时下单取当前油品进价、销价
            if (empty($params['oil_time'])) {
                // 销价验证
                $settlementType = 1;
                if(!empty($params['orgcode'])) {
                    $settlementType = self::getOrderSettlementType($params['orgcode']);
                }
                if ($priceMode['verify_price'] == 1 && $settlementType == 1) {
                    if (abs(bcsub($params['oil_price'], $item['platform_price'], 2))) {
                        throw new RuntimeException('验价失败,油品销价' . $params['oil_price'] . '和实际销价' . $item['platform_price'] . '不同,请核对!', $this->errorCodeService->createOrderError(40319));
                    }
                }
                // 进价验证
                if (empty($item['supplier_price'])) {
                    throw new RuntimeException('验价失败,价格异常,油品进价为0,请联系管理员!', $this->errorCodeService->createOrderError(40320));
                }

                // 扣费单价
                $priceData['oil_price'] = $this->getPayPrice($params['oil_price'], $item['platform_price'], $priceMode['sale_price_mode']);
                $item['platform_price'] = $priceData['oil_price'];
                $item['supplier_price'] = $this->getBuyPrice($params['oil_price'], $item['supplier_price'], $priceMode['buy_price_mode']);

            } else {
                // 补录历史订单取历史油品进价、销价，只能对在生效的油品进行补录
                /**
                 * 价格校验
                 */
                $selectPriceArray = [
                    'spcode'     => $stationInfo['pcode'],
                    'station_id' => $stationInfo['id'],
                    'oil_name'   => $params['oil_name'],
                    'oil_type'   => $params['oil_type'],
                    'oil_level'  => $params['oil_level'],
                    'time'       => $params['oil_time'],
                ];

                $salePrice = $this->fossStation->getSalePrice($selectPriceArray);
                if (empty($salePrice)) {
                    throw new RuntimeException('选择的油站、油品不存在销价,请核对', $this->errorCodeService->createOrderError(40308));
                }
                if (empty($salePrice['supplier_price'])) {
                    throw new RuntimeException('站点应收单价为0,价格异常,请核对', $this->errorCodeService->createOrderError(40310));
                }
                // 历史价格替换
                $item['price_id'] = $salePrice['id'];
                $priceData['oil_price'] = $this->getPayPrice($params['oil_price'], $salePrice['platform_price'], $priceMode['sale_price_mode']);
                $item['platform_price'] = $priceData['oil_price'];
                $item['supplier_price'] = $this->getBuyPrice($params['supplier_price'] ?? 0, $salePrice['supplier_price'], $priceMode['buy_price_mode']);
                $item['mac_price']      = $this->getBuyPrice($params['priceGun'] ?? 0, $salePrice['supplier_mac_price'], $priceMode['buy_price_mode']);
            }

            $gunTankInfo = $item['gun'];
            $insertStationMirror = [
                'oil_type_name'  => $item['oil_type_val'],
                'oil_name_name'  => $item['oil_name_val'],
                'oil_level_name' => $item['oil_level_val'],
                'goods'          => $item['oil_type_val'] . $item['oil_name_val'] . $item['oil_level_val'],
                'gun_id'         => empty($gunTankInfo[0]['gun_id']) ? '' : $gunTankInfo[0]['gun_id'],
                'gun_name'       => empty($gunTankInfo[0]['gun_name']) ? '' : $gunTankInfo[0]['gun_name'],
                'tank_id'        => empty($gunTankInfo[0]['tank_id']) ? '' : $gunTankInfo[0]['tank_id'],
                'tank_name'      => empty($gunTankInfo[0]['tank_name']) ? '' : $gunTankInfo[0]['tank_name'],
                'supplier_price' => $item['supplier_price'],
                'platform_price' => $item['platform_price'],
                'mac_price'      => array_get($item, 'mac_price', 0),
                'ndrc_price'     => array_get($item, 'ndrc_price', null) ?? null,
                'price_id'       => $item['price_id'],
                //【满帮】站价信息以折扣模式推送G7WALLET-4858
                'discount_rate'  => $item['discount_rate'],
            ];
            if (!empty($gunTankInfo) && !empty($gunId)) {
                foreach ($gunTankInfo as $value) {
                    if (!strcmp($value['gun_id'], $gunId)) {
                        $insertStationMirror['gun_id'] = $value['gun_id'];
                        $insertStationMirror['gun_name'] = $value['gun_name'];
                        $insertStationMirror['tank_id'] = $value['tank_id'];
                        $insertStationMirror['tank_name'] = $value['tank_name'];
                        break 2;
                    }
                }
                throw new RuntimeException('验价失败,指定油枪无法使用指定油品加油!', $this->errorCodeService->createOrderError(40321));
            }
            break;
        }

        if (empty($insertStationMirror)) {
            throw new RuntimeException('验价失败,未查询到有效油品,请联系管理员或重新下单!', $this->errorCodeService->createOrderError(40324));
        }

        // 供应商
        $supplierInfo = $this->supplierRepositories->getSupplierByScodes([$stationInfo['pcode']], ['supplier_name', 'scode'], false)->keyBy('scode')->toArray();
        // 省市信息
        $provinceCityDict = (new CityModel())->whereIn('city_code', [
            array_get($stationInfo, 'provice_code', ''),
            array_get($stationInfo, 'city_code', ''),
        ])->get()->keyBy('city_code')->toArray();
        // 挂牌价
//        $provinceCode = $this->provinceAndCityUtil->getMunicipalities($stationInfo['provice_code']);
//        $listPriceArray = array_get($this->fossStation->getTodayListPrice(['city_code' => $provinceCode]),$provinceCode, []);
        $listPrice = '';
//        foreach ($listPriceArray as $item) {
//            if (strcmp($insertStationMirror['oil_type_name'], $item['oil_type_value'])) {
//                continue;
//            }
//            if (strcmp($insertStationMirror['oil_name_name'], $item['oil_name_value'])) {
//                continue;
//            }
//            $listPrice = $item['oil_price'];
//        }
        $insertStationArray = [
            'station_code'  => $stationInfo['station_code'],
            'supplier_code' => $stationInfo['pcode'],
            'province_code' => $stationInfo['provice_code'],
            'city_code'     => $stationInfo['city_code'],
        ];

        $insertStationMirror = array_merge($insertStationMirror, [
            'list_price'      => $listPrice,
            'station_name'    => $stationInfo['station_name'],
            'remark_name'     => $stationInfo['remark_name'],
            'station_address' => $stationInfo['address'],
            'rebate_grade'    => $stationInfo['rebate_grade'],
            'supplier_name'   => empty($supplierInfo[$stationInfo['pcode']]['supplier_name']) ? '' : $supplierInfo[$stationInfo['pcode']]['supplier_name'],
            'province_name'   => empty($provinceCityDict[$stationInfo['provice_code']]['city_name']) ? '' : $provinceCityDict[$stationInfo['provice_code']]['city_name'],
            'city_name'       => empty($provinceCityDict[$stationInfo['city_code']]['city_name']) ? '' : $provinceCityDict[$stationInfo['city_code']]['city_name'],
            'lng'             => array_get($stationInfo, "lng", ''),
            'lat'             => array_get($stationInfo, "lat", ''),
            'app_station_id'  => array_get($stationInfo, "app_station_id", ''),
            'trade_type'      => array_get($stationInfo, "trade_type", ''),
        ]);

        return [
            'insert_station_array'  => $insertStationArray,
            'insert_station_mirror' => $insertStationMirror,
            'price_data'            => $priceData,
        ];
    }

    /**
     * 取价模式
     * ###站点应收单价：进价 客户应付单价：销价##
     * 1：卡车类型订单
     *      1-1.进销价均以G7为准
     *      1-2.订单对比须验证销价
     * 2：普通订单
     *      2-1.山高和宝兑通进价和销价用三方推送的价格
     *      2-2.非山高和宝兑通，销价以三方推送的单价为准，进价已G7库里面的进价为准
     * @param $params
     * @param array $stationInfo
     * @return array
     */
    public function getPriceMode($params, $stationInfo = [])
    {
        Common::argumentCheck(['station_id', 'oil_name', 'trade_mode'], $params);
        $ret = [
            'verify_price'    => 1, // 验价
            'buy_price_mode'  => 1, // 1：进价以G7为准 2：销以传入价格为准
            'sale_price_mode' => 1, // 1：销价以G7为准 2：销价以传入价格为准 3：比价取最低
        ];
        if (empty($stationInfo)) {
            $stationInfo = $this->checkStationInfo(['id' => $params['station_id'], 'isdel' => 0]);
        }
        if (!empty($args['order_no'])) {
            $ret['verify_price'] = 0;
        } else {
            if (in_array($params['trade_mode'], [CardTradeConf::TRADE_MODE_SYZ_ZYS_SMP, CardTradeConf::TRADE_MODE_SYZ_XYS_SMP])) {
                // 找油不验价，进价以G7为准，销价以找油为准
                if (in_array($stationInfo['pcode'], CommonDefine::getComparePricePcode())) {
                    $ret['verify_price'] = 0;
                    $ret['buy_price_mode'] = 1;
                    $ret['sale_price_mode'] = 2;

                } elseif (in_array($stationInfo['pcode'], CommonDefine::getAcceptPricePcode())) {
                    // 宝兑通、山高不验价，进销价以传入价格为准
                    $ret['verify_price'] = 0;
                    $ret['buy_price_mode'] = 2;
                    $ret['sale_price_mode'] = 2;
                } else {
                    $ret['verify_price'] = 0;
                    $ret['buy_price_mode'] = 1;
                    $ret['sale_price_mode'] = 2;
                }

            } elseif (in_array($params['trade_mode'], CardTradeConf::$trade_mode_bl)) {
                // 补录以进价以G7为准，销价以传入价格为准
                $ret['verify_price'] = 0;
                $ret['buy_price_mode'] = 2;
                $ret['sale_price_mode'] = 2;
            } elseif (in_array($params['trade_mode'], [CardTradeConf::TRADE_MODE_ZYZ_XYS_ZDP_HN])) {
                // 黄牛以G7销价为准
                $ret['verify_price'] = 0;
                $ret['buy_price_mode'] = 1;
                $ret['sale_price_mode'] = 1;
            } elseif (in_array($stationInfo['pcode'], CommonDefine::forceAcceptPricePcode())) {
                // 中物流一类供应商不验价，进价以中物流回调价格为准，销价以中物流回调价格为准
                $ret['verify_price'] = 0;
                $ret['buy_price_mode'] = 2;
                $ret['sale_price_mode'] = 1;
            }
        }

        return $ret;
    }

    /**
     * 取支付单价
     * @param $oilPrice 传入价
     * @param $platformPrice G7销价
     * @param $priceMode
     * @return mixed
     */
    public function getPayPrice($oilPrice, $platformPrice, $priceMode)
    {
        switch ($priceMode) {
            // 以G7销价为准
            case 1:
                $salePrice = $platformPrice;
                break;
            // 以传入价格为准
            case 2:
                $salePrice = $oilPrice;
                break;
            // 比价取最低价格
            case 3:
                if (bccomp($oilPrice, $platformPrice, 2) > 0) {
                    $salePrice = $platformPrice;
                } else {
                    $salePrice = $oilPrice;
                }
                break;
            default:
                $salePrice = $oilPrice;
        }

        return $salePrice;
    }

    /**
     * 取进价
     * @param $oilPrice 传入价
     * @param $supplierPrice G7进价
     * @param $priceMode
     * @return mixed
     */
    public function getBuyPrice($oilPrice, $supplierPrice, $priceMode)
    {
        switch ($priceMode) {
            // 以G7进价为准
            case 1:
                $buyPrice = $supplierPrice;
                break;
            // 以传入价格为准
            case 2:
                $buyPrice = $oilPrice;
                break;
            default:
                $buyPrice = $oilPrice;
        }

        return $buyPrice;
    }

    /**
     * @param array $stationInfo
     * @param array $params
     * 下单拦截
     * G7WALLET-1504
     */
    public function checkOrderNum($stationInfo = [], $params = [], $trade_num = 0)
    {
        if (App::environment('prod') || App::environment('pro')) {
            $stationCodeMap = [
                'J55VVB' => ['num' => 100, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['324c232e211f695872bb1e779e5703c3'], 'level' => ['45e583037b951ffdc1f7b4a977ba4a0e']],
                '2KBBCM' => ['num' => 100, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['324c232e211f695872bb1e779e5703c3'], 'level' => ['45e583037b951ffdc1f7b4a977ba4a0e']],
                'N6QBCG' => ['num' => 80.01, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['324c232e211f695872bb1e779e5703c3'], 'level' => ['173699caebef9e0dbdf7f30e06fe9767']],
                'HE7FGH' => ['num' => 80.01, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['324c232e211f695872bb1e779e5703c3'], 'level' => ['173699caebef9e0dbdf7f30e06fe9767']],
            ];
        } else {
            $stationCodeMap = [
                'UUVUTB' => ['num' => 100, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['ab31e8289ebd8fdfea01841072a251c3'], 'level' => ['66d5c1e1ee3aae9642128bf9cdb34157']],
                'MVMVMS' => ['num' => 80.01, 'name' => ['1d7e1e11be017d9fe9d2717abe892837'], 'type' => ['ab31e8289ebd8fdfea01841072a251c3'], 'level' => ['66d5c1e1ee3aae9642128bf9cdb34157']],
            ];
        }
        if (in_array($stationInfo['station_code'], array_keys($stationCodeMap))) {
            $condition = $stationCodeMap[$stationInfo['station_code']];
            if (in_array($params['oil_name'], $condition['name']) && in_array(array_get($params, "oil_type", ""), $condition['type']) && in_array(array_get($params, "oil_level", ""), $condition['level'])
                && bccomp($trade_num, $condition['num'], 2) < 0) {
                throw new RuntimeException('加油数量不符合优惠商品最低数量限制，请重新选择商品！', $this->errorCodeService->createOrderError(50224));
            }
        }
        return true;
    }

    /**
     * 获取订单对应的电子券的核销二维码
     * @param $params
     * @return array
     */
    public function getPayMentQrcode($params)
    {
        Common::argumentCheck(['pcode', 'history_id'], $params);
        //判断该订单供应商是否是河北中石油
        if (!in_array($params['pcode'], CouponDefine::couponPcode())) {
            throw new \RuntimeException('该站点不支持核销二维码模式!', CommonError::INVALID_SUPPLIER_PCODE);
        }
        return (new MiniOrderService())->payMentQrcode(['history_id' => $params['history_id']]);
    }

    /**
     * 获取退款订单（默认获取一天前）
     * @param $params
     * @return mixed
     */
    public function getOrgRefundOrder($params)
    {
        Common::argumentCheck(['org_code'], $params);
        $retCheckDate = $this->validateQueryOrderBillDate($params);
        return (new Foss())->getOrgRefundTrade([
            'orgcode'    => $params['org_code'],
            'begin_time' => $retCheckDate['ge_create_time'],
            'end_time'   => $retCheckDate['le_create_time'],
        ]);
    }

    /**
     * 查询对账单时间校验
     * @param array $params
     * @return array
     */
    public function validateQueryOrderBillDate(array $params)
    {
        $beginTime = !empty($params['date_begin']) ? $params['date_begin'] : date('Y-m-d 00:00:00', strtotime('-1 day'));
        $endTime = !empty($params['date_end']) ? $params['date_end'] . ' 23:59:59' : date('Y-m-d 23:59:59', strtotime('-1 day'));
        if (strtotime($endTime) - strtotime($beginTime) > 7 * 86400) {
            throw new \RuntimeException('超过7天最大时间跨度！', '40131');
        }
        return ['ge_create_time' => $beginTime, 'le_create_time' => $endTime];
    }

    /**
     * @param array $order
     * 报警挂牌价为空
     */
    public function alarmNdrcOrder($params = [])
    {
        //todo 在挂牌价上线之前，需先停用
        $condition['name'] = ['1d7e1e11be017d9fe9d2717abe892837', 'cce3b82b4073484bf734e8c6c1689a0f', '324c232e211f695872bb1e779e5703c3'];
        if (App::environment('prod') || App::environment('pro')) {
            $condition['name'] = ['1d7e1e11be017d9fe9d2717abe892837', '7cda28d88d34a37979392b52e3207072', '8ac4c1be43e03b2ab9a2400bdb6e9fa9'];
        }
        $ndrc = array_get($params, "ndrc_price", 0);
        if (in_array($params['oil_name'], $condition['name']) && empty($ndrc)) {
            $text = "下单无挂牌价\n";
            $text .= "G7订单号：{$params['order_id']}\n";
            $text .= "站点：{$params['station_name']}\n";
            $text .= "油品：{$params['goods']}\n";
            $text .= "结算价格：{$params['oil_price']}\n";
            $text .= "数量：{$params['oil_num']}\n";
            $text .= "金额：{$params['oil_money']}\n";
            $text .= "车牌号：{$params['truck_no']}\n";
            $text .= "预警原因：下单未关联到挂牌价\n";
            Falcon::feishu(config('feishu')['ndrc_price_token'], $text);
        }
    }

    /**
     * 聚合出行获取订单(按运营商查询所有支付成功订单及指定时间范围内退款订单)
     *
     * @param $params
     * @return array
     */
    public function getOrderIdsForJHCX($params): array
    {
        return array_merge($this->newOrderRepository->getBatchOrderByParams([
            'order_status'  => OrderModel::SUCCESS_PAY,
            'supplier_code' => $params['supplier_code'],
        ], ['order_id', 'order_status'], false), $this->newOrderRepository->getBatchOrderByParams([
            'order_status'   => OrderModel::REFUND,
            'supplier_code'  => $params['supplier_code'],
            'ge_create_time' => $params['ge_create_time'],
            'le_create_time' => $params['le_create_time'],
        ], ['order_id', 'order_status'], false));
    }

    /**
     * @param $mobile
     * 检查服务升级
     */
    public function checkGray($mobile)
    {
        $conf = config("service_notice");
        if (!is_null($conf['maintain']['enable']) && !$conf['maintain']['enable'] && !in_array($mobile, $conf['maintain']['white']['mobile'])) {
            throw new \RuntimeException($conf['index_notice']['content'], '5031');
        }
    }

    public function determineOrderIsDeductionFailed($orderId): bool
    {
        $result = $this->newOrderRepository->getOneOrderByParams(['order_id' => $orderId], '*', false);
        if (empty($result)) {
            return true;
        }
        if ($result['order_status'] != OrderModel::SUCCESS_PAY) {
            $orderIsDeductionFailedForAccount = $this->foss->determineOrderIsDeductionFailed([
                'order_id' => $orderId,
            ]);
            if ($orderIsDeductionFailedForAccount) {
                return true;
            }
            return false;
        }
        return false;
    }

    public function getExpiredSoonTripartiteCouponsForDriver($user_id): array
    {
        $userInfo = $this->fossUser->getUserInfo(['uid' => $user_id]);
        if (!isset($userInfo['history_mobiles']) || empty($userInfo) || count($userInfo['history_mobiles']) == 0) {
            throw new \RuntimeException('', CommonError::USER_ERROR);
        }
        $data = OrderModel::getExpiredSoonTripartiteCoupons($userInfo['history_mobiles']);
        foreach ($data['list'] as &$v) {
            $v['order_id'] = (string)$v['order_id'];
            unset($v);
        }
        return $data;
    }

    public function getLimitStation($orgCode = "")
    {
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                "title" => "开始获取客户交易限制",
                "org_code" => $orgCode,
            ]);
        }
        $data = $this->limitRepository->getBatchLimitStation($orgCode);
        if ($rootSpan) {
            $rootSpan->log([
                "title" => "结束获取客户交易限制",
                "org_code" => $orgCode,
                "length" => count($data)
            ]);
        }
        return $data;
    }

    public function checkInstitutionSpecificAndLowYieldStation(string $orgCode, string $stationId)
    {
        $activeOrgCOde = $this->limitRepository->getActiveOrgcode($orgCode);
        $institutionSpecificStation = $this->limitRepository->getInstitutionSpecificStation($activeOrgCOde, $stationId);
        $lowYieldStation = $this->limitRepository->getLowYieldStation($activeOrgCOde, $stationId);
        if (!empty($institutionSpecificStation->first()->available) and strpos(
                $institutionSpecificStation->first()->available,
                '1'
            ) === false) {
            throw new RuntimeException(
                '站点交易限制',
                $this->errorCodeService->createOrderError(40334)
            );
        }
        if ($lowYieldStation->count() > 0) {
            throw new RuntimeException(
                '站点交易限制',
                $this->errorCodeService->createOrderError(40334)
            );
        }
    }

    /**
     * @param array $params
     * @return void
     * @throws Exception
     */
    public function distributeOrderStatus($orderId)
    {
        $orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $orderId], [
            'order_id',
            'order_status',
            'org_code',
            'supplier_code',
        ], true);
        if (empty($orderInfo)) {
            throw new Exception("订单信息不存在,请核实后重试");
        }
        $this->messageService->pushData2OA([
            'order_id'      => $orderId,
            'org_code'      => $orderInfo['org_code'],
            'supplier_code' => $orderInfo['supplier_code'],
            'order_status'  => $orderInfo['order_status'],
            'trade_type'    => $orderInfo['ext']['trade_type'],
        ], 'ORDER_STATUS_SYNC');
    }

    /**
     * 通过预约加油订单查询关联的销售订单
     * @param array $reservationOrderId 预约加油订单ID
     * @return mixed
     */
    public function getOrderInfoByReservationOrder(array $reservationOrderId)
    {
        $orderModel = new OrderModel();
        $orderExtModel = (new OrderExtendModel());
        return $orderExtModel->whereIn('original_order_id', $reservationOrderId)
                             ->whereNotIn("{$orderExtModel->getTable()}.order_id", $reservationOrderId)
                             ->leftJoin(
                                 $orderModel->getTable(),
                                 "{$orderModel->getTable()}.order_id",
                                 "=",
                                 "{$orderExtModel->getTable()}.order_id"
                             )
                             ->get();
    }

    /**
     * 查询订单日维度对账快照数据
     * @param array $params
     * @return array
     */
    public function getDayOrderReconciliationSnapshotData(array $params): array
    {
        return BaseModel::scopeWithCondition(DayOrderReconciliationSnapshotModel::query(), $params)->get()->toArray();
    }

    public function getTruckNoThroughLastMonthOrderOfDriver($user_id)
    {
        $userInfo = $this->fossUser->getUserInfo(['uid' => $user_id]);
        if (!isset($userInfo['history_mobiles']) || empty($userInfo) || count($userInfo['history_mobiles']) == 0) {
            Log::warning("通过用户ID未查询到司机使用过的手机号", [
                'user_id'   => $user_id,
                'user_info' => $userInfo,
            ]);
            return [];
        }
        return OrderModel::scopeWithCondition(OrderModel::query(), [
            'driver_phone'   => $userInfo['history_mobiles'],
            'ge_create_time' => date('Y-m-d 00:00:00', strtotime('-1 month')),
            'le_create_time' => date('Y-m-d H:i:s'),
        ], true)->orderBy('create_time', 'desc')
                         ->get(['truck_no'])
                         ->unique('truck_no')
                         ->pluck('truck_no')
                         ->filter()
                         ->slice(0, 4)
                         ->values();
    }

    public function receivePaymentResultWithOutCoupon($orderId)
    {
        (new Foss())->chargeOff($orderId);
    }

    public function getAssignList(array $params)
    {
        return $this->foss->getAssignList($params);
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function cashierEnterOrder(array $params)
    {
        $orderId = $this->redisService->makeOrderId();
        $orgInfo = OilOrgModel::where('orgcode', $params['org_code'])->first();
        $priceOrgCode = $this->newOrderRepository::getSpecialPriceOrg(
            $params['station_id'],
            $params['org_code'],
            substr($params['org_code'], 0, 6)
        );
        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams([
            'id' => $params['station_id'],
        ]);
        $supplierInfo = \App\Models\SupplierModel::where('scode', $stationInfo['pcode'])->first();
        $provinceCityDict = (new CityModel())->whereIn('city_code', [
            $stationInfo['provice_code'],
            $stationInfo['city_code'],
        ])->get()->keyBy('city_code')->toArray();
        $stationPriceInfo = $this->fossStation->getOilListAndPrice([
            'station_id' => $params['station_id'],
            'orgcode'    => $priceOrgCode,
        ]);
        if (empty($stationPriceInfo)) {
            throw new RuntimeException(
                '车队长禁止司机在此站加油,请联系车队长',
                $this->errorCodeService->createOrderError(40317)
            );
        }
        $platformPrice = $supplierPrice = $macPrice = 0;
        foreach ($stationPriceInfo as $item) {
            if ($item['oil_type'] != $params['oil_type'] or
                $item['oil_name'] != $params['oil_name'] or
                $item['oil_level'] != $params['oil_level']) {
                continue;
            }
            if (!$item['can_use']) {
                throw new RuntimeException(
                    '验价失败,站点指定油品不可用!',
                    $this->errorCodeService->createOrderError(40318)
                );
            }
            $platformPrice = $item['platform_price'];
            $supplierPrice = $item['supplier_price'];
            $macPrice = $item['mac_price'];
            $params['price_id'] = $item['price_id'];
            $params['goods'] = $item['oil_type_val'] . $item['oil_name_val'] . $item['oil_level_val'];
            $params['oil_type_name'] = $item['oil_type_val'];
            $params['oil_name_name'] = $item['oil_name_val'];
            $params['oil_level_name'] = $item['oil_level_val'];
            $params['gun_id'] = empty($item['gun'][0]['gun_id']) ? '' : $item['gun'][0]['gun_id'];
            $params['gun_name'] = empty($item['gun'][0]['gun_name']) ? '' : $item['gun'][0]['gun_name'];
            $params['tank_id'] = empty($item['gun'][0]['tank_id']) ? '' : $item['gun'][0]['tank_id'];
            $params['tank_name'] = empty($item['gun'][0]['tank_name']) ? '' : $item['gun'][0]['tank_name'];
            break;
        }
        if ($platformPrice == 0 || $supplierPrice == 0 || $macPrice == 0) {
            throw new RuntimeException(
                '油价有误,请核对!',
                $this->errorCodeService->createOrderError(40319)
            );
        }
        if ($params['oil_unit'] == OrderModel::FIXED_LITRE) {
            $params['oil_money'] = round(bcmul($params['oil_num'], $platformPrice, 3), 2);
            $params['supplier_money'] = round(bcmul($params['oil_num'], $supplierPrice, 3), 2);
            $params['mac_money'] = round(bcmul($params['oil_num'], $macPrice, 3), 2);
            $params['real_oil_num'] = $params['oil_num'];
        }
        if ($params['oil_unit'] == OrderModel::FIXED_MONEY) {
            $params['real_oil_num'] = bcdiv($params['mac_money'], $macPrice, 6);
            $params['supplier_money'] = round(bcmul($params['real_oil_num'], $supplierPrice, 3), 2);
            $params['oil_money'] = round(bcmul($params['real_oil_num'], $platformPrice, 3), 2);
            $params['oil_num'] = $this->numberUtil->formatNumber($params['real_oil_num'], 2);
        }
        DB::connection('mysql_gas')->beginTransaction();
        try {
            $makeOrder = [
                'order_id'        => $orderId,
                'order_sn'        => 'cashier-enter' . time(),
                'trade_no'        => $this->redisService->makeSerialNum(),
                'order_channel'   => CardTradeConf::CASHIER_ENTER,
                'trade_mode'      => CardTradeConf::TRADE_MODE_ZYZ_ZYS_ENTER,
                'order_status'    => OrderModel::WAIT_PAY,
                'station_id'      => $params['station_id'],
                'station_code'    => $stationInfo['station_code'],
                'province_code'   => $stationInfo['provice_code'],
                'city_code'       => $stationInfo['city_code'],
                'supplier_code'   => $stationInfo['pcode'],
                'org_code'        => $params['org_code'],
                'card_no'         => 0,
                'card_type'       => 0,
                'card_level'      => 0,
                'driver_name'     => $params['driver_name'],
                'driver_source'   => OrderModel::OWN_DRIVER, // 三方司机不允许补录
                'driver_phone'    => $params['driver_phone'],
                'truck_no'        => $params['truck_no'],
                'oil_type'        => $params['oil_type'],
                'oil_name'        => $params['oil_name'],
                'oil_level'       => $params['oil_level'],
                'oil_unit'        => $params['oil_unit'],
                'oil_num'         => $params['oil_num'],
                'oil_price'       => $platformPrice,
                'oil_money'       => $params['oil_money'],
                'real_oil_num'    => $params['real_oil_num'],
                'service_price'   => 0,
                'service_money'   => 0,
                'mirror'          => '',
                'creator'         => $params['operator'],
                'updator'         => $params['operator'],
                'order_sale_type' => OrderModel::ORDER_SALE_TYPE_REFUEL,
            ];
            $mirror = [
                'order_id'          => $orderId,
                'org_name'          => $orgInfo->org_name,
                'account_type_name' => '',
                'account_name'      => '',
                'account_no'        => '',
                'station_name'      => $stationInfo['station_name'],
                'remark_name'       => $stationInfo['remark_name'],
                'station_address'   => $stationInfo['address'],
                'rebate_grade'      => $stationInfo['rebate_grade'],
                'supplier_name'     => $supplierInfo->supplier_name,
                'province_name'     => $provinceCityDict[$stationInfo['provice_code']]['city_name'],
                'city_name'         => $provinceCityDict[$stationInfo['city_code']]['city_name'],
                'oil_type_name'     => $params['oil_type_name'],
                'oil_name_name'     => $params['oil_name_name'],
                'oil_level_name'    => $params['oil_level_name'],
                'goods'             => $params['goods'],
                'gun_id'            => $params['gun_id'],
                'gun_name'          => $params['gun_name'],
                'tank_id'           => $params['tank_id'],
                'tank_name'         => $params['tank_name'],
                'supplier_price'    => $supplierPrice,
                'platform_price'    => $platformPrice,
                'price_id'          => $params['price_id'],
                'order_token'       => '',
                'operator_id'       => $params['operator_id'],
                'app_station_id'    => $stationInfo['app_station_id'],
                'trade_type'        => $stationInfo['trade_type'],
                'lng'               => $stationInfo['lng'],
                'lat'               => $stationInfo['lat'],
                'mac_price'         => $macPrice,
                'mac_amount'        => $params['mac_money'],
                'original_order_id' => $orderId,
                'discount_rate'     => null
            ];
            $makeOrder['third_actual_fee'] = $params['oil_money'];
            $makeOrder['third_actual_price'] = $platformPrice;
            $result = $this->newOrderRepository->insertOrder($makeOrder);
            if (empty($result)) {
                throw new \Exception(
                    '创建订单失败!',
                    $this->errorCodeService->createOrderError(40354)
                );
            }
            $oilTime = date('Y-m-d H:i:s');
            $mirror['oil_time'] = $oilTime;
            $mirror['update_time'] = date("Y-m-d H:i:s", time());
            $extRes = $this->newOrderExtRepository->insertOrderExt($mirror);
            if (!$extRes) {
                throw new \Exception(
                    '创建订单失败!',
                    $this->errorCodeService->createOrderError(40355)
                );
            }
            DB::connection('mysql_gas')->commit();
        } catch (Throwable $throwable) {
            DB::connection('mysql_gas')->rollBack();
            throw $throwable;
        }
        try {
            $fossPayResult = $this->foss->openCardAndAssignAndPay([
                'order_no'            => $orderId,
                'sale_type'           => '现金消费',
                'unit'                => $params['oil_unit'],
                'station_id'          => $params['station_id'],
                'station_name'        => $stationInfo['station_name'],
                'station_code'        => $stationInfo['station_code'],
                'pay_money'           => $params['oil_money'], // 不带服务费
                'pay_unit_price'      => $platformPrice,
                'service_money'       => 0,
                'oil_num'             => $params['oil_num'],
                'third_order_no'      => $orderId,
                'oil_name'            => $params['goods'],
                'oil_type_name'       => $params['oil_name_name'],
                'trade_place'         => $stationInfo['station_name'], //油站名称
                'trade_address'       => $stationInfo['address'], //油站地址
                'oil_time'            => $oilTime,
                'pcode'               => $stationInfo['pcode'],
                'orgcode'             => $params['org_code'],
                'province_code'       => $stationInfo['provice_code'],
                'province_name'       => $provinceCityDict[$stationInfo['provice_code']]['city_name'],
                'city_code'           => $stationInfo['city_code'],
                'city_name'           => $provinceCityDict[$stationInfo['city_code']]['city_name'],
                'open_check_password' => 0,
                'card_password'       => '',
                'imgurl'              => '', // PDA加油手工签名地址
                'truck_no'            => $params['truck_no'],
                'drivername'          => $params['driver_name'],
                'drivertel'           => $params['driver_phone'],
                'rebate_grade'        => $stationInfo['rebate_grade'],
                'xpcode_pay_price'    => $supplierPrice, // 站点应收(进价)
                'xpcode_pay_money'    => $params['supplier_money'],
                'truename'            => $params['operator'],
                'remark'              => '',
                'trade_from'          => CardTradeConf::CASHIER_ENTER,
                'real_oil_num'        => $params['real_oil_num'],
                'mac_price'           => $macPrice,
                'mac_amount'          => $params['mac_money'] ?? 0,
                'price_id'            => $params['price_id'],
                'original_order_id'   => $orderId,
                'document_type'       => 10,
                'order_type'          => OrderModel::ORDER_SALE_TYPE_REFUEL,
                'ocr_truck_no_url'    => $params['ocr_url'],
                'pay_ext_info'        => [],
            ]);
            $paymentId = $fossPayResult['payment_id'];
            $makeOrder['pay_time'] = date('Y-m-d H:i:s');
            $makeOrder['payment_id'] = $paymentId;
            $makeOrder['card_password'] = '';
            $makeOrder['old_id'] = $orderId;
            $makeOrder['card_no'] = $fossPayResult['card_no'];
            $makeOrder['mirror'] = json_encode($mirror, JSON_UNESCAPED_UNICODE);
            $makeOrder['supplier_money'] = $params['supplier_money'];
            $historyId = app(HistoryService::class)->addHistoryAfterPay(
                $makeOrder,
                HistoryModel::COST_SUCCESS
            );
            $this->newOrderExtRepository->updateOrderExtByOrderId($orderId, [
                'account_type_name' => '现金账户',
                'account_no'        => $fossPayResult['account_no'],
                'account_name'      => $fossPayResult['account_name'],
                'update_time'       => $makeOrder['pay_time'],
            ]);
            $this->newOrderRepository->updateOrderByOrderId($orderId, [
                'order_status'   => OrderModel::SUCCESS_PAY,
                'payment_id'     => $paymentId,
                'pay_time'       => $makeOrder['pay_time'],
                'third_order_id' => '',
                'history_id'     => $historyId,
                'remark'         => '',
                'updator'        => '系统',
                'service_money'  => $fossPayResult['service_money'],
                'service_price'  => round(
                    bcdiv($fossPayResult['service_money'], $params['oil_num'], 3),
                    2
                ),
                'card_no'        => $fossPayResult['card_no'],
                'card_type'      => OrderModel::VALUE_CARD,
                'card_level'     => OrderModel::PERSONAL_CARD,
            ]);
        } catch (Throwable $throwable) {
            $this->newOrderRepository->updateOrderByOrderId($orderId, [
                'order_status' => OrderModel::FAIL_PAY,
                'remark'       => substr($throwable->getMessage(), 0, 200),
                'updator'      => '系统'
            ]);
            throw $throwable;
        }
        return [
            'order_id'       => $orderId,
            'station_name'   => $stationInfo['station_name'],
            'truck_no'       => $params['truck_no'],
            'org_name'       => $orgInfo->org_name,
            'driver_phone'   => $params['driver_phone'],
            'card_no'        => $fossPayResult['card_no'],
            'goods'          => $params['goods'],
            'oil_num'        => $params['oil_num'],
            'mac_price'      => $macPrice,
            'mac_amount'     => $params['mac_money'],
            'operation_time' => $oilTime,
            'operator'       => $params['operator'],
        ];
    }
}