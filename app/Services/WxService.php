<?php


namespace App\Services;


use App\Jobs\SendWxTemplateMessage;
use Illuminate\Support\Facades\Queue;


class WxService
{
    public static function asyncBatchSendTemplateMessage(array $parameters)
    {
        if ((!isset($parameters['toUser']) or !isset($parameters['data']) or !isset(
                    $parameters['templateId'])) and (!is_array($parameters['toUser']) or !is_array(
                    $parameters['data']))) {

            return;
        }

        foreach ($parameters['toUser'] as $v) {

            Queue::push(new SendWxTemplateMessage($v, $parameters['templateId'], $parameters['data']), '',
                'send-wx-template-message');
        }
    }
}