<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/20
 * Time: 10:05
 */
namespace App\Servitization;
use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class Adapter
{

    private  $config;

    public function __construct()
    {
        $this->config = config('adapter');
    }

    /**
     * 执行[接口均走的OA白名单]
     *
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @param bool $addKafkaLog
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL, $addKafkaLog = false)
    {
        $params = $args;
        try {
            $params['data'] = json_encode($params['data']);
            $url = $this->config['apiUrl'].$params['method'];
            unset($params['method']);
            $sign = $this->createSign($params, $this->config['app_secret']);
            $params['sign'] = $sign;

            $startTime = microtime(true)*1000;
            $data = Common::request($type, $url,60, $params);
            $cost = microtime(true)*1000 - $startTime;
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($args['method'], 'oil-adapter', $params, $data, $cost);
            }
            if (!$data) {
                throw new RuntimeException('adapter服务器异常,请稍后再试', CommonError::ADAPTER_ERROR);
            }
            Log::info('请求adapter,参数:' . json_encode($params, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);
            $data = json_decode($data, true);

            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (Exception $e) {
            KafkaLog::thirdOperationLog($args['method'], 'oa-web', $params, $e->getMessage());
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    public function createSign($data, $secret)
    {
        $sign = $secret;
        ksort($data);
        foreach ($data as $key => $val) {
            if ($key != '' && !is_null($val)) {
                $sign .= $key . $val;
            }
        }
        $sign .= $secret;
        $sign = strtoupper(md5($sign));

        return $sign;
    }

    //获取车主邦枪号
    public function checkGun($params)
    {
        $data = self::exec('post', [
            'method' => '/oil/getGunNosByStationAndOil',
            'data'   => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key' => $this->config['app_key'],
        ]);
        return $data;
    }

    //批量获取枪号
    public function batchGun( $params )
    {
        $data = self::exec('post', [
            'method' => '/oil/getGunNosByStation',
            'data'   => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key' => $this->config['app_key'],
        ]);
        return $data;
    }

    //生成壳牌二维码
    public function createPayMentQrcode($params)
    {
        $data = self::exec('post', [
            'method' => '/code/getSecondaryPaymentQrCode',
            'data'   => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key' => $this->config['app_key'],
        ]);
        return $data;
    }

    /**
     * gms补录审核通过后通知OA扣款
     *
     * @param $params
     * @return mixed
     */
    public function sendAdditionalToOa($params)
    {
        $data = self::postExec('/order/reToBePaid', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ]);

        return $data;
    }

    public function parseQrCode(array $params)
    {
        return self::postExec('/code/parse', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ]);
    }

    /**
     * 查询订单信息
     *
     * @param $params
     * @return mixed
     */
    public function queryOrderExchange($params)
    {
        $data = self::postExec('/order/query', [
            'data' => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key' => $this->config['app_key'],
        ]);
        /*
         * {"platform_order_status" : 2}
         */
        return $data;
    }

    /**
     * 通知三方下单
     *
     * @param $params
     * @return mixed
     */
    public function sendCreateOrderMsg($params)
    {
        $data = self::postExec('/data/push', [
            'data' => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key' => $this->config['app_key'],
        ], NULL, true);

        return $data;
    }

    /**
     * POST请求
     *
     * @param $path
     * @param array $args
     * @param callable|NULL $callback
     * @param bool $addKafkaLog
     * @return mixed
     */
    protected function postExec($path, array $args, callable $callback = NULL, $addKafkaLog = false)
    {
        $params = $args;
        try {
            $params['data'] = json_encode($params['data'],JSON_UNESCAPED_UNICODE);
            $sign = $this->createSign($params, $this->config['app_secret']);
            $params['sign'] = $sign;
            $url = $this->config['apiUrl'].$path;

            $startTime = microtime(true) * 1000;
            $data = Common::request('POST', $url, 120, $params);
            $cost = microtime(true) * 1000 - $startTime;

            Log::info('请求adapter-runtime:'.$cost.',url:'.$url.',参数:' . json_encode($params, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);

            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($path, 'oil-adapter', $params, $data, $cost);
            }

            if (!$data) {
                throw new RuntimeException('adapter服务器异常,请稍后再试', CommonError::ADAPTER_ERROR);
            }

            $data = json_decode($data, true);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (Exception $e) {
            KafkaLog::thirdOperationLog($path, 'oil-adapter', $params, $e->getMessage());
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    public function getCouponStatus($params = [])
    {
        return self::postExec('/coupon/getState', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ], NULL, true);
    }

    public function cancelCoupon($params = [])
    {
        return self::postExec('/coupon/cancel', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ], NULL, true);
    }

    public function refundCustomer($params = [])
    {
        return self::postExec('/order/refundCustomerForOrderCenter', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ], NULL, true);
    }

    public function parseQrCodeForElectricity(array $params)
    {
        return self::postExec('/code/parseElectricity', [
            'data'      => $params,
            'timestamp' => Carbon::now('Asia/Shanghai')->toDateTimeString(),
            'app_key'   => $this->config['app_key'],
        ]);
    }
}