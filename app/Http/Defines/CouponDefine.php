<?php
/**
 * Functions description
 * Created by PhpStorm.
 * User: youki
 * Date: 2018/4/11
 * Time: 上午10:02
 */

namespace App\Http\Defines;

class CouponDefine
{
    /**
     * 在售状态 - 在售
     */
    CONST SELL_ON = 1;

    /**
     * 在售状态 - 停售
     */
    CONST SELL_STOP = 2;

    /**
     * 展示状态 - 展示
     */
    CONST SHOW = 1;

    /**
     * 展示状态 - 隐藏
     */
    CONST HIDE = 2;

    /**
     * 券加注类型 - 金额券
     */
    CONST REFUEL_AMOUNT = 1;

    /**
     * 券加注类型 - 数量券
     */
    CONST REFUEL_QUANTITY = 2;

    /**
     * 券加注类型 - 默认
     */
    CONST REFUEL_DEFAULT = 0;

    /**
     * 电子券状态 - 未占用
     */
    const NO_USE = 1;

    /**
     * 电子券状态 - 已占用
     */
    const USING = 5;

    /**
     * 电子券状态 - 未核销
     */
    const NO_CHARGE_OFF = 10;

    /**
     * 电子券状态 - 已核销
     */
    const CHARGE_OFFED = 15;

    /**
     * 电子券状态 - 已作废
     */
    CONST INVALID = 20;

    // 电子券状态 - 取消核销
    CONST CANCEL_CHARGE_OFF = 30;

    /**
     * 使用类型编码 - 油品券
     */
    CONST BIZ_TYPE_OIL = 4001;

    /**
     * 使用类型编码 - 汽油券
     */
    CONST BIZ_TYPE_QI_YOU = 4002;

    /**
     * 使用类型编码 - 柴油券
     */
    CONST BIZ_TYPE_CHAI_YOU = 4003;

    const STATUS_LIST = [
        self::NO_USE => '未占用',
        self::USING => '已占用',
        self::NO_CHARGE_OFF => '待核销',
        self::CHARGE_OFFED => '已核销',
        self::INVALID => '已作废',
        self::CANCEL_CHARGE_OFF => '取消核销',
    ];

    //中石油电子券
    const PCODE_ZSY_COUPON = '2000VSVCS2V2VU';
    const PCODE_ZSY_COUPON_ONLINE = '20008QONSL2AD5';

    // 湖北中石油电子券
    const P_CODE_ZSY_COUPON_HU_BEI = '20003CAMMM';
    const P_CODE_ZSY_COUPON_ONLINE_HU_BEI = '2000ASJCSU';

    //DT运营商
    const PCODE_DT = "2000VSVC3V3A4B";
    const PCODE_DT_ONLINE = "20008QONBCCVJA";

    CONST SELL_STATUS = [
        self::SELL_ON   => '在售',
        self::SELL_STOP => '停售'
    ];

    const SHOW_STATUS = [
        self::SHOW => '展示',
        self::HIDE => '隐藏'
    ];

    const REFUEL_TYPE = [
        self::REFUEL_AMOUNT   => '金额券',
        self::REFUEL_QUANTITY => '数量券'
    ];

    const COUPON_WRITE_OFF_HELP = [
        CommonDefine::HB1_TEST_SUPPLIER_CODE => '<div><p style="font-weight: 400">使用说明：</p>
          <p style="font-weight: 500">1.请先选择加油金额，支付成功后将成功页面的二维码出示给加油员核销，核销成功后在提枪加油；</p>
          <p style="font-weight: 500">2.加油枪每出一次油，仅能使用一张二维码进行核销。如还需加油，需先挂枪结算，重新支付成功后再次提枪加油；</p>
          <p style="font-weight: 500">3.本核销码不找零不兑现</p>
        </div>',
        CommonDefine::HB1_PROD_SUPPLIER_CODE => '<div>
          <p style="font-weight: 400">使用说明：</p>
          <p style="font-weight: 500">1.请先选择加油金额，支付成功后将成功页面的二维码出示给加油员核销，核销成功后在提枪加油；</p>
          <p style="font-weight: 500">2.加油枪每出一次油，仅能使用一张二维码进行核销。如还需加油，需先挂枪结算，重新支付成功后再次提枪加油；</p>
          <p style="font-weight: 500">3.本核销码不找零不兑现</p>
        </div>',
        CommonDefine::HB2_TEST_SUPPLIER_CODE => '<div><p style="font-weight: 400">使用说明：</p>
          <p style="font-weight: 500">1.请先选择加油金额，支付成功后将成功页面的二维码出示给加油员核销，核销成功后在提枪加油；</p>
          <p style="font-weight: 500">2.加油枪每出一次油，仅能使用一张二维码进行核销。如还需加油，需先挂枪结算，重新支付成功后再次提枪加油；</p>
          <p style="font-weight: 500">3.本核销码不找零不兑现</p>
        </div>',
        CommonDefine::HB2_PROD_SUPPLIER_CODE => '<div>
          <p style="font-weight: 400">使用说明：</p>
          <p style="font-weight: 500">1.请先选择加油金额，支付成功后将成功页面的二维码出示给加油员核销，核销成功后在提枪加油；</p>
          <p style="font-weight: 500">2.加油枪每出一次油，仅能使用一张二维码进行核销。如还需加油，需先挂枪结算，重新支付成功后再次提枪加油；</p>
          <p style="font-weight: 500">3.本核销码不找零不兑现</p>
        </div>',
    ];

    const COUPON_STATUS_TRANSLATION = [
        CommonDefine::HB1_TEST_SUPPLIER_CODE => [
            'NORMAL'      => '未核销',
            'CONSUMED'    => '已核销',
            'EXPIRE'      => '已过期',
            'UNAVAILABLE' => '已作废',
        ],
        CommonDefine::HB1_PROD_SUPPLIER_CODE => [
            'NORMAL'      => '未核销',
            'CONSUMED'    => '已核销',
            'EXPIRE'      => '已过期',
            'UNAVAILABLE' => '已作废',
        ],
    ];

    //电子券运营商
    static public function couponPcode()
    {
        if (\App::environment('prod')) {
            return [self::PCODE_ZSY_COUPON_ONLINE, CommonDefine::HB2_PROD_SUPPLIER_CODE,CommonDefine::SH_PROD_SUPPLIER_CODE,];
        } else {
            return [self::PCODE_ZSY_COUPON, CommonDefine::HB2_TEST_SUPPLIER_CODE,CommonDefine::SH_TEST_SUPPLIER_CODE,];
        }
    }

    static public function isCouponPCode($code)
    {
        return in_array($code, self::couponPcode());
    }

    static public function getHeBeiZSYPCode()
    {
        if (\App::environment('prod')) {
            return self::PCODE_ZSY_COUPON_ONLINE;
        } else {
            return self::PCODE_ZSY_COUPON;
        }
    }

    static public function getHuBeiZSYPCode()
    {
        if (\App::environment('prod')) {
            return CommonDefine::HB2_PROD_SUPPLIER_CODE;
        } else {
            return CommonDefine::HB2_TEST_SUPPLIER_CODE;
        }
    }

    static public function getShPCode()
    {
        if (\App::environment('prod')) {
            return CommonDefine::SH_PROD_SUPPLIER_CODE;
        } else {
            return CommonDefine::SH_TEST_SUPPLIER_CODE;
        }
    }

    //二维码刷新按钮配置
    static public function showRefreshBtn()
    {
        if (\App::environment('prod')) {
            return [self::PCODE_ZSY_COUPON_ONLINE, self::PCODE_DT_ONLINE, CommonDefine::HB1_PROD_SUPPLIER_CODE,
                    CommonDefine::HB2_PROD_SUPPLIER_CODE, CommonDefine::HSY_PROD_SUPPLIER_CODE,
                    CommonDefine::ZDC_PROD_SUPPLIER_CODE,CommonDefine::SH_PROD_SUPPLIER_CODE,
                    CommonDefine::XMSK_PROD_SUPPLIER_CODE, CommonDefine::SH_SX_PROD_SUPPLIER_CODE,
                ];
        } else {
            return [self::PCODE_ZSY_COUPON, self::PCODE_DT, CommonDefine::HB1_TEST_SUPPLIER_CODE,
                    CommonDefine::HB2_TEST_SUPPLIER_CODE, CommonDefine::HSY_TEST_SUPPLIER_CODE,
                    CommonDefine::ZDC_TEST_SUPPLIER_CODE, CommonDefine::SH_TEST_SUPPLIER_CODE,
                    CommonDefine::XMSK_TEST_SUPPLIER_CODE, CommonDefine::SH_SX_TEST_SUPPLIER_CODE,
                ];
        }
    }

    static public function getMap($status)
    {
        return isset(self::STATUS_LIST[$status]) ? self::STATUS_LIST[$status] : "-";
    }

    //退款特殊处理供应商
    static public function specialRefundSupplierCode(): array
    {
        if (\App::environment('prod')) {
            return [self::PCODE_ZSY_COUPON_ONLINE, CommonDefine::HB1_PROD_SUPPLIER_CODE,
                    CommonDefine::HB2_PROD_SUPPLIER_CODE, CommonDefine::SH_PROD_SUPPLIER_CODE,
                    CommonDefine::SH_SX_PROD_SUPPLIER_CODE,];
        } else {
            return [self::PCODE_ZSY_COUPON, CommonDefine::HB1_TEST_SUPPLIER_CODE,
                    CommonDefine::HB2_TEST_SUPPLIER_CODE, CommonDefine::SH_TEST_SUPPLIER_CODE,
                    CommonDefine::SH_SX_TEST_SUPPLIER_CODE,];
        }
    }

    //支持撤销订单关联优惠券的供应商名单
    static public function canCancelOrderCouponSupplierCode(): array
    {
        if (\App::environment('prod')) {
            return [CommonDefine::HB1_PROD_SUPPLIER_CODE];
        } else {
            return [CommonDefine::HB1_TEST_SUPPLIER_CODE];
        }
    }

    /**
     * @return array[]
     * 映射券适用商品
     */
    static public function getCouponToGoodsMapping()
    {
        return [
            self::PCODE_ZSY_COUPON_ONLINE.self::BIZ_TYPE_OIL => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "7cda28d88d34a37979392b52e3207072",
                "ad6164b03a87295d4a983d92fb2cabc0",
            ],
            self::PCODE_ZSY_COUPON_ONLINE.self::BIZ_TYPE_QI_YOU => [
                "8ac4c1be43e03b2ab9a2400bdb6e9fa9"
            ],
            self::P_CODE_ZSY_COUPON_ONLINE_HU_BEI.self::BIZ_TYPE_OIL => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "7cda28d88d34a37979392b52e3207072",
                "ad6164b03a87295d4a983d92fb2cabc0",
            ],
            self::P_CODE_ZSY_COUPON_ONLINE_HU_BEI.self::BIZ_TYPE_CHAI_YOU => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "7cda28d88d34a37979392b52e3207072",
                "ad6164b03a87295d4a983d92fb2cabc0",
            ],
            self::P_CODE_ZSY_COUPON_ONLINE_HU_BEI.self::BIZ_TYPE_QI_YOU => [
                "8ac4c1be43e03b2ab9a2400bdb6e9fa9",
            ],
            CommonDefine::SH_PROD_SUPPLIER_CODE.self::BIZ_TYPE_CHAI_YOU => [
                "1d7e1e11be017d9fe9d2717abe892837",
            ],
            self::PCODE_ZSY_COUPON.self::BIZ_TYPE_OIL => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "cce3b82b4073484bf734e8c6c1689a0f",
            ],
            self::PCODE_ZSY_COUPON.self::BIZ_TYPE_QI_YOU => [
                "324c232e211f695872bb1e779e5703c3"
            ],
            self::P_CODE_ZSY_COUPON_HU_BEI.self::BIZ_TYPE_OIL => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "cce3b82b4073484bf734e8c6c1689a0f",
            ],
            self::P_CODE_ZSY_COUPON_HU_BEI.self::BIZ_TYPE_QI_YOU => [
                "324c232e211f695872bb1e779e5703c3",
            ],
            self::P_CODE_ZSY_COUPON_HU_BEI.self::BIZ_TYPE_CHAI_YOU => [
                "1d7e1e11be017d9fe9d2717abe892837",
                "cce3b82b4073484bf734e8c6c1689a0f",
            ],
            CommonDefine::SH_TEST_SUPPLIER_CODE.self::BIZ_TYPE_CHAI_YOU => [
                "1d7e1e11be017d9fe9d2717abe892837",
            ],
        ];
    }
}