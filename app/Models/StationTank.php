<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationTank extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_tank';

    protected $guarded = ["id"];

    protected $fillable = ['id','name',"station_id","capacity","purch_way","oil_name","oil_type","oil_level"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

}