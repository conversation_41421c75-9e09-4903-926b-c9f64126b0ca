<?php

namespace Tests\Unit;

use App\Services\CardService;
use App\Services\CommonService;
use App\Services\NewOrderService;
use App\Services\PaymentService;
use App\Services\TruckCardService;
use Tests\TestCase;

class CardServiceTest extends TestCase
{

//    public function testCheckCardAndGetArray()
//    {
//        $obj = new CardService();
//
//        $obj->checkCardAndGetArray(8211071213934392, 0, 0, 0, true);
//    }

//    public function testCheckTruckCardTake()
//    {
//        $obj = app(TruckCardService::class);
//
//        $mobile = '15010687938';
//        $viceNo = 8211071213934392;
//
//        $obj->checkTruckCardTake($mobile, $viceNo);
//    }

//    public function testFinishOrder()
//    {
//        $params = [
//            'card_no' => 8211071213934392
//        ];
//
//        app(PaymentService::class)->finishOrder($params);
//
//    }

        public function testOrderTimeOut()
    {
        CommonService::isWhiteUser(12);

        $params = [
            'order_id' =>2022021812071410029
        ];
        app(NewOrderService::class)->orderTimeOut($params);
    }
}
