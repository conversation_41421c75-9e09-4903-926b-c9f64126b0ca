<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/21
 * Time: 20:24
 */
namespace App\Library;
use App\Exceptions\ParamInvalidException;
use Request as pRequest;

class Request extends pRequest {

    public  static function all()
    {
        if(pRequest::isJson()) {
            $json = pRequest::getContent();
            $paramData = json_decode($json, true);
            if (!empty(json_last_error())) {
                throw new ParamInvalidException('参数类型错误', 2);
            }
            return $paramData;
        }else {
            return pRequest::all();
        }
    }

    public  static function only($param)
    {
        if(pRequest::isJson()) {
            $json = pRequest::getContent();
            $filterParam =  json_decode($json, true);
            if(json_last_error()) {
                throw new ParamInvalidException('参数格式错误');
            }
        }else {
            $filterParam =  pRequest::only($param);
        }
        return array_intersect_key($filterParam,  array_flip($param));
    }

    public static function httpHeaders()
    {
        return pRequest::header();
    }
}