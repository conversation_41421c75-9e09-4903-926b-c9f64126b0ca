<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\OrgStationModel;
use App\Models\StationLimitConfigModel;
use App\Models\StationModel;
use App\Models\StationOrgModel;
use App\Models\StationOrgRuleModel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class StationLimitConfigRepository extends EloquentRepository
{
    protected $limitConfig;
    protected $stationModel;
    protected $stationOrgRuleModel;

    public function __construct(
        StationLimitConfigModel          $limitModel,
        StationModel $sModel,
        StationOrgRuleModel $stationOrgRuleModel
    )
    {
        $this->limitConfig = $limitModel;
        $this->stationModel = $sModel;
        $this->stationOrgRuleModel = $stationOrgRuleModel;
    }

    const CONFIG_TYPE_INSTITUTION_SPECIFIC_STATION = 1;
    const CONFIG_TYPE_LOW_YIELD_STATION_SHIELDING_AGENCY = 2;
    const CONFIG_TYPE_DESCRY = [
        self::CONFIG_TYPE_INSTITUTION_SPECIFIC_STATION         => '机构专属油站配置',
        self::CONFIG_TYPE_LOW_YIELD_STATION_SHIELDING_AGENCY   => '低收益油站屏蔽机构配置',
    ];


    public function getBatchLimitStation($orgCode = "")
    {
        if(empty($orgCode)){
            return [];
        }
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '开始读取gos_station_limit_config',
            ]);
        }
        $data = $this->limitConfig->get();
        if (count($data) == 0){
            return [];
        }
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '结束读取gos_station_limit_config',
            ]);
        }
        $activeOrgCode = self::getActiveOrgcode($orgCode);
        $institutionSpecificStation = [];
        $lowYieldStationShieldingAgency = [];
        foreach ($data as $v) {

            $configValue = json_decode($v->config_value, true) ?: [];
            switch ($v->config_type) {
                case self::CONFIG_TYPE_INSTITUTION_SPECIFIC_STATION:
                    foreach ($configValue as $k => $cv) {
                        if (!in_array($activeOrgCode, $cv) || empty($activeOrgCode)) {

                            $institutionSpecificStation[] = $k;
                        }
                    }
                    break;
                case self::CONFIG_TYPE_LOW_YIELD_STATION_SHIELDING_AGENCY:

                    foreach ($configValue as $k => $cv) {
                        if ($activeOrgCode && in_array($activeOrgCode, $cv)) {

                            $lowYieldStationShieldingAgency[] = $k;
                        }
                    }
                    break;
            }
        }
        $limitStationCode = array_unique(array_merge($institutionSpecificStation,
            $lowYieldStationShieldingAgency));
        $limitStationIds = [];
        if (!empty($limitStationCode)) {
            $limitStationIds = $this->stationModel->getOneField([
                'station_code' => $limitStationCode
            ], 'id', 'station_code')->toArray();
        }
        return $limitStationIds;
    }


    /**
     * @Notes:根据机构编码，向上级拆分成多级机构编码
     * @Interface splitOrgCode
     * @param $orgcode
     * @return array
     * @author: yuanzhi
     * @Time: 2023/12/27   2:54 PM
     */
    public function splitOrgCode($orgcode)
    {
        $len       = strlen($orgcode);
        $start     = 6;
        $orgRoot[] = substr($orgcode, 0, $start);
        $splitArr = [];
        if ($len > 6) {
            $arr = str_split(substr($orgcode, -($len - 6)), 2);
            if ($arr) {
                foreach ($arr as $v) {
                    $start       += 2;
                    $splitArr[] = substr($orgcode, 0, $start);
                }
            }
        }

        return array_merge($orgRoot, $splitArr);
    }

    public function getActiveOrgcode($orgCode)
    {
        $orgCodeTree = self::splitOrgCode($orgCode);
        return self::getActiveOrgcodeByOrgTree($orgCodeTree);
    }

    /**
     * @Notes:查询当前生效的规则
     * @Interface getRuleByOrgTree
     * @param $orgTree
     * @return array
     * @author: yuanzhi
     * @Time: 2023/12/27   3:08 PM
     */
    public function getActiveOrgcodeByOrgTree($orgTree)
    {
        return $this->stationOrgRuleModel->getActiveOrgCode(['orgcode_list'=>$orgTree]);
    }

    public function getInstitutionSpecificStation($orgCode, $stationId): Collection
    {
        //如果查询到油站配置规则且包含机构编码，则返回1(可用)，否则返回2(不可用)
        return (new OrgStationModel())->getData([
            "station_id" => $stationId,
        ], [
            DB::raw("group_concat(distinct if(orgcode = '$orgCode', 1, 2)) as available")
        ]);
    }

    public function getLowYieldStation($orgCode, $stationId): Collection
    {
        //仅查询黑名单配置
        return (new StationOrgModel())->getData([
            "orgcode"    => $orgCode,
            "station_id" => $stationId,
            "list_type"  => 2,
        ], ["id"]);
    }
}