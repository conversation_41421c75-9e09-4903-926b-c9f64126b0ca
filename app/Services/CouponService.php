<?php


namespace App\Services;


use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Library\Helper\Common;
use App\Models\CouponsTypeModel;
use App\Models\CouponsModel;
use App\Models\OrderModel;
use App\Repositories\CouponRepository;
use App\Repositories\CouponTypeRepository;
use App\Repositories\GetCanUseCoupon\GetCanUseCoupon;
use App\Repositories\NewOrderRepository;
use App\Repositories\TripartiteCouponRepository;
use App\Servitization\Adapter;
use App\Servitization\FeiShu;
use App\Servitization\Foss;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Servitization\FossStation;

class CouponService
{
    protected $newOrderRepository;
    protected $adapter;
    protected $feishu;
    //protected $refund;
    protected $couponTypeRepository;
    protected $couponRepository;
    protected $fossStation;
    protected $tripartiteCouponRepository;
    protected $getCanUseCoupon;

    public function __construct
    (
        NewOrderRepository $newOrderRepository,
        Adapter $adapter,
        FeiShu $feiShu,
        //RefundService $refund,
        CouponTypeRepository $couponTypeRepository,
        CouponRepository $couponRepository,
        FossStation $fossStation,
        TripartiteCouponRepository $tripartiteCouponRepository,
        GetCanUseCoupon $getCanUseCoupon
    )
    {
        $this->newOrderRepository = $newOrderRepository;
        $this->adapter = $adapter;
        $this->feishu = $feiShu;
        //$this->refund = $refund;
        $this->couponTypeRepository = $couponTypeRepository;
        $this->couponRepository = $couponRepository;
        $this->fossStation = $fossStation;
        $this->tripartiteCouponRepository = $tripartiteCouponRepository;
        $this->getCanUseCoupon = $getCanUseCoupon;
    }

    /**
     * 获取电子券类别
     * @param $params
     * @return array
     */
    public function getCouponType($params = [])
    {
        $station = (new StationService())->getStationInfo($params);
        if ( !$station ) {
            throw new \RuntimeException("",CommonError::STATION_OFF);
        }
        if ( !in_array($station->pcode,CouponDefine::couponPcode()) ) {
            throw new \RuntimeException("该站点不支持电子券", 500051);
        }

        $condition = [
            'pcode'             => $station->pcode,
            'sell_status'       => CouponDefine::SELL_ON,
            'show_status'       => CouponDefine::SHOW,
            'gt_un_used_stock'  => 0
        ];

        $data = $this->couponTypeRepository->getList($condition);
        foreach ($data as $k => &$v) {
            if (!empty($params['oil_name']) and !in_array(
                    $params['oil_name'],
                    CouponDefine::getCouponToGoodsMapping()[$v['pcode'] . $v['goods_code']] ?? []
                )) {
                unset($data[$k]);
                continue;
            }
            $v['face_value'] = intval($v['face_value']);
            $v['unit'] = '元';
        }
        $data = array_values($data);
        return $data;
    }

    /**
     * 验证电子券类别是否存在
     * @param $id
     * @return CouponsTypeModel | bool
     */
    protected function checkCouponTypeExist($id)
    {
        $detail = $this->couponTypeRepository->getById($id);

        if (empty($detail))
            return false;

        return $detail;
    }

    /**
     * 获取可用的券
     *
     * @param $coupon_type_id int
     * @return CouponsModel
     */
    public function getCanUseCoupon($supplier_code, $coupon_type_id = 0)
    {
        if (empty($coupon_type_id) || ($type = $this->checkCouponTypeExist($coupon_type_id)) === false) {

            throw new \RuntimeException(CommonError::$codeMsg[CommonError::COUPONS_NOT_EXISTS],
                CommonError::COUPONS_NOT_EXISTS);
        }

        if ($type->sell_status == CouponDefine::SELL_STOP || $type->show_status == CouponDefine::HIDE) {

            throw new \RuntimeException(CommonError::$codeMsg[CommonError::COUPONS_NOT_AVAILABLE_FOR_SALE],
                CommonError::COUPONS_NOT_AVAILABLE_FOR_SALE);
        }

        if ($type->un_used_stock < 1) {

            throw new \RuntimeException(CommonError::$codeMsg[CommonError::INSUFFICIENT_COUPON_INVENTORY],
                CommonError::INSUFFICIENT_COUPON_INVENTORY);
        }

        if (empty($coupon = $this->getCanUseCoupon->handle($coupon_type_id, $supplier_code))) {

            throw new \RuntimeException('', CommonError::COUPON_TYPE_INVALID);
        }

        return $coupon;
    }

    /**
     * 验证券是否存在
     * @param $coupon_flag
     * @return \App\Models\CouponsModel|bool
     */
    public function checkCouponExist($coupon_flag)
    {
        if (empty($coupon_flag))
            return false;

        $coupon = $this->couponRepository->getByVoucher($coupon_flag);

        if (empty($coupon))
            return false;

        return $coupon;
    }

    protected function canCouponStatusChange($source, $target)
    {
        switch ($source) {
            case CouponDefine::NO_USE:
                if ($target != CouponDefine::USING) {
                    return false;
                }
                break;
            case CouponDefine::USING:
                if (! in_array($target, [CouponDefine::NO_USE,CouponDefine::NO_CHARGE_OFF])) {
                    return false;
                }
                break;
            case CouponDefine::NO_CHARGE_OFF:
                if (! in_array($target, [CouponDefine::NO_USE,CouponDefine::CHARGE_OFFED])) {
                    return false;
                }
                break;
            case CouponDefine::CHARGE_OFFED:
                if ($target != CouponDefine::NO_CHARGE_OFF) {
                    return false;
                }
        }

        return true;
    }

    /**
     * 更改电子券数据
     * @param int $coupon_flag
     * @param $data
     *
     * $data 中字段列表
     *      status     G7券状态
     *
     *      distribute_batch_no
     *      tripartite_voucher
     *      distribute_time
     *      start_time
     *      end_time
     *      tripartite_img
     *      instructions
     *
     *      charge_off_time
     *
     *      tripartite_status
     *
     *      modifier
     *
     * @param $enableTransaction
     *
     * @return bool
     */
    public function updateCoupon($coupon_flag, $data=[], $enableTransaction=false)
    {
        if (empty($coupon_flag) || ($coupon = $this->checkCouponExist($coupon_flag)) === false)
            return true;

        if (isset($data['status'])) {
            if (! $this->canCouponStatusChange($coupon->status, $data['status'])) {
                // @todo 文案
                throw new \RuntimeException('券状态异常', CommonError::COUPON_TYPE_INVALID);
            }
        }

        try {
            if ($enableTransaction) {
                DB::connection('mysql_gas')->beginTransaction();
            }

            $this->couponRepository->updateByVoucher($coupon_flag, $data);

            if (isset($data['status'])) {
                if ($coupon->status == CouponDefine::NO_USE) {
                    $this->couponTypeRepository->updateCouponTypeStock($coupon->coupon_type_id, 1, true);
                } elseif ($data['status'] == CouponDefine::NO_USE) {
                    $this->couponTypeRepository->updateCouponTypeStock($coupon->coupon_type_id, 1, false);
                }
            }

            if ($enableTransaction) {
                DB::connection('mysql_gas')->commit();
            }
        } catch (\Exception $e) {
            if ($enableTransaction) {
                DB::connection('mysql_gas')->rollBack();
            }

            Common::log('error', 'coupon_update', ['err_msg' => $e->getMessage(), 'trace' => $e->getTrace()]);

            throw new \RuntimeException('', CommonError::SYSTEM_ERROR);
        }

        return true;
    }

    /**
     * @param array $condition
     * @param mixed $select
     * @param bool $withLock
     * @return mixed
     */
    public function getCouponInfo(array $condition, $select = ['*'], bool $withLock = false)
    {
        return $this->couponRepository->getInfo($condition, $select, $withLock);
    }


    /**
     * @param array $condition
     * @param string $pluck
     * @return mixed
     */
    public function getCouponsPluckByCondition(array  $condition,$pluck){
        return $this->couponRepository->getCouponsPluckByCondition($condition,$pluck);
    }

    /**
     * 接受核销结果
     */
    public function receiveHxResult($data = [])
    {
        $info = $this->getCouponInfo(['tripartite_voucher'=>$data['voucher']]);
        if( count($info) <= 0 ){
            throw new \RuntimeException('券标识不合法', 60004);
        }
        if( $info['tripartite_status'] == CouponDefine::CHARGE_OFFED ){
            return $data;
        }
        // 校验订单状态
        $order = $this->newOrderRepository->getOneOrderByParams(['order_id' => $info['order_id']], ['order_id', 'order_status'], false);
        if (empty($order)) {
            throw new \RuntimeException('订单不存在', 60006);
        }
        if ($order['order_status'] != OrderModel::SUCCESS_PAY) {
            throw new \RuntimeException('当前订单状态无法核销', 60007);
        }

        $upData['charge_off_time'] = array_get($data,"usedtime","");
        $upData['tripartite_status'] = CouponDefine::CHARGE_OFFED;
        $upData['status'] = CouponDefine::CHARGE_OFFED;
        try {
            DB::connection('mysql_gas')->beginTransaction();

            // 更新副卡交易流水核销状态
            $sync = (new Foss())->chargeOff($info['order_id']);
            if ($sync['affected']) {
                $upData['sync_foss'] = 1;
            }

            $this->updateCoupon($info['voucher'],$upData);
            $this->tripartiteCouponRepository->updateOrCreate([
                'tripartite_voucher' => $data['voucher'],
                'pcode' => $info['pcode'],
            ], [
                'tripartite_status' => CouponDefine::CHARGE_OFFED,
                'updated_time' => date('Y-m-d H:i:s'),
                'charge_off_time' => array_get($data,"usedtime", null)
            ]);
            DB::connection('mysql_gas')->commit();
        } catch (\Exception $e) {
            DB::connection('mysql_gas')->rollBack();

            Common::log('error', 'receiveHxResult', ['params' => $data, 'err_msg' => $e->getMessage(), 'trade' => $e->getTrace()]);

            throw new \RuntimeException('', CommonError::SYSTEM_ERROR);
        }

        return $data;
    }

    /**
     * 注销电子券
     * @param $supplierCode string
     * @param $couponFlag string
     * @param $updater string
     */
    public function cancelCoupon($supplierCode, $couponFlag, $updater)
    {
        if (empty($couponFlag))
            return;

        //lp-do河北中石油注销电子券
        try {
            // 如果是券运营商，但是退款申请已经提交给上游审核，这里无需再次注销电子券
            if (in_array($supplierCode, CouponDefine::couponPcode()) and
                !in_array($supplierCode, CommonDefine::linkedRefundApproveSupplier())) {
                $coupon=CouponsModel::getInfo(['voucher'=>$couponFlag]);
                if ($coupon) {
                    $this->fossStation->cancelCoupon($coupon['id'],$updater);
                }
            }
        }catch (Exception $e){
            Common::log('error', date('Y-m-d H:i:s').'注销电子券失败信息:'.$e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function cancelReceivePaymentResult($data = [])
    {
        try {
            DB::connection('mysql_gas')->beginTransaction();
            $info = $this->getCouponInfo(['tripartite_voucher' => $data['voucher']], ['*'], true);
            if (count($info) <= 0) {
                throw new \RuntimeException('券标识不合法', 60004);
            }
            if ($info['tripartite_status'] != CouponDefine::CHARGE_OFFED) {
                throw new \RuntimeException('券状态非已核销，无法取消核销', 60008);
            }
            $order = $this->newOrderRepository->getOrderInfoByLock(['order_id' => $info['order_id']],
                ['order_id', 'order_status'],
                false);
            if (empty($order)) {
                throw new \RuntimeException('订单不存在', 60006);
            }
            if ($order['order_status'] != OrderModel::SUCCESS_PAY) {
                throw new \RuntimeException('当前订单状态无法核销', 60007);
            }
            (new Foss())->cancelChargeOff($info['order_id']);
            $this->updateCoupon($info['voucher'], [
                'tripartite_status' => CouponDefine::CANCEL_CHARGE_OFF,
                'status'            => CouponDefine::NO_CHARGE_OFF,
                'updatetime'        => date('Y-m-d H:i:s'),
                'modifier'          => '系统(OA)',
            ]);
            $this->tripartiteCouponRepository->updateOrCreate([
                'tripartite_voucher' => $data['voucher'],
                'pcode'              => $info['pcode'],
            ], [
                'tripartite_status' => CouponDefine::CANCEL_CHARGE_OFF,
                'updated_time'      => date('Y-m-d H:i:s'),
            ]);
            DB::connection('mysql_gas')->commit();
        } catch (\Exception $e) {
            DB::connection('mysql_gas')->rollBack();
            throw $e;
        }
    }
}