<?php


namespace App\Models;


class TruckCardWechatPhonesModel extends BaseModel
{
    public $connection = 'mysql_gas';

    protected $table = 'gas_truck_card_wechat_phones';

    /**
     * 获取G7能源车辆账户最近的使用记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->orderBy('id', 'DESC')->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}