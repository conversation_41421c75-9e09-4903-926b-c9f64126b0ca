<?php
/**
 * Created by PhpStorm.
 * User: liying02
 * Date: 2021/09/22
 * Time: 15:53
 */
namespace App\Repositories;

use App\Models\CouponsTypeModel;
use Illuminate\Support\Facades\DB;

class CouponTypeRepository extends EloquentRepository
{
    public function __construct(CouponsTypeModel $stationModel)
    {
        $this->model = $stationModel;
    }

    /**
     * 获取类别列表
     * @param array $condition
     * @return mixed
     */
    public function getList(array $condition)
    {
        $fields = ['id', 'face_value', 'goods_code', 'pcode'];
        return $this->model->withCondition($condition)->orderBy('face_value', 'desc')->get($fields)->toArray();
    }

    /**
     * 根据id获取电子券类别
     * @param $id
     * @return CouponsTypeModel
     */
    public function getById($id)
    {
        if (empty($id))
            return null;

        return $this->model->where('id', $id)->first();
    }

    public function updateCouponTypeStock($coupon_type_id=0, $number=1, $consume=true)
    {
        if (!$number || empty($coupon_type_id))
            return true;

        if ($consume) {
            $data = [
                'un_used_stock' => DB::raw("un_used_stock - ".$number), // 减库存
                'used_stock'    => DB::raw("used_stock + ".$number)
            ];
        } else {
            $data = [
                'un_used_stock' => DB::raw("un_used_stock + ".$number), // 增库存
                'used_stock'    => DB::raw("used_stock - ".$number)
            ];
        }

        return $this->model->where('id', $coupon_type_id)->update($data);
    }
}