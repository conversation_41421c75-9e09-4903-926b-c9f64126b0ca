<?php
/**
 * Created by PhpStorm.
 * User: jesse
 * Date: 16/5/18
 * Time: 12:06
 */

namespace App\Repositories;


use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use RuntimeException;

abstract class EloquentRepository
{
    /**
     * @var   注入的model
     */
    protected $model;

    /**
     * 过滤参数
     * @return mixed
     */
    protected function initValidatorRules()
    {
    }

    /**
     * 关联model的scope where的方法
     * @return mixed
     */
    protected function initFilterField()
    {
    }

    /**
     * 管理model的scope的order by方法
     * @return mixed
     */
    protected function initOrderField()
    {
    }

    protected $rules = [
        'STORE'       => [],
        'UPDATE'      => [],
        'FILTERFIELD' => [],
    ];

    protected $filterField = [];
    protected $orderField = [];

    public function __construct()
    {
        $this->initValidatorRules();
        $this->initFilterField();
        $this->initOrderField();
    }

    protected function filter(array $fileds, Model $model = null)
    {
        if (empty($model)) {
            $model = $this->model;
        }

        $validator = Validator::make($fileds, $this->rules['FILTERFIELD']);

        if ($validator->passes()) {
            foreach ($fileds AS $filed => $value) {
                if (isset($value) && in_array($filed, $this->filterField)) {
                    $filed = camel_case($filed);
                    $model = $model->$filed($value);
                }
            }

            return $model;

        } else {

            $messages = $validator->messages();
            throw new ValidationException($messages);
        }
    }

    public function get(array $filter = [], array $fields = ['*'], array $orders = [], $offset = 0, $limit = 0)
    {
        $model = $this->filter($filter);
        $model = $this->order($orders, $model);
        if ($limit > 0) {
            $model->skip($offset);
            $model->take($limit);
        }
        $collection = $model->get($fields);

        return $collection;
    }

    public function getOne(array $filter, array $fields = ['*'], array $orders = [], $lock = false)
    {
        $model = $this->filter($filter);
        if ($orders) {
            $model = $this->order($orders, $model);
        }

        if ($lock == 'forUpdate') {
            $model->lockForUpdate();
        } elseif ($lock == 'shared') {
            $model->sharedLock();
        }

        return $model->first($fields);
    }

    public function storeModel(array $attributes, $model = null, $after = null)
    {
        $validator = Validator::make($attributes, $this->rules['STORE']);
        if ($after) {

            if (is_array($after)) {

                foreach ($after AS $afterOne) {
                    $validator->after($afterOne);
                }

            } else {
                $validator->after($after);
            }
        }

        if ($validator->passes()) {

            $model = $model ?: $this->model;

            return $model->create($attributes);

        } else {

            $messages = $validator->messages();
            throw new ValidationException($messages);
        }
    }

    protected function updateModel(array $attributes, Model $model, $after = null)
    {
        $validator = Validator::make($attributes, $this->rules['UPDATE']);
        if (is_array($after)) {
            foreach ($after AS $afterOne) {
                $validator->after($afterOne);
            }
        } elseif ($after) {
            $validator->after($after);
        }

        if ($validator->passes()) {

            foreach ($attributes AS $filed => $attribute) {
                if ($model->isFillable($filed)) {
                    $model->$filed = $attribute;
                }
            }
            $model->save();

            return $model;

        } else {
            $messages = $validator->messages();
            throw new ValidationException($messages);
        }
    }

    protected function order(array $orders, $model = null)
    {
        if (empty($model)) {
            $model = $this->model;
        }

        foreach ($orders AS $order) {
            if ($order && in_array($order, $this->orderField)) {
                $order = camel_case($order);
                $model = $model->$order();
            }
        }

        return $model;
    }

    /**
     * 事物 闭包
     * @param Closure $dbTransaction
     */
    public function transactionDB(Closure $dbTransaction)
    {
        return DB::transaction($dbTransaction);
    }

    /**
     * 数据库重连
     * @param null $connect
     * @return mixed
     */
    public function reconnect($connect = null)
    {
        return DB::reconnect($connect);
    }

    /**
     * 数据库断开
     * @param null $connect
     * @return mixed
     */
    public function deconnect($connect = null)
    {
        return DB::disconnect($connect);
    }

    public function debugstart($connect = null)
    {
        return DB::connection($connect)->enableQueryLog();
    }

    public function debugend($connect = null)
    {
        return DB::connection($connect)->getQueryLog();
    }

    /**
     * 验证数组字段,失败则抛出异常
     * @param array $data 需要检查的数据
     * @param array $checkData 检查规则及消息, 例：['rules' => ['xxx' => 'in'], 'messages' => ['xxx.in' => '']]
     * @param bool $throwException 检查不通过是否抛出异常
     * @return bool
     * @throws RuntimeException
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 5:37 下午
     */
    public static function checkArrayField(array $data, array $checkData, bool $throwException)
    {
        if (!empty($checkData) and isset($checkData['rules']) and isset($checkData['messages'])) {

            $validator = Validator::make($data, $checkData['rules'] ?? [],
                $checkData['messages'] ?? []);

            if ($validator->fails()) {

                if ($throwException) {

                    throw new RuntimeException("", $validator->errors()->first());
                }

                return false;
            }

            return true;
        }

        return true;
    }
}