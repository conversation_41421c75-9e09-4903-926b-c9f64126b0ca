<?php

/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Services;

use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Library\Helper\Common;
use App\Models\OrderModel;
use App\Repositories\HistoryPayRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrdersRepository;
use App\Repositories\SupplierRepositories;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class OrderService
{
    protected $orderRepository;
    protected $historyPayRepository;
    protected $supplierRepositories;
    protected $newOrderRepository;
    protected $adapter;

    public function __construct
    (
        OrdersRepository     $orderRepository,
        HistoryPayRepository $historyPayRepository,
        NewOrderRepository   $newOrderRepository,
        SupplierRepositories $supplierRepositories,
        Adapter              $adapter
    )
    {
        $this->orderRepository = $orderRepository;
        $this->historyPayRepository = $historyPayRepository;
        $this->newOrderRepository = $newOrderRepository;
        $this->supplierRepositories = $supplierRepositories;
        $this->adapter = $adapter;
    }

    //订单入库
    public function addOrder($params)
    {
        $isSuccess = false;
        //先起事务,保证先生成订单
        DB::beginTransaction();
        try {
            $info = $this->getInfoByUnique($params);
            if ($info) {
                throw new RuntimeException("该sn已存在,请勿重复使用");
            }
            $record = $this->insertOrder($params);
            if ($record && isset($record->order_id) && !empty($record->order_id)) {
                $isSuccess = true;
            }
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            Common::log("error", var_export($exception->getMessage(), true));
            throw new RuntimeException($exception->getMessage(), $exception->getCode() > 0 ? $exception->getCode() : 2);
        }
        $isDeduct = false;
        //扣费及生成加油流水
        if ($isSuccess) {
            DB::beginTransaction();
            try {
                //订单状态修改为支付中
                $this->orderRepository->updateOrder(['id' => $params['id']], ['pay_status' => 3, 'updatetime' => Common::nowTime()]);
                $deductParams['id'] = $params['id'];
                $deductParams['orgcode'] = $params['orgcode'];
                $deductParams['card_no'] = $params['card_no'];
                $deductParams['money'] = $params['money'];
                $deductParams['station_id'] = $params['station_id'];
                $deductRes = $this->deductAccountMoney($deductParams);
                if ($deductRes) {
                    $this->orderRepository->updateOrder(['id' => $params['id']], ['pay_status' => 1, 'updatetime' => Common::nowTime()]);
                    $this->createTrades($params);
                    $isDeduct = true;
                } else {
                    throw new RuntimeException('扣费失败', 2);
                }
                DB::commit();
            } catch (Exception $exception) {
                DB::rollBack();
                Common::log("error", var_export($exception->getMessage(), true));
                throw new RuntimeException($exception->getMessage(), $exception->getCode() > 0 ? $exception->getCode() : 2);
            }
        } else {
            throw new RuntimeException('订单生成失败', 2);
        }
        //推送操作、发送模板消息、打印小票
        if ($isDeduct) {
            //推送foss消费记录
            $this->tradeAfterAsync($params);
        }
        print_r($record->toArray());
        exit;
        //$this->deductAccountMoney($params);
    }

    //调用Foss扣费
    public function deductAccountMoney($params)
    {
        return true;
        return (new Foss())->deductMoney($params);
    }

    //生成Gas加油流水
    public function createTrades($params)
    {

    }

    //消费记录生成后的异步操作
    public function tradeAfterAsync($params)
    {
        (new Foss())->pushTrades($params);
    }

    //查询Sn
    public function getInfoByUnique($params)
    {
        return $this->orderRepository->getInfoByLock(['order_id' => $params['order_id'], 'orgcode' => $params['orgcode'], "pcode" => $params['pcode']]);
    }

    //订单入库
    public function insertOrder($insterData)
    {
        $insterData['pay_status'] = 1;
        Log::error("insertData:" . var_export($insterData, true));
        return $this->orderRepository->addData($insterData);
    }

    /**
     * 通过订单号获取交易流水编号
     *
     * @param $order_id
     * @return mixed
     */
    public function getHistoryIdByOrderId($order_id)
    {
        $select = ['order_id', 'history_id'];
        $result = $this->historyPayRepository->getHistoryPayInfoByParams(['order_id' => $order_id], $select, true);
        if (empty($result)) {
            return $order_id;
        }

        return $result['history_id'];
    }


    public function batchGetByOrderId(array $orderIds, $master = false)
    {
        if (empty($orderIds))
            return [];

        if (!is_array($orderIds)) {
            $orderIds = [$orderIds];
        }

        $fields = ['order_id', 'order_status', 'station_id', 'station_code'];

        $data = $this->orderRepository->getListByFilter(['order_id' => $orderIds], $fields, $master);
        foreach ($data as &$item) {
            $item['order_status_text'] = OrderModel::$ORDER_STATUS[$item['order_status']];
        }

        return $data;
    }
}