<?php


namespace App\Services;


use App\Http\Defines\CardTradeConf;
use App\Http\Defines\FossRuleDefine;
use App\Library\Helper\Common;
use App\Library\Helper\NumberUtil;
use App\Library\Request;
use App\Library\Upload\Upload;
use App\Models\GasHistoryApproveModel;
use App\Models\HistoryModel;
use App\Models\OilOrgModel;
use App\Models\OrderModel;
use App\Repositories\CardRepository;
use App\Repositories\CityRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\HistoryPayRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationGunRepository;
use App\Repositories\StationRepository;
use App\Repositories\SupplierRepositories;
use App\Servitization\Foss;
use App\Servitization\FossStation;

class HistoryService
{
    protected $historyRepository;
    protected $historyPayRepository;
    protected $stationRepository;
    protected $stationGunRepository;
    protected $supplierRepositories;
    protected $orgRepository;
    protected $dictRepository;
    protected $cityRepository;
    protected $cardRepository;
    protected $historyApproveRepository;
    protected $orderService;
    protected $stationService;
    protected $errorCodeService;
    protected $redisService;
    protected $fossStation;
    protected $foss;
    protected $oneCardConfig;
    protected $ossConfig;
    protected $numberUtil;
    protected $orderExt;

    public function __construct
    (
        HistoryRepository $historyRepository,
        HistoryPayRepository $historyPayRepository,
        StationRepository $stationRepository,
        StationGunRepository $stationGunRepository,
        SupplierRepositories $supplierRepositories,
        OrgRepository $orgRepository,
        DictRepository $dictRepository,
        CityRepository $cityRepository,
        CardRepository $cardRepository,
        HistoryApproveRepository $historyApproveRepository,
        OrderService $orderService,
        StationService $stationService,
        ErrorCodeService $errorCodeService,
        RedisService $redisService,
        FossStation $fossStation,
        Foss $foss,
        NumberUtil $numberUtil,
        NewOrderExtRepository $ext
    )
    {
        $this->historyRepository = $historyRepository;
        $this->historyPayRepository = $historyPayRepository;
        $this->stationRepository = $stationRepository;
        $this->stationGunRepository = $stationGunRepository;
        $this->supplierRepositories = $supplierRepositories;
        $this->historyApproveRepository = $historyApproveRepository;
        $this->dictRepository = $dictRepository;
        $this->cityRepository = $cityRepository;
        $this->orgRepository = $orgRepository;
        $this->cardRepository = $cardRepository;
        $this->orderService = $orderService;
        $this->stationService = $stationService;
        $this->errorCodeService = $errorCodeService;
        $this->redisService = $redisService;
        $this->fossStation = $fossStation;
        $this->foss = $foss;
        $this->oneCardConfig = config('oneCardConfig');
        $this->ossConfig = config('oss');
        $this->numberUtil = $numberUtil;
        $this->orderExt = $ext;
    }

    /**
     * 根据制定条件,获取交易流水
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getHistoryPaginate($params)
    {
        $page = empty($params['page']) ? 1 : $params['page'];
        $limit = empty($params['limit']) ? 10 : $params['limit'];

        $selectParams = [];

        $stationCode = [];
        // 渠道ID,站点编码
        if (!empty($params['channel_id']) && !empty($params['station_code'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
            $stationCode = array_intersect($channelStationCode, [$params['station_code']]);
            if (empty($stationCode)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
        } elseif (!empty($params['channel_id'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
            $stationCode = $channelStationCode;
        } elseif (!empty($params['station_code'])) {
            $stationCode = [$params['station_code']];
        }

        // 站点名称,站点编码
        if (!empty($params['station_id']) && !empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }

            $station_id_array = explode(',', $params['station_id']);
            $arrayIntersectStation = array_intersect($station_id_array, $stationIds);
            if (empty($arrayIntersectStation)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
            $selectParams['station_id'] = $arrayIntersectStation;
        } elseif (!empty($params['station_id'])) {
            $selectParams['station_id'] = explode(',', $params['station_id']);
        } elseif (!empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
            $selectParams['station_id'] = $stationIds;
        }
        // 交易流水ID
        if (!empty($params['business_log_id'])) {
            $selectParams['id'] = $params['business_log_id'];
        }
        // G7流水ID
        if (!empty($params['serial_num'])) {
            $selectParams['serial_num'] = $params['serial_num'];
        }
        // 三方站点ID
        if (!empty($params['stream_no'])) {
            $id = $this->orderService->getHistoryIdByOrderId($params['stream_no']);
            if (empty($id)) {
                return ['data' => [], 'count' => 0, 'page' => $page, 'limit' => $limit];
            }
            $selectParams['id'] = $id;
        }
        // 单据类型
        $selectParams['log_type'] = [
            HistoryModel::COST_SUCCESS,
            HistoryModel::COST_ABNORMAL,
            HistoryModel::COST_SUCCESS_ADD,
            HistoryModel::COST_INVALID,
            HistoryModel::COST_INVALID_2,
            HistoryModel::COST_RESERVATION_REFUEL,
            HistoryModel::COST_RESERVATION_REFUEL_REFUND,
        ];
        if (!empty($params['log_type'])) {
            $selectParams['log_type'] =  explode(',', $params['log_type']);
        }
        // 账号
        if (!empty($params['card_no'])) {
            $selectParams['card_no'] = $params['card_no'];
        }
        // 站点供应商
        if (!empty($params['pcode'])) {
            $selectParams['pcode'] = $params['pcode'];
        }
        // 司机机构(查看子机构)
        if (!empty($params['org_code']) && !empty($params['need_relevant_org_order'])) {
            $selectParams['left_gascode'] = $params['org_code'];
        } else if (!empty($params['org_code'])) {
            $selectParams['gascode'] = $params['org_code'];
        }
        // 司机姓名
        if (!empty($params['driver_name'])) {
            $selectParams['like_driver_name'] = $params['driver_name'];
        }
        // 司机手机号
        if (!empty($params['driver_phone'])) {
            $selectParams['left_driver_phone'] = $params['driver_phone'];
        }
        // 司机车牌号
        if (!empty($params['truck_no'])) {
            $selectParams['like_truck_no'] = $params['truck_no'];
        }
        // 所属省市
        if (!empty($params['province_code'])) {
            $selectParams['provice_code'] = $params['province_code'];
        }
        if (!empty($params['city_code'])) {
            $selectParams['city_code'] = $params['city_code'];
        }
        // 商品(name、type、level)
        if (!empty($params['oil_type'])) {
            $selectParams['oil_type'] = $params['oil_type'];
        }
        if (!empty($params['oil_name'])) {
            $selectParams['oil_name'] = $params['oil_name'];
        }
        if (!empty($params['oil_level'])) {
            $selectParams['oil_level'] = $params['oil_level'];
        }

        // 创建时间
        if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
            if (strtotime($params['le_create_time']) - strtotime($params['ge_create_time']) > 180*86400) {
                throw new \Exception('无法查询出该时间段的结果!');
            }
            $selectParams['ge_createtime'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
            $selectParams['le_createtime'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }
        // 创建时间不等于空，结束时间等于空
        if (!empty($params['ge_create_time']) && empty($params['le_create_time'])) {
            $params['le_create_time'] = date('Y-m-d 00:00:00',time());
            if (strtotime($params['le_create_time']) - strtotime($params['ge_create_time']) > 180*86400) {
                throw new \Exception('无法查询出该时间段的结果!');
            }
            $selectParams['ge_createtime'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
            $selectParams['le_createtime'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }
        // 创建时间等于空，结束时间不等于空
        if (empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
            // 不能超过180天
            $params['ge_create_time'] = date('Y-m-d 00:00:00',time()- 180*86400);
            $selectParams['ge_createtime'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
            $selectParams['le_createtime'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }
        // 加油时间
        $selectParams['ge_oil_time'] = date('Y-m-d 00:00:00', time() - 180*86400);
        $selectParams['le_oil_time'] = date('Y-m-d 23:59:59', time());
        if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
            // 不能超过180天
            if (strtotime($params['le_oil_time']) - strtotime($params['ge_oil_time']) > 180*86400) {
                throw new \Exception('无法查询出该时间段的结果!');
            }
            $selectParams['ge_oil_time'] = date('Y-m-d H:i:s', strtotime($params['ge_oil_time']));
            $selectParams['le_oil_time'] = date('Y-m-d H:i:s', strtotime($params['le_oil_time']));
        }

        // G7能源账户筛选条件
        $selectParams['support_type'] = 2;
        $selectParams['xpcode'] = '20003JCP';

        $result = $this->historyRepository->getHistoryPaginate($selectParams, $page, $limit);
        if (empty($result['data'])) {
            return $result;
        }

        // 订单流水信息
        $historyIdBatch = array_unique(array_column($result['data'], 'id'));
        $select = ['order_id', 'history_id'];
        $historyPayInfo = collect($this->historyPayRepository->getHistoryPayInfoByParamsBatch(['history_id' => $historyIdBatch], $select))->keyBy('history_id')->all();
        // 站点信息
        $stationIdBatch = array_unique(array_column($result['data'], 'station_id'));
        $select = [
            'id', 'station_name', 'pcode', 'isstop', 'card_classify', 'station_type', 'station_brand', 'trade_type',
            'rebate_grade', 'provice_code', 'city_code', 'station_code'
        ];
        $stationInfo = collect($this->stationRepository->getBatchStationInfoByParams(['id' => $stationIdBatch], $select))->keyBy('id')->all();
        // 机构信息
        $orgCodeBatch = array_column($result['data'], 'gascode');
        $select = ['orgcode', 'org_name'];
        $orgInfo = collect($this->orgRepository->getBatchOrgByParams(['orgcode' => $orgCodeBatch], $select))->keyBy('orgcode')->all();
        // 油枪信息
        $gunIdBatch = array_column($result['data'], 'gun_id');
        $select = ['id', 'name'];
        $gunInfo = $this->stationGunRepository->getBatchGunByParams(['id' => $gunIdBatch], $select, false)->keyBy('id')->all();
        // 油品数据字典
        $oilDict = $this->dictRepository->getOilDict();
        // 省、市信息
        $provinceCityDict = $this->cityRepository->getProvinceCityDict(false)->keyBy('city_code')->toArray();
        // 供应商
        $supplierInfo = $this->supplierRepositories->getOneCardSupplier(['scode', 'supplier_name'], false)->keyBy('scode')->all();

        $data = [];
        foreach ($result['data'] as $key => $item) {
            $value['id'] = $item['id'];
            $value['serial_num'] = $item['serial_num'];
            $value['log_type'] = HistoryModel::$LOG_TYPE_ENUM[$item['log_type']]['msg'] ?? '--';
            $value['station_name'] = $stationInfo[$item['station_id']]['station_name'] ?? '--';
            $value['station_code'] = $stationInfo[$item['station_id']]['station_code'] ?? '--';
            $value['supplier_name'] = $supplierInfo[$item['pcode']]['supplier_name'] ?? '--';
            $value['goods'] = array_get($oilDict, 'oil_type.' . $item['oil_type'], '') . array_get($oilDict, 'oil_name.' . $item['oil_name'], '') . array_get($oilDict, 'oil_level.' . $item['oil_level'], '');
            $value['money'] = $item['xpcode_pay_money']; //站点应收金额
            $value['pay_money'] = $item['data_type'] == '13' ? $item['money'] : $item['pay_money']; //客户应收金额
            $value['price'] = $item['xpcode_pay_price']; //站点应收单价
            $value['pay_price'] = $item['pay_price']; //客户应付单价
            $value['oil_num'] = $item['oil_num'];
            $value['card_no'] = $item['card_no'];
            $value['gascode_name'] = $orgInfo[$item['gascode']]['org_name'] ?? '--'; //司机机构
            $value['driver_name'] = $item['driver_name'] ?: '--';
            $value['driver_phone'] = $item['driver_phone'] ? Common::secretString($item['driver_phone'], 3, 4) : '--';
            $value['truck_no'] = $item['truck_no'] ?: '--';
            $imgUrl = json_decode($item['imgData'], true);
            $value['ticket_url'] = !empty($imgUrl['carnumUrl']) ? Upload::getSignUrl( $imgUrl['carnumUrl']) : '--'; //电子票
            $value['province_name'] = $provinceCityDict[$item['provice_code']]['city_name'] ?? '--';
            $value['city_name'] = $provinceCityDict[$item['city_code']]['city_name'] ?? '--';
            $value['gun_name'] = $gunInfo[$item['gun_id']]['name'] ?? '--';
            $dataType = $item['data_type'] == '35' ? '31' : ($item['data_type'] == '41' ? '4' : $item['data_type']);
            $value['data_type'] = HistoryModel::$dataTypeList[$dataType] ?? '';
            $value['stream_no'] = !empty($historyPayInfo[$item['id']]['order_id']) ? $historyPayInfo[$item['id']]['order_id'] : (isset($item['stream_no']) ? $item['stream_no'] : '--'); //第三方订单号
            $value['truename'] = $item['truename'] ?: '--'; //操作人
            $value['createtime'] = $item['createtime']; //创建时间
            $value['oil_time'] = $item['oil_time']; //交易时间
            $value['is_special_refund_pcode'] = in_array($item['pcode'], explode(',', env('PCODE_OA_CHECK_BEFORE_REFUND'))) ? 1 : 0;
            //收款成功与失败
            $value['pay_result'] = in_array($item['log_type'],[HistoryModel::COST_SUCCESS, HistoryModel::COST_ABNORMAL, HistoryModel::COST_SUCCESS_ADD, HistoryModel::COST_RESERVATION_REFUEL]) ? '收款成功' : "已退款";
            $value['log_type_num'] = $item['log_type'];
            $value['old_id'] = (string)$item['old_id'];
            $data[$key] = $value;
        }
        $result['data'] = $data;
        return $result;
    }

    /**
     * 获取交易流失日期最大范围
     * @return int
     */
    public function getMaxDay()
    {
        $maxDay = 90; //天

        try{
            //请求foss配置
            $data = $this->foss->getStationTimeLimit([]);
            if($data['time_limit'])
            {
                $maxDay = $data['time_limit'];
            }
        }catch (\Exception $e){
            Common::log('info','getMaxDay获取交易流失日期最大范围值失败：'.$e->getMessage(),[]);
        }

        return $maxDay;
    }

    /**
     * 支付成功后生成交易流水
     *
     * @param $params
     * @param $logType
     * @return string
     * @throws \Exception
     */
    public function addHistoryAfterPay($params, $logType)
    {
        $mirror = json_decode(array_get($params, 'mirror', "{}"), true);
        if (empty($mirror)) {
            throw new \Exception('参数异常:请按照格式传参!', $this->errorCodeService->gotoPay(40301));
        }

        /**
         * G7能源账户信息补充
         */
        $gasCardInfo = $this->cardRepository->getOneCardByParams(['card_no' => $params['card_no']], ['card_id','orgcode']);
        if (empty($gasCardInfo)) {
            $tmpCard = $this->cardRepository->getCardInfo(['vice_no' => strval($params['card_no'])]);
            if( empty($tmpCard) ){
                throw new \Exception('参数异常:G7能源账号编号错误!', $this->errorCodeService->gotoPay(40302));
            }
            $card_id = $tmpCard->id;
            $org = array_get($tmpCard,"org.orgcode","");
        }else{
            $card_id = $gasCardInfo['card_id'];
            $org = $gasCardInfo['orgcode'];
        }

        $serialNum = array_get($params,'trade_no','');
        if(empty($serialNum)){
            $serialNum = $this->redisService->makeSerialNum();
        }
        $id = $params['order_id'];
        $addHistory = [
            'id' => $id,
            'serial_num' => substr($serialNum, 0, 11),
            'pcode' => $params['supplier_code'],
            'xpcode' => '20003JCP',
            'scode' => '',// 空
            'gascode' => $params['org_code'],
            'card_id' => $card_id,
            'card_no' => $params['card_no'],
            'unit' => $params['oil_unit'],// gas_card.unit => GMS加油方式是在油站控制的,这里取真实加油方式
            'log_type' => $logType,
            'sale_type' => 11,// 11现金消费 12透支消费 13透支服务费 14现金服务费 (怎么存的都是11呢) ？
            'xpcode_sale_type' => 11,// 11非透支
            #'data_type' => '9'.$params['order_channel'], // 订单来源(新建)
            'data_type' => 11,// 11 微信 12 安卓 2 补录 31 smart主动上报 32 插卡加油
            'truck_no' => $params['truck_no'],// 车牌号
            'truename' => $params['creator'] ?? Request::get('user_name'),// 加油员姓名
            'creator' => $params['creator'] ?? Request::get('user_name'),
            'orgcode' => $org,// gas_card.orgcode
            'money' => $params['oil_money'],// 销价*数量
            'pay_money' => $params['oil_money'],//销价*数量
            'xpcode_pay_money' => $params['supplier_money'] ?? round(
                    bcmul($mirror['supplier_price'], $params['real_oil_num'], 3),
                    2
                ),//进价*数量
            //'balance' => 0.00, // 自营账户余额(gas维护，建议GMS不维护) ? gas必填字段
            //'xpcode_balance' => 0.00, // 非自营账户余额(foss维护，建议GMS不维护)
            'oil_type' => $params['oil_type'],// 油品类型ID
            'oil_name' => $params['oil_name'],// 油品名称ID
            'oil_level' => $params['oil_level'],// 油品等级ID
            'oil_num' => $params['oil_num'],// 加油升数
            'provice_code' => $params['province_code'],
            'city_code' => $params['city_code'],
            'driver_name' => $params['driver_name'],
            'driver_phone' => $params['driver_phone'],
            'price_id' => $mirror['price_id'],// 自营站定价ID
            'xpcode_price_id' => 'custom',// 三方站点定价,自营站PDA扫码均为custom
            'price' => $params['oil_price'],
            'pay_price' => $params['oil_price'],
            //'buy_price' => '', // 采购价 无卡模式会用到 ？待确认
            'xpcode_pay_price' => $mirror['supplier_price'],// 进价
            'support_type' => 2,// 加油类型 2非自营
            //'return_price' => '', // 返现单价
            //'use_price' => '', // 平台佣金单价
            'service_price' => $params['service_price'],// 服务费
            //'xpcode_service_price' => '',// 运营商服务费
            'station_id' => $params['station_id'],
            'tank_id' => $mirror['tank_id'],
            'gun_id' => $mirror['gun_id'],
            'oil_time' => $params['pay_time'],// 待产品确认
            'createtime' => date('Y-m-d H:i:s'),
            'updatetime' => date('Y-m-d H:i:s'),
            'imgurl' => '',// 加油员签名图片
            'imgData' => $mirror['image'] ?? '',// 补录时有图片
            'old_id' => $params['old_id'] ?? '',// 补录时有历史交易流水单号
            'ifreturn' => 0,// 是否返利 0未返利 1已返利
            'remark' => $params['remark'] ?? '',
            'is_pay' => 22,// 支付类型 22
            'ispay' => 1,// 是否扣费 1扣费 0不扣费
            //            'stream_no' => empty($params['third_order_id']) ? $id : $params['third_order_id'] , // 三方订单号 or 交易流水号
            'stream_no' => md5(Common::uuid()),// 保证唯一
            //'last_stock' => '',// 实时库存，G7能源账户不维护
            'list_price' => empty($params['list_price']) ? 0.00 : $params['list_price'],
            'oil_starttime' => $params['pay_time'],
            'mileage' => 0.00,// 总里程
            'card_classify' => 2,// 卡类型 2引流卡
            'payment_no' => $params['payment_id'],
            'pay_tag' => '',
        ];

        // G7WALLET-442
        if (in_array($logType, [HistoryModel::COST_SUCCESS_ADD, HistoryModel::COST_ABNORMAL]) && !empty($params['oil_time'])) {
            $addHistory['oil_time'] = $params['oil_time'];
        }

        $result = $this->historyRepository->insertHistory($addHistory);
        if (empty($result)) {
            throw new \Exception('添加交易流水失败,请重试!', $this->errorCodeService->gotoPay(40403));
        }

        return $id;
    }

    /**
     * 交易流水撤销作废
     *
     * @param $historyId
     * @param int $logType
     * @return string
     * @throws \Exception
     */
    public function updateHistoryAddRefund($historyId, int $logType = HistoryModel::COST_INVALID)
    {
        $originalHistoryInfo = $this->historyRepository->getOneHistoryByParams(['id' => $historyId]);
        $id = md5(Common::uuid());
        $update = [
            'log_type'   => HistoryModel::COST_ORIGINAL,
            'old_id'     => $historyId,
            'updatetime' => date('Y-m-d H:i:s'),
        ];

        $insert = [
            'id'               => $id,
            'serial_num'       => substr($this->redisService->makeSerialNum(), 0, 11),
            'pcode'            => $originalHistoryInfo['pcode'],
            'xpcode'           => $originalHistoryInfo['xpcode'],
            'scode'            => $originalHistoryInfo['scode'],
            'gascode'          => $originalHistoryInfo['gascode'],
            'card_id'          => $originalHistoryInfo['card_id'],
            'card_no'          => $originalHistoryInfo['card_no'],
            'unit'             => $originalHistoryInfo['unit'],
            'log_type'         => $logType,
            'sale_type'        => $originalHistoryInfo['sale_type'],
            'xpcode_sale_type' => $originalHistoryInfo['xpcode_sale_type'],
            'data_type'        => $originalHistoryInfo['data_type'],
            'truck_no'         => $originalHistoryInfo['truck_no'],
            'truename'         => Request::get('user_name'),
            'creator'          => Request::get('user_name'),
            'orgcode'          => $originalHistoryInfo['orgcode'],
            'money'            => $originalHistoryInfo['money'],
            'pay_money'        => $originalHistoryInfo['pay_money'],
            'xpcode_pay_money' => $originalHistoryInfo['xpcode_pay_money'],
            //'balance' => '', // 自营账户余额
            //'xpcode_balance' => '', // 非自营账户余额
            'oil_type' => $originalHistoryInfo['oil_type'],
            'oil_name' => $originalHistoryInfo['oil_name'],
            'oil_level' => $originalHistoryInfo['oil_level'],
            'oil_num' => $originalHistoryInfo['oil_num'],
            'provice_code' => $originalHistoryInfo['provice_code'],
            'city_code' => $originalHistoryInfo['city_code'],
            'driver_name' => $originalHistoryInfo['driver_name'],
            'driver_phone' => $originalHistoryInfo['driver_phone'],
            'price_id' => $originalHistoryInfo['price_id'],
            'xpcode_price_id' => $originalHistoryInfo['xpcode_price_id'],
            'price' => $originalHistoryInfo['price'],
            'pay_price' => $originalHistoryInfo['pay_price'],
            'buy_price' => $originalHistoryInfo['buy_price'],
            'xpcode_pay_price' => $originalHistoryInfo['xpcode_pay_price'],
            'support_type' => $originalHistoryInfo['support_type'],
            'return_price' => $originalHistoryInfo['return_price'],
            'use_price' => $originalHistoryInfo['use_price'],
            'service_price' => $originalHistoryInfo['service_price'],
            'xpcode_service_price' => $originalHistoryInfo['xpcode_service_price'],
            'station_id' => $originalHistoryInfo['station_id'],
            'tank_id' => $originalHistoryInfo['tank_id'],
            'gun_id' => $originalHistoryInfo['gun_id'],
            'oil_time' => $originalHistoryInfo['oil_time'],
            'createtime' => date('Y-m-d H:i:s'),
            'updatetime' => date('Y-m-d H:i:s'),
            'imgurl' => $originalHistoryInfo['imgurl'],
            'imgData' => $originalHistoryInfo['imgData'],
            'old_id' => $historyId,
            'ifreturn' => $originalHistoryInfo['ifreturn'],
            'remark' => $originalHistoryInfo['remark'],
            'is_pay' => $originalHistoryInfo['is_pay'],
            'ispay' => $originalHistoryInfo['ispay'],
            'stream_no' => $id,
            //'last_stock' => '',// 实时库存，G7能源账户不维护
            'list_price' => $originalHistoryInfo['list_price'],
            'oil_starttime' => $originalHistoryInfo['oil_starttime'],
            'mileage' => $originalHistoryInfo['mileage'], // 总里程
            'card_classify' => $originalHistoryInfo['card_classify'],
            'payment_no' => $originalHistoryInfo['payment_no'],
            'pay_tag' => $originalHistoryInfo['pay_tag'],
        ];
        $result = $this->historyRepository->updateHistory(['id' => $historyId], $update);
        if (empty($result)) {
            throw new \Exception('更新交易流水(修改作废前原始数据)失败,请重试!');
        }
        $result = $this->historyRepository->insertHistory($insert);
        if (empty($result)) {
            throw new \Exception('创建交易流水(撤销作废)失败,请重试!');
        }

        return $id;
    }

    /**
     * 手机管站交易流水列表
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getMtHistoryPaginate($params)
    {

        $whereParams = [];
        // 站点ID
        if (!empty($params['station_id'])) {
            $whereParams['station_id'] = $params['station_id'];
        }

        // 交易流水号
        if (!empty($params['history_id'])) {
            $whereParams['id'] = $params['history_id'];
        }
        // 不能超过180天
        if (strtotime($params['le_oil_time']) - strtotime($params['ge_oil_time']) > 180 * 86400) {
            throw new \Exception('最多可查180天加油记录!');
        }
        $whereParams['ge_oil_time'] = date('Y-m-d 00:00:00', strtotime($params['ge_oil_time']));
        $whereParams['le_oil_time'] = date('Y-m-d 23:59:59', strtotime($params['le_oil_time']));

        // 交易流水状态 1扣款成功 2退款中 3已退款
        $getRefundingHistory = true;
        if (!empty($params['history_status'])) {
            switch ($params['history_status']) {
                case 1:
                    $whereParams['log_type'] = [HistoryModel::COST_SUCCESS, HistoryModel::COST_ABNORMAL, HistoryModel::COST_SUCCESS_ADD];
                    $getRefundingHistory = false;
                    break;
                case 2:
                    $approveInfo = $this->historyApproveRepository->getApprovePaginate([
                        'station_id' => $params['station_id'],
                        'ge_create_time' => date('Y-m-d H:i:s', strtotime($params['ge_oil_time'])),
                        'le_create_time' => date('Y-m-d H:i:s', strtotime($params['le_oil_time'])),
                        'approve_status' => [GasHistoryApproveModel::WAIT_APPROVE, GasHistoryApproveModel::FAIL_AFTER_APPROVE],
                        'history_type' => GasHistoryApproveModel::CANCEL_HISTORY,
                        'refund_system' => 1,
                    ], ['id', 'original_history_id'], $params['page'], $params['limit'], true);
                    if (empty($approveInfo['data'])) {
                        return ['data' => [], 'count' => 0, 'total_pay_money' => "0.00", 'page' => $params['page'], 'limit' => $params['limit']];
                    }
                    $whereParams['id'] = collect($approveInfo['data'])->pluck('original_history_id')->all();
                    break;
                case 3:
                    $whereParams['log_type'] = [HistoryModel::COST_INVALID, HistoryModel::COST_INVALID_2, HistoryModel::COST_RESERVATION_REFUEL_REFUND];
                    $getRefundingHistory = false;
                    break;
            }
        } else {
            $whereParams['log_type'] = [
                HistoryModel::COST_SUCCESS,
                HistoryModel::COST_ABNORMAL,
                HistoryModel::COST_SUCCESS_ADD,
                HistoryModel::COST_INVALID,
                HistoryModel::COST_INVALID_2,
                HistoryModel::COST_RESERVATION_REFUEL,
                HistoryModel::COST_RESERVATION_REFUEL_REFUND,

            ];
        }

        // 查询交易流水
        $whereParams['support_type'] = 2;
        $whereParams['xpcode'] = '20003JCP';

        $historyInfo = $this->historyRepository->getHistoryPaginate($whereParams, $params['page'], $params['limit']);
        if (empty($historyInfo['data'])) {
            return ['data' => [], 'count' => 0, 'total_pay_money' => "0.00", 'page' => $params['page'], 'limit' => $params['limit']];
        }
        // 通过historyApprove表查询待退款状态(查看全部时,包含退款)
        $originalHistoryId = [];
        if (($getRefundingHistory == true) && empty($params['history_status'])) {
            $originalHistoryId = collect($this->historyApproveRepository->getBatchApproveInfoByParams([
                'original_history_id' => collect($historyInfo['data'])->pluck('id')->all(),
                'approve_status' => [GasHistoryApproveModel::WAIT_APPROVE, GasHistoryApproveModel::FAIL_AFTER_APPROVE],
                'history_type' => GasHistoryApproveModel::CANCEL_HISTORY,
                'refund_system' => 1,
            ], ['original_history_id','approve_status'], true))->pluck('original_history_id')->all();
        }

        // 油品数据字典
        $oilDict = $this->dictRepository->getOilDict();
        // 机构信息
        $orgCodeBatch = array_column($historyInfo['data'], 'gascode');
        $select = ['orgcode', 'org_name'];
        $orgInfo = collect($this->orgRepository->getBatchOrgByParams(['orgcode' => $orgCodeBatch], $select))->keyBy('orgcode')->all();
        $needArray = [];
        $orderIds = [];
        // 由于异常修改生成的新流水的old_id存入的是原订单号，故这里需要兼容
        foreach ($historyInfo['data'] as $v) {
            if ($v['log_type'] == HistoryModel::COST_ABNORMAL) {
                $orderIds[] = $v['id'];
                continue;
            }
            $orderIds[] = $v['old_id'];
        }
        $supplierAndOrgInfo = (new Foss())->getSupplierAndOrgInfoByOrderId([
            'order_ids' => $orderIds,
            'supplier_info_fields' => [
                'settlement_docker'
            ],
            'org_info_fields' => [
                'belongto_saler',
            ],
        ]);
        $orderInfo = app(OrderModel::class)->getBatchOrderAndExtInfo([
            'order_id' => $orderIds,
        ], [
            'order_id',
            'third_order_id',
            'real_oil_num'
        ], [
            'order_id',
            'supplier_price'
        ])->keyBy('order_id')
          ->toArray();
        $currentOrgInfo = (new OilOrgModel())->getBatchOrgByParams([
            'orgcode' => array_column($historyInfo['data'], 'gascode'),
        ], [
            'is_system_orgcode',
            'orgcode',
        ])->keyBy('orgcode');
        $stationInfo = app(StationRepository::class)->getBatchStationInfoByParams([
            'id' => array_column($historyInfo['data'], 'station_id'),
        ], [
            'card_assign_method', 'id'
        ], false)->keyBy('id');
        foreach ($historyInfo['data'] as $item) {
            $historyAssocOrderId = $item['old_id'];
            // 由于异常修改生成的新流水的old_id存入的是原订单号，故这里需要兼容
            if ($item['log_type'] == HistoryModel::COST_ABNORMAL) {
                $historyAssocOrderId = $item['id'];
            }
            $array = [
                'history_id'          => $item['id'],
                'station_id'          => $item['station_id'],
                'goods'               => array_get($oilDict, 'oil_type.' . $item['oil_type'], '') . array_get($oilDict, 'oil_name.' . $item['oil_name'], '') . array_get($oilDict, 'oil_level.' . $item['oil_level'], ''),
                'oil_num'             => sprintf('%.2f', $item['oil_num']),
                'pay_price'           => sprintf('%.2f', $item['pay_price']),
                'pay_money'           => sprintf('%.2f', $item['data_type'] == '13' ? $item['money'] : $item['pay_money']),//客户应收金额,
                'truck_no'            => $item['truck_no'] ?? '--',
                'card_no'             => (string)$item['card_no'],
                'org_name'            => $orgInfo[$item['gascode']]['org_name'] ?? '--',
                'oil_time'            => $item['oil_time'],
                'history_status'      => 1,
                'history_status_name' => '扣款成功',
                'supplier_money' => sprintf(
                    '%.2f',
                    round(
                        bcmul(
                            $orderInfo[$historyAssocOrderId]['real_oil_num'] ?? '',
                            $orderInfo[$historyAssocOrderId]['ext']['supplier_price'] ?? '',
                            3
                        ),
                        2
                    )
                ),
                'settlement_docker'   => $supplierAndOrgInfo[$historyAssocOrderId]['settlement_docker'] ?? '',
                'belongto_saler'      => $supplierAndOrgInfo[$historyAssocOrderId]['belongto_saler'] ?? '',
                'order_id'            => $historyAssocOrderId,
                'third_order_id'      => $currentOrgInfo[$item['gascode']]['is_system_orgcode'] == 2 ?
                    ($orderInfo[$historyAssocOrderId]['third_order_id'] ?? '') : '',
                'is_system_org'       => $currentOrgInfo[$item['gascode']]['is_system_orgcode'] ?? 1,
                'show_assign_query'   => $stationInfo[$item['station_id']]['card_assign_method'] == 4,
            ];
            // 无流水状态作为筛选条件时
            if ($getRefundingHistory == true) {
                if (empty($params['history_status']) && in_array($item['id'], $originalHistoryId)) {
                    $array['history_status'] = 2;
                    $array['history_status_name'] = '退款中';
                    // 直接筛选退款单时
                } else if ($params['history_status'] == 2) {
                    $array['history_status'] = 2;
                    $array['history_status_name'] = '退款中';
                }
            }

            if (in_array($item['log_type'], [HistoryModel::COST_INVALID, HistoryModel::COST_INVALID_2, HistoryModel::COST_RESERVATION_REFUEL_REFUND])) {
                $array['history_status'] = 3;
                $array['history_status_name'] = '已退款';
            }

            $needArray[] = $array;
        }

        $historyInfo['data'] = $needArray;

        return $historyInfo;
    }

    /**
     * 获取加油小票
     *
     * @param $params
     * @return array
     */
    public function getTicket($params)
    {
        $historyItem = $this->historyRepository->getOneHistoryByParams([
           'id' => $params['history_id'],
           'log_type' => [HistoryModel::COST_SUCCESS, HistoryModel::COST_ABNORMAL, HistoryModel::COST_SUCCESS_ADD]
        ]);
        if (empty($historyItem)) {
            return [];
        }
        // 站点
        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $historyItem['station_id']]);
        // 油品
        $oilDict = $this->dictRepository->getOilDict();
        // 订单扩展信息
        $orderExt = $this->orderExt->getInfoByOrderId($historyItem['id']);
        $mac_price = array_get($orderExt,"mac_price",$historyItem['pay_price']);
        $mac_amount = array_get($orderExt,"mac_amount",$historyItem['pay_money']);
        return [
            'order_id' => $historyItem['id'],
            'station_name' => $stationInfo['station_name'],
            'remark_name' => $stationInfo['remark_name'],
            'truck_no' => $historyItem['truck_no'],
            'driver_phone' => $this->numberUtil->encryptNumber($historyItem['driver_phone'], 3, 4),
            'card_no' => $this->numberUtil->encryptNumber($historyItem['card_no'], 4, 6),
            'goods' => array_get($oilDict, 'oil_type.'.$historyItem['oil_type'], ''). array_get($oilDict, 'oil_name.'.$historyItem['oil_name'], ''). array_get($oilDict, 'oil_level.'.$historyItem['oil_level'], ''),
            'oil_price' => sprintf('%.2f', $historyItem['pay_price']),
            'oil_num' => sprintf('%.2f', $historyItem['oil_num']),
            'oil_money' => sprintf('%.2f', $historyItem['pay_money']),
            'service_price' => sprintf('%.2f', $historyItem['service_price']),
            'service_money' => sprintf('%.2f', bcmul($historyItem['service_price'], $historyItem['oil_num'], 3)),
            'total_money' => sprintf('%.2f', bcadd($historyItem['pay_money'], bcmul($historyItem['service_price'], $historyItem['oil_num'], 3), 3)),
            'pay_time' => $historyItem['oil_time'],
            'update_time' => $historyItem['updatetime'],
            'creator' => $historyItem['creator'] ?? '',
            'driver_signature' => empty($historyItem['imgurl']) ? '' : $this->ossConfig['host'].$historyItem['imgurl'],
            'mac_price' => sprintf('%.2f',$mac_price),
            'mac_amount' => sprintf('%.2f',$mac_amount)
        ];
    }

    public function getHistoryInfo(array $whereParas, $select = ['*'])
    {
        return $this->historyRepository->getOneHistoryByParams($whereParas,$select);
    }

    /**
     * 列表数据字段过滤
     * @param  $params array
     * @param  $result array
     * @return array
     */
    public function filterHistoryList(array $params,array $result){
        $routes=Request::only(['routes'])['routes'];
        $routes=explode(',',$routes);
        if( ((isset($params['type']) && $params['type'] == FossRuleDefine::PLAT) || !isset($params['type'])) && in_array(FossRuleDefine::HISTORY_PLAT,$routes)){
            //平台全部输出
            return $result;
        }elseif(((isset($params['type']) && $params['type'] == FossRuleDefine::OUTER) || !isset($params['type'])) && in_array(FossRuleDefine::HISTORY_OUTER,$routes)){
            //对外输出
            if(isset($result['data']) && !empty($result['data'])){
                foreach ($result['data'] as $k=>$item){
                    unset($result['data'][$k]['pay_money']);//客户应付金额
                    unset($result['data'][$k]['pay_price']);//客户应付单价
                    unset($result['data'][$k]['gascode_name']);//司机机构
                    unset($result['data'][$k]['driver_name']);//司机姓名
                }
            }else{
                $result['data']=[];
            }
        }else{
            $result['data']=[];
        }
        return $result;
    }

    public function getDocumentTypeByHistoryId($historyId): int
    {
        $c = $this->historyRepository->getHistoryCountByParams([
            'old_id' => $historyId,
            'ge_createtime' => date('Y-m-d H:i:s',strtotime('-6 hours'))
        ]);
        if ($c > 2) {
            return CardTradeConf::$history_type_to_foss_mapping[HistoryModel::COST_INVALID_2];
        }
        if ($c > 1) {
            return CardTradeConf::$history_type_to_foss_mapping[HistoryModel::COST_INVALID];
        }
        return CardTradeConf::$history_type_to_foss_mapping[HistoryModel::COST_SUCCESS];
    }
}