<?php
/**
 * Created by PhpStorm.
 * User: liying02
 * Date: 2021/09/22
 * Time: 15:57
 */

namespace App\Models;

/**
 * Class CouponsTypeModel
 * @package App\Models\Foss\Coupon
 * @property $id
 * @property $name
 * @property $alias
 * @property $refuel_type
 * @property $face_value
 * @property $pcode
 * @property $sell_status
 * @property $show_status
 * @property $un_used_stock
 */
class CouponsTypeModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'foss_coupons_type';

    protected $guarded = ["id"];

    protected $fillable = ['name',"alias","refuel_type","face_value", 'top_oil_cat', 'goods_code', 'instructions',
        'usage_type_desc', 'img', 'enable_time', 'disable_time', 'pcode', 'sell_status', 'show_status', 'used_stock',
        'un_used_stock', 'remark', 'creator', 'modifier'];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}