<?php


namespace App\Models;


class StationPcodeModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_pcode';

    public $incrementing = false;

    /**
     * 获取所有对G7能源账户开放的供应商
     *
     * @return mixed
     */
    public function getOneCardPcode()
    {
        return self::select('pcode')->where(['xpcode' => '20003JCP'])->groupBy('pcode')->pluck('pcode')->all();
    }
}