<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 * Class DayOrderReconciliationSnapshotModel
 * @package App\Models
 * @property $id
 * @property $org_code
 * @property $reconciliation_day
 * @property $total_num
 * @property $total_oil_num
 * @property $total_oil_money
 * @property $total_mac_money
 */
class DayOrderReconciliationSnapshotModel extends Model
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'foss_day_order_reconciliation_snapshot';

    protected $guarded = ["id"];

    protected $fillable = [
        'org_code',
        'reconciliation_day',
        'total_num',
        'total_oil_num',
        'total_oil_money',
        'total_mac_money',
    ];

    public $incrementing = false;

    public $timestamps = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}