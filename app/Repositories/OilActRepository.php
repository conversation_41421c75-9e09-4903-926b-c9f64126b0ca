<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\OilActModel;

class OilActRepository extends \App\Repositories\EloquentRepository
{
    protected $act;
    public function __construct(OilActModel $act)
    {
        $this->act = $act;
    }

    //查询
    public function getInfoByCode(array $condition)
    {
        $data = $this->act->select(['id','start_time','end_time','condition'])
            ->withCondition($condition)
            ->first();
        return $data;
    }

}