<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OrgTruckLimitModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_org_truck_limit';

    protected $guarded = ["id"];

    public $incrementing = true;

    protected $fillable = ['id','orgcode',"is_open","car_limit_type"];

    public function getFillAble()
    {
        return $this->fillable;
    }
}