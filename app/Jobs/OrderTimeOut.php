<?php

namespace App\Jobs;

use App\Library\Helper\Common;
use App\Repositories\NewOrderRepository;
use App\Services\MessageService;
use App\Services\NewOrderService;
use Exception;

class OrderTimeOut extends Basic
{
    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @param $orderId
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Common::log('info', date('Y-m-d H:i:s') . '订单号:' . $this->orderId . '支付失败,订单超时!');
            app(NewOrderService::class)->orderTimeOut(['order_id' => $this->orderId]);
            $orderInfo = app(NewOrderRepository::class)->getOneOrderByParams([
                'order_id' => $this->orderId
            ], [
                'order_id',
                'order_status',
                'org_code',
                'supplier_code',
            ]);
            app(MessageService::class)->pushData2OA([
                'order_id'     => $this->orderId,
                'org_code'     => $orderInfo['org_code'],
                'order_status' => $orderInfo['order_status'],
                'supplier_code' => $orderInfo['supplier_code'],
                'trade_type'    => $orderInfo['ext']['trade_type'],
            ], 'ORDER_STATUS_SYNC');
        } catch (Exception $e) {
            Common::log('info',
                date('Y-m-d H:i:s') . '订单号:' . $this->orderId . '订单超时,修改订单状态失败:' . $e->getMessage(
                ) . $e->getFile() . $e->getLine()
            );
        }
    }
}
