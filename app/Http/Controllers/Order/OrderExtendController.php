<?php


namespace App\Http\Controllers\Order;


use App\Http\Controllers\Controller;
use App\Services\OrderExtendService;

class OrderExtendController extends Controller
{
    protected $extendService;

    public function __construct
    (
        OrderExtendService $extendService
    )
    {
        $this->extendService = $extendService;
    }

    /**
     * 保存司机签名
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function addDriverSignature()
    {
        $field = [
            'order_id' => '',
            'driver_signature' => '',
        ];
        $rule = [
            'order_id' => 'required|numeric',
            'driver_signature' => 'required|string|max:200'
        ];
        $message = [
            'driver_signature.max' => '图片路径长度超限,请联系管理员'
        ];
        $param = $this->getParamAll($field);
        try {
            $this->validator($param, $rule, $message);
            $result = $this->extendService->addDriverSignature($param);

            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }
}