<?php

namespace App\Jobs;

use App\Servitization\Wx;
use Throwable;


class SendWxTemplateMessage extends Basic
{
    protected $toUser;
    protected $templateId;
    protected $templateData;

    /**
     * SendWxTemplateMessage constructor.
     * @param string $toUser
     * @param string $templateId
     * @param array $templateData
     */
    public function __construct(string $toUser, string $templateId, array $templateData)
    {
        $this->toUser = $toUser;
        $this->templateId = $templateId;
        $this->templateData = $templateData;
    }

    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/15 3:20 下午
     */
    public function handle()
    {
        Wx::requestWithAccessToken(env("WX_TEMPLATE_MESSAGE_SEND_URL"), [
            "touser"      => $this->toUser,
            "template_id" => $this->templateId,
            "data"        => $this->templateData,
        ]);
    }
}
