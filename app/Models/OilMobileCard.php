<?php
/**
 * Created by PhpStorm.
 * User: leiqing
 * Date: 2020-02-10
 * Time: 15:21
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OilMobileCard extends Model
{
    /**
     * 表名
     */
    protected $table = 'oil_mobile_card';

    /**
     * 缓存组名
     */
    static protected $cacheTag = 'mobile_card';

    /**
     * 可入库字段
     */
    protected $fillAble = ['id','orgcode','vice_no','mobile','createtime','updatetime'];

    //disable incrementing id;
    public $incrementing = FALSE;

    protected $guarded = [];
    /**
     * 隐藏输出字段
     */
    protected $hidden = [];

    /**
     * 数据库连接
     *
     * @var string
     */
    protected $connection = 'mysql_foss';

    const UPDATED_AT='updatetime';
    const CREATED_AT = 'createtime';

    public function Org()
    {
        return $this->belongsTo('App\Models\Foss\Org', 'orgcode', 'orgcode');
    }

    public function getFillAble()
    {
        return $this->fillAble;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return mixed
     */
    public function scopeFilter($query, $params)
    {
        //Search By city_code
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            if(is_array($params['vice_no']) && count($params['vice_no']) > 0){
                $query->whereIn('vice_no', $params['vice_no']);
            }else{
                $query->where('vice_no', '=', $params['vice_no']);
            }
        }

        if(isset($params['orgcode']) && $params['orgcode'] != ''){
            $query->where('orgcode', '=', $params['orgcode']);
        }

        if(isset($params['mobile']) && $params['mobile'] != ''){
            $query->where('mobile', '=', $params['mobile']);
        }

        return $query;
    }

    /**
     * 卡列表
     * @return mixed
     */
    public static function getList()
    {
        //todo
    }

    /**
     * 根据id获取卡信息
     * @param $id
     * @return mixed
     */
    public static function getById($id)
    {
        return self::where('id',$id)->first();
    }

    /**
     * 根据账号获取卡信息
     * @param $vice_no
     * @return mixed
     */
    public static function getByViceNo($params = [])
    {
        return self::Filter($params)->first();
    }

    /**
     * 新增数据
     */
    public static function addData($data = [])
    {
        return self::create($data);
    }
}
