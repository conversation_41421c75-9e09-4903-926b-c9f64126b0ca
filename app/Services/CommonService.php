<?php


namespace App\Services;

use App\Library\Request;
use App\Http\Defines\FossRuleDefine;
use App\Servitization\FossUser;

class CommonService
{

    public static function isMobileInWhiteList($mobile)
    {
        $list = config("service_notice.maintain.white.mobile");

        if (is_null($list))
            return true;

        if (empty($list))
            return false;

        return in_array($mobile, $list);
    }

    /**
     * 获取用户绑定组织下的组织类型下的站点或运营商
     * @param $params array
     * @return array
     */
    public function getStationOrSupplierOfAdminUserRule($params){
        $rule_val=Request::only(['rule_val'])['rule_val'];
        $rule_type=Request::only(['rule_type'])['rule_type'];
        if($rule_type == FossRuleDefine::PART_STATION){
            //部分站点
            $station_codes=explode(',',$rule_val);
            if(isset($params['station_id'])){
                $param_station_code=explode(',', $params['station_id']);
                $diff=array_diff($param_station_code,$station_codes);
                if(!empty($diff)){
                    $params['station_id']=$rule_val;
                }
            }else{
                $params['station_id']=$rule_val;
            }
        }elseif($rule_type == FossRuleDefine::PART_SUPPLIER){
            //部分运营商
            $pcodes=explode(',',$rule_val);
            if(isset($params['pcode'])){
                $param_pcode=explode(',', $params['pcode']);
                $diff=array_diff($param_pcode,$pcodes);
                if(!empty($diff)){
                    $params['pcode']=$pcodes;
                }
            }else{
                $params['pcode']=$pcodes;
            }
        }
        return $params;
    }
}