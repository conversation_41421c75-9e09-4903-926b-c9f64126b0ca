<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/15
 * Time: 17:27
 */

namespace App\Http\Middleware;

use App\Library\Helper\Common;
use App\Library\Request;
use Closure;

define('START', microtime(true));

class BeforeMiddleware
{
    //第三个参数为额外传参
    public function handle($request, Closure $next)
    {
        //前置中间件,在执行路由定义指定的操作前做你想做的事情
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->setTag("http.method", $request->getMethod());
            $rootSpan->log([
                'request_data'    => $request->all(),
                'request_headers' => $request->header(),
                'current_time'    => (string)microtime(true),
            ]);
        }
        return $next($request);
    }
}