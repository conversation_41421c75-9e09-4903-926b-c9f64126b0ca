<?php


namespace App\Repositories;


use App\Models\StationPcodeModel;
use App\Models\SupplierModel;

class SupplierRepositories
{
    protected $supplierModel;
    protected $stationPcodeModel;

    public function __construct
    (
        SupplierModel $supplierModel,
        StationPcodeModel $stationPcodeModel
    )
    {
        $this->supplierModel = $supplierModel;
        $this->stationPcodeModel = $stationPcodeModel;
    }

    /**
     *
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneCardSupplier($select = ['*'], $toArray = false)
    {
        $oneCardPcode = $this->stationPcodeModel->getOneCardPcode();

        return $this->supplierModel->getBatchSupplierByParams(['scode' => $oneCardPcode], $select, $toArray);
    }

    /**
     * 获取供应商信息
     *
     * @param $scodes
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getSupplierByScodes($scodes, $select = ['*'], $toArray = false)
    {
        return $this->supplierModel->getBatchSupplierByParams(['scode' => $scodes], $select, $toArray);
    }
}