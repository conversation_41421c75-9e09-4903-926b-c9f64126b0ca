<?php


namespace App\Models;


class GasHistoryApproveModel extends BaseModel
{
    /**
     * history_type enum
     */
    CONST INSERT_HISTORY = 1;
    CONST UPDATE_HISTORY = 2;
    CONST CANCEL_HISTORY = 3;

    public static $HISTORY_TYPE_ENUM = [
        self::INSERT_HISTORY => '补录',
        self::UPDATE_HISTORY => '异常修改',
        self::CANCEL_HISTORY => '退款',
    ];

    /**
     * approve_status enum
     */
    CONST WAIT_APPROVE = 1;
    CONST FAIL_AFTER_APPROVE = 2;
    CONST SUCCESS_AFTER_APPROVE = 3;
    CONST FINISH_AFTER_APPROVE = 4;
    const REJECT_APPROVE = 5;
    const REFUND_SUCCESS_AFTER_APPROVE = 6;

    public static $APPROVE_STATUS_ENUM = [
        self::WAIT_APPROVE                 => '待审核',
        self::FAIL_AFTER_APPROVE           => '审核通过且失败',
        self::SUCCESS_AFTER_APPROVE        => '审核通过且成功',
        self::FINISH_AFTER_APPROVE         => '已完结',
        self::REJECT_APPROVE               => '驳回',
        self::REFUND_SUCCESS_AFTER_APPROVE => '审核通过且退款成功',
    ];

    /**
     * is_delete enum
     */
    CONST HAS_DELETE = 1; // 已删除
    CONST NOT_DELETE = 0; // 未删除

    /**
     * 连接名
     */
    protected $connection = 'mysql_gas';

    /**
     * 表名
     */
    protected $table = 'gas_history_approve';

    /**
     * 不自动维护createtime updatetime
     */
    public $timestamps = false;

    protected $fillable = [
        'id', 'history_id', 'original_history_id', 'history_type', 'approve_mirror', 'approve_status', 'source',
        'approve_abnormal_reason', 'remark', 'truck_no', 'card_no', 'driver_phone', 'station_id', 'is_delete',
        'create_time', 'creator', 'update_time', 'updator', 'third_order_id', 'refund_system',
    ];

    /**
     * 分页查询审核列表
     *
     * @param array $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @return array
     */
    public function getHistoryApprovePaginate($whereParams = [], $select = ['gas_history_approve.*'], $page = 1, $limit = 10, $toArray = false)
    {

        $buildParam = collect($whereParams)->except('order_sale_type')->toArray();
        $query = BaseModel::scopeWithCondition(GasHistoryApproveModel::select($select), $buildParam, true, 'gas_history_approve');

        if (! empty($whereParams['order_id'])) {
            $query->where(function ($query) use ($whereParams) {
                $query->where('gas_history_approve.history_id', '=', $whereParams['order_id'])
                    ->orWhere('gas_history_approve.original_history_id', '=', $whereParams['order_id']);
            });
        }


        $orderParam = collect($whereParams)->only('order_sale_type')->toArray();
        $query = BaseModel::scopeWithCondition($query, $orderParam, true, 'gas_order_new');


        $query->leftJoin('gas_order_new',function ($join){
            $join->on( 'gas_order_new.order_id', '=', 'gas_history_approve.order_id')->whereNotNull('gas_order_new.order_id');
        });

        $count = $query->count();

        $data = $query->orderBy('gas_history_approve.id', 'DESC')->offset(($page - 1)*$limit)->limit($limit)->get();

        if ($toArray) {
            $data = empty($data) ? [] : $data->toArray();
        }

        return ['data' => $data, 'count' => $count, 'page' => $page, 'limit' => $limit];
    }

    /**
     * 获取一条审核单
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @param bool $lock
     * @return array|object
     */
    public function getOneApproveInfoByParams(array $whereParams, array $select = ['*'],
                                              bool $toArray = false, bool $lock = false)
    {
        $model = BaseModel::scopeWithCondition(self::select($select), $whereParams);
        if ($lock) {

            $model = $model->lockForUpdate();
        }

        $result = $model->first();
        if ($toArray) {

            $result = empty($result) ? [] : $result->toArray();
        }
        return $result;
    }

    /**
     * 获取批量审核记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchApproveInfoByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams, true)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 创建审核单
     *
     * @param array $insertArray
     * @return mixed
     */
    public function approveInsert(array $insertArray)
    {
        return self::insertGetId($insertArray);
    }

    /**
     * 更新审核单
     *
     * @param $id
     * @param array $updateArray
     * @return mixed
     */
    public function approveUpdateById($id,array $updateArray)
    {
        return self::where(['id' => $id])->update($updateArray);
    }
    /**
     * @param array $whereParams
     * @param string[] $select
     * @return mixed
     */
    public function getHistoryApproveCount($whereParams = [], $select = ['*'])
    {
        $historyParams = collect($whereParams)->except(['order_sale_type'])->toArray();
        $query = BaseModel::scopeWithCondition(GasHistoryApproveModel::select($select), $historyParams, true, 'gas_history_approve');
        if (! empty($whereParams['order_id'])) {
            $query->where(function ($query) use ($whereParams) {
                $query->where('gas_history_approve.history_id', '=', $whereParams['order_id'])
                    ->orWhere('gas_history_approve.original_history_id', '=', $whereParams['order_id']);
            });
        }
        $orderParam = collect($whereParams)->only(['order_sale_type'])->toArray();
        $query = BaseModel::scopeWithCondition($query, $orderParam, true, 'gas_order_new');
        if($orderParam) {
            $query->leftJoin('gas_order_new', function ($join) {
                $join->on('gas_order_new.order_id', '=', 'gas_history_approve.order_id')->whereNotNull('gas_order_new.order_id');
            });
        }
        return  $query->count();
    }

}