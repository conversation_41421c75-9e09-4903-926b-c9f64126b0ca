<?php

namespace App\Repositories;

use App\Models\ElectricityOrderDetailModel;

class ElectricityOrderDetailRepository
{
    protected $electricityOrderDetailModel;

    public function __construct
    (
        ElectricityOrderDetailModel $electricityOrderDetailModel
    )
    {
        $this->electricityOrderDetailModel = $electricityOrderDetailModel;
    }

    public function insertElectricityOrderDetail(array $params): bool
    {
        return $this->electricityOrderDetailModel::insertElectricityOrderDetail($params);
    }
}
