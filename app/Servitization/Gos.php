<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/1
 * Time: 14:43
 */
namespace App\Servitization;
use App\Exceptions\ReponseException;
use App\Library\Helper\Common;
use App\Library\Encrypt;
use App\Library\Helper\KafkaLog;
use Illuminate\Support\Facades\Log;
class Gos
{

    private  $config;

    public function __construct()
    {
        $this->config = config('gos');
    }

    /*
     * 请求Gos生成二维码
     * author : txb
     */
    public function getPaycode($params)
    {
        $sendParams = [
            'method'    => 'v1/pay/getQrCodeContent',
            'apiParams' => $params,
        ];
        $result = $this->post($sendParams);
        return $result;
    }

    public function searchCard($params)
    {
        $sendParams = [
            'method'    => 'v1/cardVice/get_list',
            'apiParams' => $params,
        ];
        $data = $this->post($sendParams);
        return $data;
    }

    /**
     * 查询站点列表
     * @param $params
     * @return mixed
     * @throws ReponseException
     */
    public function getStationList($params)
    {
        $sendParams = [
            'method'    => 'gasApp/station/getList',
            'apiParams' => $params,
        ];
        $data = $this->post($sendParams);
        return $data;
    }

    //gos请求公用方法
    public function post($params = [], $addKafkaLog = false)
    {
        $sendParams = [
            'request_id'   => Common::uuid(),
            'app_id'       => $this->getAppId(),
            'channel'      => $params['channel'] ?? null,
            'action'       => $params['action'] ?? null,
            'method'       => $params['method'] ?? null,
            'dataType'     => $params['dataType'] ?? null,
            'attach'       => $params['attach'] ?? null,
            'data'         => $this->encode($params['apiParams']),
            'callback_url' => $this->getCallbakUrl(),
        ];
        $apiUrl            = $this->getAppUrl() . '/' . $params['method'];

        $startTime = microtime(true)*1000;
        $response = Common::requestJson($apiUrl, $sendParams);
        $cost = microtime(true)*1000 - $startTime;
        $arr = json_decode($response, true);
        if ($addKafkaLog) {
            KafkaLog::thirdOperationLog($params['method'], 'gos', $params, $response, $cost);
        }
        if(json_last_error()) {
            \Log::info(['message' => '请求gos接口异常', 'data' => $response]);
            throw new ReponseException('请求失败，请重试');
        }
        if((isset($arr['code']) && $arr['code'] != 0) ) {
            throw new ReponseException($arr['message'], $arr['code']);
        }
        if(array_has($arr, ['code', 'data']) == false) {
            \Log::info(['message' => '请求gos接口异常', 'data' => $response]);
            throw new ReponseException('网络异常,请重试');
        }
        return $arr['data'];

    }

    public function encode(array $params = [])
    {
        $rsa = new Encrypt($this->config['public_key']);
        return $rsa->encrypt($params);
    }

    public function decode($content)
    {
        $rsa = new Encrypt($this->config['public_key']);

        return $rsa->decrypt($content);
    }

    public function getAppId()
    {
        return $this->config['app_id'];
    }

    public function getAppUrl()
    {
        return $this->config['url'];
    }

    public function getCallbakUrl()
    {
        return $this->config['callbak_url'];
    }

    public function getPageStyle()
    {
        return $this->config['page_style'];
    }

    public function filterStationWithLimitConfiguration($params)
    {
        $sendParams = [
            'method'    => 'v1/station/filterStationWithLimitConfiguration',
            'apiParams' => $params,
        ];
        $result = $this->post($sendParams);
        return $result;
    }
}