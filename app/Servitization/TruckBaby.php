<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/20
 * Time: 10:05
 */
namespace App\Servitization;
use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class TruckBaby
{

    private  $config;

    public function __construct($serverConf = [])
    {
        $this->config = $serverConf;
    }

    /**
     * 执行
     *
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @param bool $addKafkaLog
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL, $addKafkaLog = false)
    {
        $params = $args;
        try {
            $dataJson = json_encode($params['data']);
            $url = trim(env("KC_API_URL"),'/').$params['method'];
            unset($params['method']);
            $encryptData = $this->createEncrypt($dataJson);

            $startTime = microtime(true)*1000;
            //$data = Common::request($type, $url,60, $params);
            $data = Common::requestJson($url,$encryptData,['Content-MD5'=>base64_encode(md5($dataJson,true))]);
            //$data = Common::GasPostHttp($url,$encryptData);
            $cost = microtime(true)*1000 - $startTime;
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($args['method'], 'oil-truck', $params, $data, $cost);
            }
            if (!$data) {
                throw new RuntimeException('三方服务器异常,请稍后再试', CommonError::ADAPTER_ERROR);
            }
            Log::info('请求truck,参数:' . json_encode($params, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);
            $data = json_decode($data, true);

            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (Exception $e) {
            KafkaLog::thirdOperationLog($args['method'], '三方-web', $params, $e->getMessage());
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    public function createEncrypt($content)
    {

        $pubPem = chunk_split(env("KC_RSA_PUBLIC"), 64, "\n");
        $pubPem = "-----BEGIN PUBLIC KEY-----\n" . $pubPem . "-----END PUBLIC KEY-----\n";
        $publicKey = openssl_pkey_get_public($pubPem);
        if ( !$publicKey ) {
            throw new \RuntimeException("rsa公钥获取失败", 500042);
        }
        $result = '';
        $data = str_split($content, 117);
        foreach ($data as $block) {
            openssl_public_encrypt($block, $dataEncrypt, $publicKey, OPENSSL_PKCS1_PADDING);
            $result .= $dataEncrypt;
        }
        return base64_encode($result);
    }

    public function pushOrder($params = [])
    {
        $data = self::exec('post', [
            'method' => '/api/api/g7/g7order',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * @param string $order_no
     * 查询订单详情
     */
    public function getDetail( $params = [] )
    {
        $data = self::exec('post', [
            'method' => '/api/api/g7/g7orderPayStatus',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * @param array $params
     * @return mixed
     * 给司机退款
     */
    public function refundOrder($params = [])
    {
        $data = self::exec('post', [
            'method' => '/api/api/g7/g7orderRefunded',
            'data'   => $params
        ]);

        return $data;
    }

    /**
     * 推送司机退款申请单审核结果
     * @param array $params
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 11:31 下午
     */
    public function pushDriverRefundApprovalStatusToKc($params = [])
    {
        return self::exec('post', [
            'method' => '/api/api/g7/g7orderRefundedExamine',
            'data'   => $params
        ]);
    }
}