<?php


namespace App\Library\Helper;


use App\Library\Request as ClientRequest;
use Library\Monitor\Kafka;
use Ramsey\Uuid\Uuid;

class KafkaLog
{
    /**
     * order服务请求三方服务的日志记录
     *
     * @param $path
     * @param $thirdWay
     * @param $whereParams
     * @param $response
     * @param $cost
     * @return bool
     * @throws \Exception
     */
    public static function thirdOperationLog($path, $thirdWay, $whereParams, $response, $cost = 0)
    {
        if (empty($headerInfo = ClientRequest::get('header_info'))) {
            return true;
        }

        $esParams = [
            'path' => $path,
            'cost' => empty($cost) ? intval(1000 *(microtime(true) - START)) : intval($cost),
            'way' =>  env('APPLICATION__NAME') ?? 'foss-order',
            'call' => 2,
            'source' => strtolower(array_get($headerInfo, 'source', '')),
            //nginx产生的request_id
            'request_id' => $headerInfo['request_id'] ?? Common::uuid(),
            //trace_id全局唯一,上游服务透传的日志id
            'trace_id' => $headerInfo['trace_id'] ??  Uuid::uuid1()->getHex(),
            //上游服务透传的授权的用户id
            'uid' => $headerInfo['uid'] ?? '',
            //上游服务透传的用户昵称
            'user_name' => $headerInfo['user_name'] ?? '',
            //上游服务透传的访客ip
            'request_real_ip' => $headerInfo['request_real_ip'] ?? $_SERVER['REMOTE_ADDR'],
            'third_way' => $thirdWay,
        ];
        $whereParams = is_array($whereParams) ? json_encode($whereParams, JSON_UNESCAPED_UNICODE|JSON_PARTIAL_OUTPUT_ON_ERROR) : $whereParams;
        $response = is_array($response) ? json_encode($response, JSON_UNESCAPED_UNICODE|JSON_PARTIAL_OUTPUT_ON_ERROR) : $response;

        return self::operationLog($esParams, $whereParams, $response);
    }

    /**
     * 和foss-station统一日志格式
     *
     * @param array $context
     * @param string $whereParams
     * @param string $response
     * @return bool
     */
    public static function operationLog(array $context = [], string $whereParams = "", string $response = "")
    {
        if (empty($context)) {
            return true;
        }
        $oaSuccessCode = config('oa.success_code');
        $responseArr = json_decode($response, true) ? json_decode($response, true) : [];

        if($responseArr) {
            $response = json_encode($responseArr, JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE);
        }
        //发送kafka日志
        $uuid = Common::uuid();
        $kafkaLogData = [
            //日志主键id，索引唯一
            //不设置的话es会自动生成, 但是此时数据唯一性不保证
            "id"      => $uuid,
            //es索引名，对应数据表名【建议和kafka topic对应上】
            "index"   => "gms_bussiness_log",
            //操作类型"insert|update|delete|upsert
            "op"      => "insert",
            //null字段是否修改，不传默认false
            "options" => ["writeMapNullValue" => true],
            // 消息的产生时间
            "time"    => date('Y-m-d H:i:s'),
            //业务方的实际需要存储的数据,和es索引字段一一对应,字段不能有额外的，否则会插入失败
            "data"    => [
                "id" => $uuid,
                "call" => $context['call'] ?? '',
                "params" => $whereParams,
                "result" => $response,
                "is_success" => isset($responseArr['code']) ?
                    ($context['path'] == '/data/push' && in_array($responseArr['code'], $oaSuccessCode) ?
                        1 : ($responseArr['code'] == 0 ? 1 : 0)) : 0,
                "path" => $context['path'] ?? '',
                "ip" => $context['request_real_ip'] ?? '',
                "source" => $context['source'] ?? '',
                "cost" => $context['cost'] ?? '',
                "uid" => $context['uid'] ?? '',
                "user_name" => $context['user_name'] ?? '',
                "way" => $context['way'] ?? env('APP_NAME'),
                "third_way" => $context['third_way'] ?? '',
                "create_time" => date('Y-m-d H:i:s'),
                "update_time" => date('Y-m-d H:i:s'),
                "request_id" => $context['request_id'] ?? '',
                "trace_id" => $context['trace_id'] ?? ''
            ],
            //kafka的topic名称，通常与索引名一一对应
            "topic"   => "gms_bussiness_log",
            //卡夫卡的key值，随意设置
            "key"     => "test-key",

        ];
        return Kafka::send($kafkaLogData);

    }
}