<?php


namespace App\Http\Controllers\Order;


use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Services\OrderApproveService;
use App\Services\PaymentService;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct
    (
        PaymentService $paymentService
    )
    {
        $this->paymentService = $paymentService;
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '订单控制器初始化结束',
                'current_time' => (string)microtime(true),
            ]);
        }
    }

    /**
     * 验密和支付
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkPasswordAndPay()
    {
        $field = [
            'order_id' => '',
            'card_no' => '',
            'password' => ''
        ];

        $rule = [
            'order_id' => 'required|numeric',
            'card_no' => 'required|numeric',
            'password' => 'required|string'
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'card_no.required' => '账号必传!',
            'card_no.numeric' => '账号格式错误!',
            'password.required' => '密码必传!',
            'password.string' => '密码格式错误!',
        ];

        $params = $this->getParamAll($field);
        try {
            $this->validator($params, $rule, $message);
            $result = $this->paymentService->checkPasswordAndPay($params);
            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 微信卡包验密支付
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cardBoxPayment()
    {
        $field = [
            'order_id' => '',
            'card_no' => '',
            'password' => ''
        ];

        $rule = [
            'order_id' => 'required|numeric',
            'card_no' => 'required|numeric',
            'password' => 'required|string'
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'card_no.required' => '账号必传!',
            'card_no.numeric' => '账号格式错误!',
            'password.required' => '密码必传!',
            'password.string' => '密码格式错误!',
        ];

        $params = $this->getParamAll($field);
        try {
            $this->validator($params, $rule, $message);
            if( isset($params['is_new']) && $params['is_new'] == 1 ){
                $result = $this->paymentService->finishOrder($params);
            }else{
                $result = $this->paymentService->cardBoxPayment($params);
            }

            $response = [
                'code' => 0,
                'message' => '成功',
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            $response = [
                'code' => 2,
                'message' => $e->getMessage(),
                'success' => false,
                'data' => [],
            ];
        }

        return response()->json($response);
    }

    /**
     * OA支付成功后回调
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function oaPay()
    {
        $field = [
            'order_id' => '',
            'third_order_id' => '',
            'code' => '',
            'msg' => ''
        ];

        $rule = [
            'order_id' => 'required|numeric',
            'third_order_id' => 'string', // 三方订单号,OA不一定可获取到
            'code' => 'numeric',
            'msg' => 'string',
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'third_order_id.string' => '三方订单号格式错误!',
            'code.numeric' => '支付单状态码格式错误!',
            'msg.string' => '失败原因格式错误!'
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->paymentService->oaPay($params);
            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 支付待支付订单
     */
    public function payOrder(Request $request)
    {
        $params = $request->all();
        $rule = [
            'order_id' => 'required|numeric',
            'third_order_id' => 'string', // 三方订单号,OA不一定可获取到
            'password' => 'string',
            'code' => 'numeric',
            'msg' => 'string',
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'third_order_id.string' => '三方订单号格式错误!',
            'code.numeric' => '支付单状态码格式错误!',
            'msg.string' => '失败原因格式错误!'
        ];
        $this->validator($params, $rule, $message);
        $data = $this->paymentService->finishOrder($params);
        return $this->success($data);
    }
    
    /**
     * OA支付待支付订单
     */
    public function oaPayNew(Request $request)
    {
        $params = $request->all();
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '接参结束',
                'current_time' => (string)microtime(true),
            ]);
        }
        $rule = [
            'order_id' => 'required|numeric',
            'third_order_id' => 'string', // 三方订单号,OA不一定可获取到
            'password' => 'string',
            'code' => 'numeric',
            'msg' => 'string',
        ];
        
        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric' => '订单号格式错误!',
            'third_order_id.string' => '三方订单号格式错误!',
            'code.numeric' => '支付单状态码格式错误!',
            'msg.string' => '失败原因格式错误!'
        ];
        $this->validator($params, $rule, $message);
        $data = $this->paymentService->finishOrder($params, 'OA');
        return $this->success($data);
    }

    /**
     * 预约加油完成事件接口(预售转销售)
     * @throws \Throwable
     */
    public function reservationOrderToSaleOrder(Request $request)
    {
        $field = [
            'reservation_order_id' => null,
            'oil_type'             => null,
            'oil_name'             => null,
            'oil_level'            => null,
            'oil_unit'             => null,
            'mac_price'            => null,
            'supplier_price'       => null,
            'mac_amount'           => null,
            'supplier_money'       => null,
            'oil_money'            => null,
            'oil_num'              => null,
            'station_id'           => null,
            'third_order_id'       => null,
        ];
        $rule = [
            'reservation_order_id' => 'required|numeric',
            'oil_type'             => 'nullable|alpha_num',
            'oil_name'             => 'required|string',
            'oil_level'            => 'nullable|alpha_num',
            'oil_unit'             => 'required|in:1,2',
            'mac_price'            => 'required|range_price',
            'supplier_price'       => 'required|range_price',
            'mac_amount'           => 'required|money',
            'supplier_money'       => 'required|money',
            'oil_num'              => 'required|money',
            'station_id'           => 'required|alpha_num',
        ];
        $message = [
            'reservation_order_id.required' => '预约加油订单ID不能为空',
            'reservation_order_id.numeric'  => '预约加油订单ID格式不正确',
            'oil_num.required'              => '加油量不能为空',
            'oil_num.money'                 => '加油量格式不正确',
            'oil_name.required'             => '油品种类ID不能为空',
            'oil_name.string'               => '油品种类ID格式不正确',
            'oil_type.string'               => '油品标号ID格式不正确',
            'oil_level.string'              => '油品等级ID格式不正确',
            'oil_unit.required'             => '无效的计费模式: 1指定金额加油 2指定升数加油',
            'oil_unit.in'                   => '无效的计费模式: 1指定金额加油 2指定升数加油',
            'mac_price.required'            => '油机价不能为空',
            'mac_price.range_price'         => '油机价格式不正确',
            'mac_amount.required'           => '油机金额不能为空',
            'mac_amount.money'              => '油机金额格式不正确',
            'supplier_price.required'       => 'G7进价不能为空',
            'supplier_price.range_price'    => 'G7进价格式不正确',
            'supplier_money.required'       => '站点应收金额不能为空',
            'supplier_money.money'          => '站点应收金额格式不正确',
            'station_id.required'           => '站点ID不能为空',
            'station_id.alpha_num'          => '站点ID格式不正确',
        ];
        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);
            $this->paymentService->reservationOrderToSaleOrder($params);
            return $this->success([], true, '成功');
        } catch (\Exception $e) {
            global $rootSpan;
            if ($rootSpan) {
                $rootSpan->logException($e);
            }
            return $this->fail(empty($e->getCode()) ? 403 : $e->getCode(), $e->getMessage());
        }
    }
}