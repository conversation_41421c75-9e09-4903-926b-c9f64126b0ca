<?php
namespace Tests\Unit;

use Ramsey\Uuid\Uuid;
use Tests\TestCase;
use Illuminate\Support\Facades\Redis;

class CreateOrderTest extends TestCase {

    public function testCreateOrderWithPda()
    {
        //order_token=4285308452&
        //amountGun=1234.69&
        //order_type=2&
        //station_id=0d8ac53c571311eb9dcf06b6e98b1527&
        //oil_type=ab31e8289ebd8fdfea01841072a251c3&
        //oil_name=1d7e1e11be017d9fe9d2717abe892837&
        //oil_level=66d5c1e1ee3aae9642128bf9cdb34157&
        //oil_unit=1&oil_num=102.89&oil_price=12.00&
        //oil_money=1150.33&service_price=0&
        //service_money=0.00
        //&urlsign=B5A8D24561A47FC9A54B0DD56F1CFD6B
        //&appkey=3085766248&
        //timestamp=1704348047
        $oilNum = rand(1, 10);
        $oilPrice = '7.68';
        $gunPrice = 8;
        $orderToken = rand(100000, 999999)."";
        $this->testCreateToken($orderToken);
        $data = [
            'order_token' => $orderToken,
            'amountGun' => '1234.69',
            'order_type' => 2,
            'station_id' => '0d8ac53c571311eb9dcf06b6e98b1527',
            'oil_type' =>  'ab31e8289ebd8fdfea01841072a251c3',
            'oil_name' => '1d7e1e11be017d9fe9d2717abe892837',
            'oil_level' => '66d5c1e1ee3aae9642128bf9cdb34157',
            'oil_unit' => 1,
            'oil_num' => '95.86',
//            'oil_price' => '13.00',
            'oil_price' => '12.00',
            'oil_money' => '1150.33',
            'service_price' => '0.00',
            'service_money' => '0.00',
            'urlsign' => 'B5A8D24561A47FC9A54B0DD56F1CFD6B',
            'appkey' => '3085766248',
            'timestamp' => '1704348047'
        ];
//        $data = [
//            'station_id' => '119e6bfc7f7311eeae13fa163e2a9678',
//            'oil_name' => '1d7e1e11be017d9fe9d2717abe892837',
//            'oil_type' => '09eca763d2cfca0c7072579217bcfb90',
//            'oil_level' => '66d5c1e1ee3aae9642128bf9cdb34157',
//            'oil_num'   => $oilNum,
//            'oil_price' => $oilPrice,
//            'oil_money' => bcmul($oilPrice , $oilNum, 2),
//            'oil_unit' => 1,
//            'amountGun' => bcmul($gunPrice , $oilNum, 2),
//            'service_price' => '0.00',
//            'service_money' => '0.00',
//            'order_type' => '2',
//            'order_token' => $orderToken
//        ];
        $this->withServerVariables(['REMOTE_ADDR' => '192.168.1.2'])
            ->post('/api/order/createOrder' , $data,
            ['X-G7-Api-Uid' => '5b5e3de3072f5db59b4e44f732569e58', 'appkey' => '4319971435'])
            ->assertStatus(200)->dump();

    }

    //下游司机主动下单
    public function testCreateOrderWithDriver()
    {
        $oilNum = rand(1, 10);
        $oilPrice = '7.75';
        $overLoadOilPrice = '7.76';
        $gunPrice = 8;
        $thirdOrderId = 'NN23111610553392'.rand(1000000, 9999999);
        $data = [
            //满帮
            'card_no' => "****************",
//            'card_no' => '8211081969178581',
            "driver_name" => "李四",
            "driver_phone" => '13800138000',
            'truck_no' => '京C123111',
            'station_id' => '119e6bfc7f7311eeae13fa163e2a9678',
            'oil_name' => '1d7e1e11be017d9fe9d2717abe892837',
            'oil_type' => '09eca763d2cfca0c7072579217bcfb90',
            'oil_level' => '66d5c1e1ee3aae9642128bf9cdb34157',
            'oil_num'   => $oilNum,
//            'oil_price' => $oilPrice,
            'oil_price' => $overLoadOilPrice,
            'oil_money' => bcmul($oilPrice , $oilNum, 2),
            'pay_money' => bcmul($oilPrice , $oilNum, 2),
            'third_order_id' => $thirdOrderId,
            'trade_mode' => '10',
            'driver_source' => '2',
            'priceGun' => $gunPrice,
            'amountGun' => bcmul($gunPrice, $oilNum, 2),
            'gunNumber' => 4,

        ];
        $this->post('/api/oil_adapter/makeOrder' , $data,
                ['appkey' => '**********'])
            ->assertStatus(200)->dump();

    }

    //生成二维码
    public function testCreateToken($token)
    {
        if(empty($token)) {
            $token = rand(100000, 999999)."";
        }
        $redis = Redis::Connection('');
        $tokenInfo = [
            'card_no' => '****************',
            'is_access_platform_driver' => 1,
            'driver_name' => '张三',
            'driver_phone' => '***********',
            'truck_no' => '京A123456',
            'extends' => json_encode(['extends' => json_encode(['idCode' => 'wlqq://payment/{"QrCode":"RLqCbpEeWotjKM36hKWxLGJqD"}'])]),
            ];
        $tokenInfo = json_encode($tokenInfo, JSON_UNESCAPED_UNICODE);
        return $redis->set('order_token_'.$token, $tokenInfo);
    }

    //订单补录
    public function testAdditionalAddOrUpdate()
    {

        $reqData = [
            'appkey' => '**********',
            'timestamp'=> time(),
            'urlsign' => 'xxx',
            'card_no' => '****************',
            //满帮
//            'card_no' => '****************',
            'card_account_type' => 1,
//            'driver_phone' => '***********',
            'driver_phone' => '***********',
            'truck_no' => '京*********',
            'driver_name' => '张三',
//            'driver_name' => '付测试',
            'station_id' => '46f53a98b61f11ec8659fa163e40bb47',
            'provice_code' => '************',
            'city_code' => '************',
            'oil_name' => '1d7e1e11be017d9fe9d2717abe892837',
            'oil_type' => 'ab31e8289ebd8fdfea01841072a251c3',
            'oil_level' => '66d5c1e1ee3aae9642128bf9cdb34157',
            'gun_id' => '7ac29bf49b3311ee8ef8fa163e2a9315',
            'oil_time' => date('Y-m-d H:i:s'),
            'oil_unit' => 1,
            'mac_price' => '8.56',
            'mac_amount' => '2.1',
            'oil_num' =>  '0.24',
            'supplier_price' => '8.56',
            'supplier_money' => '2.1',
            'platform_price' => '7.55',
            'oil_money' => '1.85',
            'third_order_id' => '',
            'remark' => '付错油站',
            'remarks' => '',
            'img' => '',
            'ticket_image' => '',
            'truck_image' => '',
            'other_image' => '',
            'id' => '',
            'tank_id' => '7ac29aa09b3311eea34efa163e2a9315',
            'source' => '1',
            'settlement_type' => 2,
            'discount_rate' => '88.20'

        ];
        $this->post('/newOrder/v1/approve/additionalAddOrUpdate' , $reqData,
            ['appkey' => '**********', 'X-G7-Api-Uid' => '1'])
            ->assertStatus(200)->dump();
    }

}
