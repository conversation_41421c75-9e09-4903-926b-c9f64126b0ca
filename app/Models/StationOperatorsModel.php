<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationOperatorsModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_station_operators';

    protected $guarded = ["id"];

    protected $fillable = ['id','operators_code','operators_name',"status","is_show","short_name","audio_url",
        "is_second_check","no_used_order_invoice","is_self_operated"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}