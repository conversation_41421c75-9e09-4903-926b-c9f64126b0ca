<?php

/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2020/09/03
 * Time: 10:05
 */

namespace App\Services;

use App\Exceptions\ParamInvalidException;
use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Library\Request;
use App\Repositories\CouponTypeRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OilActRepository;
use App\Repositories\OilMobileCardRepository;
use App\Servitization\Adapter;
use App\Servitization\FossUser;
use App\Servitization\Gas;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MiniOrderService
{
    private $cardService;
    private $stationService;
    private $historyReponsitory;
    private $actReponsitory;
    private $newOrder;
    private $paymentService;
    private $mobileCard;
    private $h5Src;
    private $newOrderRepository;
    protected $couponTypeRepo;

    public function __construct()
    {
        $this->cardService = app(CardService::class);
        $this->stationService = app(StationService::class);
        $this->historyReponsitory = app(HistoryRepository::class);
        $this->actReponsitory = app(OilActRepository::class);
        $this->newOrder = app(NewOrderService::class);
        $this->paymentService = app(PaymentService::class);
        $this->mobileCard = app(OilMobileCardRepository::class);
        $this->h5Src = app(H5Service::class);
        $this->newOrderRepository = app(NewOrderRepository::class);
        $this->couponTypeRepo = app(CouponTypeRepository::class);
    }

    function validator($param, $rule, $message = [])
    {
        $attribute = config('attribute');
        $validator = Validator::make($param, $rule, $message, $attribute);

        if ($validator->fails()) {
            throw new ParamInvalidException($validator->getMessageBag()->first());
        }
    }


    public function getTradeInfo(array $params)
    {
        return $this->historyReponsitory->getTradeById(['id'=>$params['history_id']]);
    }


    //调用gas,下单接口
    public function submitStationPay(array $params)
    {
        $card_info = $this->cardService->getCardInfo($params);

        //灰度方式验证新下单流程
        if( CommonDefine::grayOrderOrg($card_info->gascode) ){
            $params['order_channel'] = CardTradeConf::WMP_ON_LINE;
            $header = Request::httpHeaders();
            $params['user_id'] = $header['x-g7-api-uid'][0];
            return $this->placeOrder($params);
        }

        $stationInfo = $this->stationService->getStationInfo($params);
        if(!$stationInfo){
            throw new \RuntimeException('',CommonError::STATION_OFF);
        }

        $deductUnit = 1;
        if(isset($stationInfo->station_oil_unit) && $stationInfo->station_oil_unit){
            $deductUnit = $stationInfo->station_oil_unit;
        }

        if ( in_array($stationInfo->pcode,CommonDefine::forceOilUnitPcode()) ) {
            $deductUnit = 2;
        }

        if( $deductUnit == 2 ){
            $rule = [
                'oil_num' => 'required|numeric',
            ];
            $messages = [
                'oil_num.numeric' => '请输入加油升数',
            ];
            $this->validator($params,$rule,$messages);
            $params['money'] = number_format(($params['oil_num'] * $params['price']),2,".","");
        }else{
            $rule = [
                'money' => 'required|numeric',
            ];
            $messages = [
                'money.numeric' => '请输入合法的金额',
            ];
            $this->validator($params,$rule,$messages);
        }

        $gunInfo = $this->stationService->getGunInfo($params);
        if(!$gunInfo){
            throw new \RuntimeException('',CommonError::NO_GUN);
        }

        // 检测站点限制
        $gas_res = [];
        $gas_res = (new Gas())->stationLimit([
            'orgcode' => $card_info->gascode,
            'card_no' => $card_info->card_no,
            'station_id' => $stationInfo->id,
        ]);
        $error_message = array_get($gas_res, 'msg', null);
        if ($error_message) {
            throw new \RuntimeException($error_message,CommonError::GAS_ERROR);
        }

        if (bccomp(0, floatval($params['money']), 2) >= 0) {
            throw new \RuntimeException('请输入金额',CommonError::ERROR_MONEY);
        }

        $priceList = $this->stationService->getGmsOilPrice($params);

        if (bccomp($priceList['trade_price'], $priceList['macPrice'], 2) > 0 && $priceList['macPrice'] > 0) {
            throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付",CommonError::GUN_ERROR_PRICE);
        }

        $priceGun = isset($params['priceGun']) && $params['priceGun'] ? $params['priceGun'] : 0;
        if(bccomp($params['price'] ,$priceList['float_price'],2) != 0 || (bccomp($priceGun ,$priceList['macPrice'],2) != 0 && $priceList['macPrice'] > 0 && $priceGun > 0) ){
            throw new \RuntimeException("未能扣款成功，油站信息有更新，请您确认油站信息后重新支付",CommonError::STATION_OVERDUE);
        }
        //202-小程序主动付款，203-小程序小票机，302-h5主动付款，303-h5小票机，需要前端传过来
        $from = array_get($params,"from",202);
        // 调用gas,下单接口
        $data = [
            'card_id' => $card_info->id,
            'gun_id' => $gunInfo->id,
            'station_id' => $stationInfo->id,
            'amount' => $params['money'],
            'amountGun' => isset($params['amountGun']) && $params['amountGun'] ? $params['amountGun'] : 0,
            'gunNumber' => isset($params['gunNumber']) && $params['gunNumber'] ? $params['gunNumber'] : '',
            'price' => $params['price'],
            'actual_price' => isset($params['actual_price']) && $params['actual_price'] ? $params['actual_price'] : 0,
            'priceGun' => $priceGun,
            'starttime' => date('Y-m-d H:i:s'),
            'is_pay' => 22,
            'pcode' =>  isset($card_info->Org) && $card_info->Org && isset($card_info->Org->pcode) ? $card_info->Org->pcode : null,
            'custom_history_id' => $params['custom_history_id'],
            'source' => 'foss-order',
            'unit' => $deductUnit,
            'trade_from' => $from,
        ];
        if($deductUnit == 2){
            $data['oil_num'] = $params['oil_num'];
            //针对按升加油，跨站单价有误，不允许加油
            if(!isset($priceList['pcode_price']) || bccomp(0,$priceList['pcode_price'],2) >= 0){
                throw new \RuntimeException("站点应收单价有误，请联系G7人员进行核对",CommonError::ERROR_PCODE_PRICE);
            }
            $data['pcode_price'] = $priceList['pcode_price'];
            $data['pcode_money'] = number_format(($params['oil_num'] * $priceList['pcode_price']),2,".","");
        }
        $gas_res = (new Gas())->createTrades($data);
        Log::info('下单结果:返回结果:'. var_export($gas_res,true));
        if (empty($gas_res) || $gas_res['code'] != 0) {
            throw new \RuntimeException($gas_res['msg'],CommonError::GAS_ERROR);
        }
        // 增加交易ID
        $data['history_id'] = isset($gas_res['data']['id']) ? $gas_res['data']['id'] : null;
        if(!isset($data['history_id']) || empty($data['history_id'])){
            throw new \RuntimeException('',CommonError::ORDER_ERROR);
        }
        $data['showQrcode'] = 1; //不展示
        $data['qr_code'] = "";
        if( in_array($stationInfo->pcode,CommonDefine::getPayMentPcode()) ) {
            $data['showQrcode'] = 2; //展示
            /*$qrData = $this->payMentQrcode(['history_id'=>$data['history_id'],"pcode"=>$stationInfo->pcode]);
            $data['certificate_type'] = array_get($qrData,"certificate_type","");
            $data['qr_code'] = $data['certificate_resource'] = array_get($qrData,"certificate_resource","");*/
        }
        $data['isRewrite'] = 1; //是否打开新页
        if( in_array($stationInfo->pcode,CommonDefine::skipHtmlPcode()) ) {
            $data['isRewrite'] = 2;
        }

        //壳牌模式支付后，E站途不能出现核销二维码
        if( in_array($stationInfo->pcode,CommonDefine::exceptPcode()) ) {
            $data['showQrcode'] = 1;
        }

        return $data;
    }

    //生成壳牌二维码
    public function payMentQrcode(array $params)
    {
        //查询消费,获取pcode
        $info = $this->getTradeInfo($params);
        //$info = $this->getTradeById($params);
        //todo 判断运营商是否需要生成二维码
        //todo 判断是否是退款流水
        if( !$info ){
            throw new \RuntimeException('',CommonError::BILL_NO_FOUND);
        }
        if( $info->log_type == 1120 ){
            throw new \RuntimeException('',CommonError::NO_QRCODE);
        }
        if( !in_array($info->pcode,CommonDefine::getPayMentPcode()) || empty($info->pcode) )
        {
            throw new \RuntimeException('',CommonError::NO_QRCODE);
        }

        if (isset($params['pcode']) && $params['pcode']) {
            $pcode = $params['pcode'];
        }else{
            $pcode = $info->pcode;
        }

        $data = (new Adapter())->createPayMentQrcode(['order_id' => $params['history_id'], "supplier_code" => $pcode]);

        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt(['order_id' => $info->id]);

        $macAmount = array_get($orderInfo, "ext.mac_amount", 0);
        $data['qr_code'] = array_get($data, "certificate_resource", "");
        $data['trade_money'] = $macAmount > 0 ? $macAmount : $orderInfo['oil_money'];
        $data['trade_place'] = array_get($orderInfo, "ext.station_name", "");
        $data['oil_name'] = array_get($orderInfo, "ext.goods", "");
        //todo 返回商品信息和券说明
        //$coupon_flag = array_get($orderInfo,"ext.g7_coupon_flag","");
        $data['coupon_remark'] = $this->getCouponRemark($orderInfo, $pcode);

        $data['refreshBtn'] = 1; //刷新按钮不展示
        if (in_array(array_get($orderInfo, "supplier_code", ""), CouponDefine::showRefreshBtn())) {
            $data['refreshBtn'] = 2;//展示刷新按钮
        }

        return $data;
    }

    public function getCouponRemark($orderInfo, $pcode)
    {
        $coupon = array_get($orderInfo, "ext.coupons", []);
        if (!empty($coupon)) {
            if (!empty($coupon['instructions'])) {
                return $coupon['instructions'];
            } else {
                $type = $this->couponTypeRepo->getById($coupon['coupon_type_id']);
                if (!empty($type) && !empty($type->instructions)) {
                    return $type->instructions;
                }
            }
        }

        return "";
    }

    //验密及请求Gas生成加油流水
    public function createTrades(array $params)
    {
        $card_info = $this->cardService->getCardInfo($params,true);
        $trade_id = $params['pre_history_id'];
        try {
            $params['trade_id'] = $trade_id;
            unset($params['pre_history_id']);

            $gasRes = (new Gas())->finishTrades($params);

            if (empty($gasRes)) {
                throw new \RuntimeException('订单支付失败', CommonError::GAS_ERROR);
            }
            return $gasRes;
        }catch (\Exception $e) {
            $this->stationService->msg2User([
                'card_no' => $params['card_no'],
                'pre_history_id' => $trade_id,
                'msg_type' => 3,
                "code" => CommonError::GAS_ERROR,
                "message" => $e->getMessage()
            ]);
            throw new \RuntimeException($e->getMessage(), CommonError::GAS_ERROR);
        }
    }

    //车主邦预支付订单
    public function placeOrder(array $params)
    {
        $userList = (new FossUser())->getUserInfo(['uid'=>$params['user_id']]);
        if(!isset($userList['mobile']) || empty($userList) || empty($userList['mobile'])){
            throw new \RuntimeException('',CommonError::USER_ERROR);
        }

        $gun_info = $this->stationService->getGunInfo($params);

        $card_info = $this->cardService->getCardInfo($params, false);

        if( (isset($params['order_channel']) && !empty($params['order_channel'])) || CommonDefine::grayOrderOrg($card_info->gascode) ) {
            $driver_phone = '';
            $orderFlag = array_get($params, "orderFlag", CardTradeConf::G7FLAG);
            $channel = array_get($params, "order_channel", 202);
            //司机实际结算价
            $drive_price = $actual_price = $params['actual_price'];
            //如果是卡车机构需要在获取通用客户订单
            $orgcode = array_get($userList, "orgRoot", '');
            if (!empty($orgcode) ) {
                $_tmpInfo = $this->mobileCard->getInfo(['orgcode' => $orgcode, 'mobile' => $userList['mobile']]);
                if ($_tmpInfo) {
                    $driver_phone = $userList['mobile'];
                    $orderFlag = 'KC';
                    //通用客户定价
                    $actual_price = $params['price'];
                    //卡车宝贝司机，只能有一笔待支付订单
                    if( $channel == CardTradeConf::H5_ON_LINE ) {
                        $waitOrder = $this->h5Src->getWaitPayment([], $_tmpInfo->orgcode, $_tmpInfo->mobile);
                        if ($waitOrder && count($waitOrder['order']) > 0) {
                            throw new \RuntimeException("请完成上一笔订单", 32044130009);
                        }
                    }
                }
            }

            //使用统一下单接口
            $orderData = [
                'station_id' => array_get($gun_info, "station_id", ''),
                'gun_id' => $params['oilGunId'],
                'oil_money' => $params['actucal_money'],
                'actucal_money' => $params['actucal_money'],
                'order_phone' => $userList['mobile'],
                'amountGun' => $params['amountGun'],
                'gunNumber' => $params['gunNumber'],
                'priceGun' => $params['priceGun'], //枪机价
                #'price' => $params['price'],
                #'actual_price' => $params['actual_price'],
                'oil_price' => $actual_price, //卡车宝贝时,记录通用客户销价,非卡车宝贝时，记录司机实际销价
                'third_price' => $drive_price, //针对卡车宝贝，司机特殊销价
                'order_channel' => $channel,
                'order_sn' => array_get($params, "order_sn", time()),
                'operator' => $userList['mobile'],
                'operator_id' => $params['user_id'],
                'oil_type' => array_get($gun_info, "tank.oil_type", ''),
                'oil_name' => array_get($gun_info, "tank.oil_name", ''),
                'oil_level' => array_get($gun_info, "tank.oil_level", ''),
                'card_no' => array_get($params, "card_no", ""),
                'oil_num' => array_get($params, "oil_num", ""),
                'orderFlag' => $orderFlag,
                'third_pay_money' => array_get($params, "third_actucal_fee", 0),
                'driver_phone' => $driver_phone,
                'coupon_type' => array_get($params, "coupon_type", ""),
                'truck_no' => array_get($params, "truck_no", ""),
                'driver_name' => array_get($params, "driver_name", ""),
                'ocr_truck_no_id' => array_get($params, "ocr_truck_no_id", ""),
            ];
            $result = $this->newOrder->proxyOrder($orderData);
            if (isset($result['order_id']) && !empty($result['order_id'])) {
                return ['orderId' => strval($result['order_id'])];
            } else {
                throw new \RuntimeException("下单失败", 2003);
            }
        }else {

            //$priceList = $this->stationService->getOilTradePrice($params);
            $priceList = $this->stationService->getGmsOilPrice($params);

            if (bccomp($priceList['trade_price'], $params['priceGun'], 2) > 0) {
                throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付", CommonError::GUN_ERROR_PRICE);
            }

            /*if(bccomp($params['price'] ,$priceList['float_price'],2) != 0){
                throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付",CommonError::GUN_ERROR_PRICE);
            }*/

            $gas_res = (new Gas())->stationLimit([
                'orgcode' => $card_info->gascode,
                'card_no' => $card_info->card_no,
                'station_id' => $gun_info->station_id,
            ]);

            $error_message = array_get($gas_res, 'msg', null);
            if ($error_message) {
                throw new \RuntimeException($error_message, CommonError::GAS_ERROR);
            }

            $data = (new Gas())->placeOrder([
                'gun_id' => $params['oilGunId'],
                'amount' => $params['actucal_money'],
                'openid' => rand(1, 99999999),
                'order_phone' => $userList['mobile'],
                'amountGun' => $params['amountGun'],
                'gunNumber' => $params['gunNumber'],
                'priceGun' => $params['priceGun'],
                'price' => $params['price'],
                'actual_price' => $params['actual_price']
            ]);
            return $data;
        }
    }

    //车主邦支付待支付订单
    public function submitOrder(array $params)
    {
        $card_info = $this->cardService->getCardInfo($params);

        if( (isset($params['is_new']) && $params['is_new'] == 1) || CommonDefine::grayOrderOrg($card_info->gascode) ) {
            //调用支付接口
            $payData['order_id'] = $params['orderId'];
            $payData['password'] = array_get($params, "password", "");
            $payData['card_no'] = $card_info->card_no;
            $result = $this->paymentService->finishOrder($payData);
            if (isset($result['history_id']) && !empty($result['history_id'])) {
                return $result;
            } else {
                throw new \RuntimeException("支付失败", 2005);
            }

        } else {

            $gas_res = (new Gas())->stationLimit([
                'orgcode' => $card_info->gascode,
                'card_no' => $card_info->card_no,
                'station_id' => $params['station_id'],
            ]);

            $error_message = array_get($gas_res, 'msg', null);
            if ($error_message) {
                throw new \RuntimeException($error_message, CommonError::GAS_ERROR);
            }
            $from = array_get($params, "from", 202);
            $data = (new Gas)->submitOrder([
                'order_id' => $params['orderId'],
                'card_no' => $params['card_no'],
                'trade_from' => $from,
            ]);
            if (!isset($data['status']) || $data['status'] != 1) {
                throw new \RuntimeException($data['message'], CommonError::GAS_ERROR);
            }
            return isset($data['data']) ? $data['data'] : $data;
        }
    }

    //支付确认页
    public function paySure(array $params)
    {
        //$card_info = $this->cardService->getCardInfo($params,false);
        $gun_info = $this->stationService->getGunInfo($params);

        if (!$gun_info || empty($gun_info)) {
            throw new \RuntimeException('请选择油枪',CommonError::GUN_NUM);
        }

        if(!isset($gun_info->station) || empty($gun_info->station) || !isset($gun_info->tank) || empty($gun_info->tank)){
            throw new \RuntimeException('油枪信息有误',4001);
        }

        $deductUnit = 1;
        if(isset($gun_info->station->station_oil_unit) && $gun_info->station->station_oil_unit){
            $deductUnit = $gun_info->station->station_oil_unit;
        }

        if ( in_array($gun_info->station->pcode,CommonDefine::forceOilUnitPcode()) ) {
            $deductUnit = 2;
        }

        if( $deductUnit == 2 ){
            $rule = [
                'oil_num' => 'required|numeric',
            ];
            $messages = [
                'oil_num.numeric' => '请输入加油升数',
            ];
            $this->validator($params,$rule,$messages);
        }else{
            $rule = [
                'money' => 'required|numeric',
            ];
            $messages = [
                'money.numeric' => '请输入金额',
            ];
            $this->validator($params,$rule,$messages);
        }

        //判断该请求是否需要验证枪号的合法性(通过卡片所属运营商以及站点所属运营商check)
        $isOnLine = false;
        if ($gun_info->station->pcode) {
            if (in_array($gun_info->station->pcode, CommonDefine::getPcodeList())) {
                $gunNumber = array_get($params,"gunNumber","");
                if( $gunNumber == "") {
                    throw new \RuntimeException('请输入枪号',CommonError::GUN_NUM);
                }
                $responseData = $this->getAdapterGunList($gun_info->station->id,$gun_info->tank->oil_name,$gun_info->tank->oil_level,$gun_info->tank->oil_type);
                if (!in_array($gunNumber,$responseData) || empty($responseData) ) {
                    throw new \RuntimeException('输入的枪号有误，请和加油员确认枪号后重新输入',CommonError::GUN_FAIL);
                }
                $isOnLine = true;
            }

            //惠运通
            if (in_array($gun_info->station->pcode, CommonDefine::getSpecialPcode())) {
                $isOnLine = true;
            }

        }

        $gunId = array_get($params, "oilGunId", "");
        //$priceList = $this->stationService->getOilTradePrice($params);
        //使用gms价格接口
        $priceList = $this->stationService->getGmsOilPrice([
            'card_no'   => $params['card_no'],
            "oilGunId"  => $gunId,
        ]);
        $orgcode = array_get($priceList, 'orgcode', '');
        $price = $priceList['trade_price'];
        $highPrice = $priceList['macPrice']; //枪机价

        //如果是卡车机构需要在获取通用客户订单
        $_tmpInfo = $this->mobileCard->getInfo(['orgcode' => $orgcode]);
        $all_price = $origin_price = array_get($priceList, 'trade_price', 0);
        if (!empty($_tmpInfo)) {
            //通用客户定价
            $_price = $this->stationService->getGmsOilPrice(['card_no' => $params['card_no'], "oilGunId" => $gunId, "isAll" => 1]);
            $all_price = $price = array_get($_price,'trade_price',0);
            $highPrice = $_price['macPrice'];
        }

        if ($highPrice <= 0) {
            $highPrice = $price;
        }
        if (bccomp($price, $highPrice, 2) == 0) {
            $highPrice = $price;
        }

        if($deductUnit == 2){
            $oil_num = $real_oil_num = $params['oil_num'];
            $inputMoney = $oil_num * $highPrice;
        }else{
            $inputMoney = $params['money'];
            $oil_num = $inputMoney / $highPrice;
            $real_oil_num = bcdiv($inputMoney, $highPrice, 6);
        }

        if (bccomp(0, floatval($inputMoney), 2) >= 0) {
            throw new \RuntimeException('请输入金额', CommonError::ERROR_MONEY);
        }

        $sureData['truck_no'] = array_get($priceList, 'truck_no', '未知');
        $sureData['oil_name'] = array_get($params, 'oil_name', "");
        $sureData['showmsg'] = 2;//不展示提示语
        //枪机价
        $sureData['oil_price'] = number_format($highPrice, 2, ".", "");
        $sureData['oil_num'] = number_format($oil_num, 2, ".", "");
        $sureData['oil_fee'] = number_format($inputMoney, 2, ".", "");
        $actucal_fee = round(bcmul($real_oil_num, $price, 3), 2);
        $sureData['oil_discount_fee'] = bcsub($inputMoney, $actucal_fee, 2);
        if (bccomp(0, $sureData['oil_discount_fee'], 2) >= 0) {
            $sureData['oil_discount_fee'] = 0;
        }
        if (bccomp($price, $highPrice, 2) == 0) {
            $sureData['oil_actucal_fee'] = number_format($inputMoney, 2, ".", "");
        } else {
            $sureData['oil_actucal_fee'] = $actucal_fee;
        }
        $sureData['unit'] = $this->stationService->getUnit($sureData['oil_name']);

        $sureData['isClose'] = 0;
        if (bccomp($price, $highPrice, 2) > 0) {
            $sureData['isClose'] = 1;
        }

        $sureData['float_price'] = 0.00;

        $sureData['msg'] = "";
        //祥辉调价配置
        $specialList = CommonDefine::getSpecialList();
        if( count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) &&
            in_array(substr($orgcode,0,6),$specialList['SPECIAL_ORGLIST']) ) {
            $sureData['showmsg'] = 1;
            $sureData['isClose'] = 0;

            $float_price = isset($specialList['FLOAT_PRICE']) && $specialList['FLOAT_PRICE'] ? $specialList['FLOAT_PRICE'] : 0;
            $sureData['msg'] = "本次结算将收服务费（".$float_price."元/".$sureData['unit']."），如有异议请联系您的车队";
            $sureData['float_price'] = $float_price;
            $price += $float_price;
            $sureData['oil_actucal_fee'] = round(bcmul($real_oil_num, $price, 3), 2);
        }

        if( bccomp($origin_price,$all_price,2) > 0 ){
            throw new \RuntimeException("油站实际结算价格有误，请联系站点或采用其他方式支付",CommonError::GUN_ERROR_PRICE);
        }
        $sureData['third_discount'] = "0.00";
        $sureData['third_actucal_fee'] = $sureData['oil_actucal_fee'];
        if( bccomp($all_price,$origin_price,2) != 0) {
            $sureData['third_actucal_fee'] = round(bcmul($real_oil_num, $origin_price, 3), 2);
            $sureData['third_discount'] = bcsub($sureData['oil_actucal_fee'], $sureData['third_actucal_fee'], 2);
        }

        //司机实际结算价（针对下游可以有特殊定价）
        $sureData['driver_price'] = $origin_price;

        //G7平台通用客户定价
        $sureData['plaform_price'] = $all_price;

        if( (isset($params['is_createOrder']) && $params['is_createOrder'] == 1) || CommonDefine::grayOrderOrg($orgcode) ){
            //统一使用先下单再支付的模式
            $sureData['tradeByOrder'] = 'on';
        }else{
            $sureData['tradeByOrder'] = $isOnLine ? 'on' : 'off';
        }
        return $sureData;
    }

    //获取影响活动开关
    public function getActSwitch($params = [])
    {
        $switch = "off";
        $act = $this->actReponsitory->getInfoByCode(['uniq_code'=>'YDCJHD-2021','is_del'=>0]);
        $history_id = array_get($params,"history_id","");
        if(!empty($act)){
            $condition = json_decode($act->condition,true);
            if( !empty($history_id) ){
                if(count($condition) > 1 && time() >= strtotime($act->start_time) && time() <= strtotime($act->end_time) ) {
                    $money = array_get($condition,"money",500);
                    $day = array_get($condition,"expireDay",1);
                    $nowStart = date("Y-m-d") . " 00:00:00";
                    $nowEnd = date("Y-m-d", strtotime("+" . $day . "day")) . " 00:00:00";
                    $tradeInfo = $this->stationService->getTradeByIdWithZbank($params);
                    if( !empty($tradeInfo) ) {
                        if (bccomp($tradeInfo->trade_money, $money, 2) >= 0 &&
                            strtotime($tradeInfo->createtime) >= strtotime($nowStart) && strtotime($tradeInfo->createtime) < strtotime($nowEnd) &&
                            $tradeInfo->is_pay == 1) {
                            $switch = "on";
                        }
                    }
                }
            }else{
                if( time() >= strtotime($act->start_time) && time() <= strtotime($act->end_time) ){
                    $switch = "on";
                }
            }
        }

        /*$_tmp = env("BANNERLIST");
        $banner = json_decode($_tmp,true);*/
        $banner = CommonDefine::getBannerList();

        $res = ["actSwitch" => $switch,'banner'=>$banner];

        $notice = $this->getServiceNotice($params['user_id']);
        if (! empty($notice)) {
            $res['indexNotice'] = $notice;
        }


        return $res;
    }

    protected function getServiceNotice($userId)
    {
        $notice = config("service_notice.index_notice");

        if (empty($notice))
            return [];

        if ($notice['times'] > 0) {

            // 验证白名单内的手机号
            $user = (new FossUser())->getUserInfo(['uid'=>$userId]);

            if (CommonService::isMobileInWhiteList($user['mobile'])) {
                return [];
            }
        }

        return $notice;
    }

    //获取车主邦油枪列表
    public function getAdapterGunList($station_id,$oil_name,$oil_level,$oil_type)
    {
        //$gun_info->station->id,
        //$gun_info->tank->oil_name,
        //$gun_info->tank->oil_level,
        //$gun_info->tank->oil_type,
        //调用对接服务查询油品对应枪号
        $responseData = (new Adapter())->checkGun([
            'stationId' => $station_id,
            'oilTypeId' => $oil_name,
            'oilLevelId' => $oil_level,
            'oilNoId' => $oil_type
        ]);
        return $responseData;
    }
}