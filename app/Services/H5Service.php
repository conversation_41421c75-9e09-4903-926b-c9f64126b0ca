<?php

/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Services;

use App\Exceptions\ParamInvalidException;
use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Models\DriverRefundApplicationRecordModel;
use App\Models\OrderModel;
use App\Repositories\NewOrderRepository;
use App\Repositories\OilMobileCardRepository;
use App\Servitization\Adapter;
use App\Servitization\FeiShu;
use App\Servitization\FossUser;
use App\Servitization\TruckBaby;
use Exception;
use Illuminate\Support\Facades\DB;
use Library\Monitor\Falcon;
use RuntimeException;
use Throwable;

class H5Service
{
    private $mobileCardRepository;
    private $newOrder;
    protected $feishu;
    protected $paymentService;
    protected $messageService;
    protected $orderService;
    protected $cache;
    protected $newOrderRepository;

    protected $couponSrv;

    public function __construct(OilMobileCardRepository $card, NewOrderRepository $order, FeiShu $feishu, PaymentService $payment,
                                MessageService $msg, NewOrderService $orderSrv, RedisService $redis, NewOrderRepository $newOrderRepository, CouponService $couponSrv)
    {
        $this->mobileCardRepository = $card;
        $this->newOrder = $order;
        $this->feishu = $feishu;
        $this->paymentService = $payment;
        $this->messageService = $msg;
        $this->orderService = $orderSrv;
        $this->cache = $redis;
        $this->couponSrv = $couponSrv;
    }

    //查询待支付订单
    public function getWaitPayment($params = [],$orgcode = '',$mobile = "")
    {
        if( empty($orgcode) || empty($mobile) ) {
            //请求foss-user得到手机号
            $userList = (new FossUser())->getUserInfo(['uid' => $params['user_id']]);
            if (!isset($userList['mobile']) || empty($userList) || $userList['mobile'] == "") {
                throw new RuntimeException('', CommonError::USER_ERROR);
            }
            $orgcode = array_get($userList,"orgRoot","");
            $mobile = array_get($userList,"mobile","");
        }
        //todo 临时增加
        //$userList['orgRoot'] = "200SD5";
        //$userList['mobile'] = '18610390918';
        if ( !empty($orgcode) ){
            $info = $this->mobileCardRepository->getInfo( ['orgcode'=>$orgcode,'mobile'=>$mobile] );
            if( $info && $info->vice_no ){
                $condition['eq_card_no'] = $info->vice_no;
                $condition['eq_driver_phone'] = $info->mobile;
                $condition['eq_order_status'] = 1;
                $condition['eq_order_flag'] = CardTradeConf::TRUCKFLAG;
                $orderInfo = $this->newOrder->getOrderInfoWithExt($condition);
                if( $orderInfo){
                    $station_name = array_get($orderInfo,'ext.station_name','');
                    $res = [
                        'order'     =>[
                            'order_no'     => strval($orderInfo['order_id']),
                            'createtime'   => $orderInfo['create_time'],
                            'pay_time'     => $orderInfo['pay_time'],
                            'station_id'   => $orderInfo['station_id'],
                            'station_name' => $station_name,
                            'remark_name'  => array_get($orderInfo,'ext.remark_name',''),
                            'oil_name'     => array_get($orderInfo,'ext.goods',''),
                            'oil_money'    =>$orderInfo['oil_money'],
                            'oil_price'    =>$orderInfo['oil_price'],
                            'oil_num'      =>$orderInfo['oil_num'],
                            'order_flag'   => $orderInfo['order_flag'],
                            'status'       => $orderInfo['order_status'],
                            'status_txt'   => '未完成',
                        ],
                        'orderFlag' => CardTradeConf::TRUCKFLAG,
                    ];
                    if(empty($res['order']['remark_name'])){
                        $res['order']['remark_name'] = $station_name;
                    }
                    return $res;
                }else{
                    return ['order'=>[],"orderFlag"=>CardTradeConf::TRUCKFLAG];
                }
            }
        }
        return ['order'=>[],"orderFlag"=>CardTradeConf::G7FLAG];
    }

    //三方查询订单详情
    public function orderDetail($params = [])
    {
        $condition['eq_order_id'] = $params['order_no'];
        $condition['left_org_code'] = $params['org_code'];
        $orderInfo = $this->newOrder->getOneOrderByParams($condition);
        if (!$orderInfo) {

            throw new RuntimeException("订单不存在", CommonError::BILL_NO_FOUND);
        }
        return $this->getOrderData($orderInfo);
    }

    //三方查询订单列表信息
    public function orderList(array $params = []): array
    {
        $orderInfo = $this->newOrder->getBatchOrderByParams([
            'order_id'      => $params['order_nos'],
            'left_org_code' => $params['org_code'],
        ]);
        if (!$orderInfo) {

            throw new RuntimeException("订单不存在", CommonError::BILL_NO_FOUND);
        }

        foreach ($orderInfo as &$v) {

            $v = $this->getOrderData($v);
        }
        return $orderInfo;
    }

    public function getOrderData(array $orderInfo): array
    {
        $kcData = [];
        $kcData['order_no'] = strval($orderInfo['order_id']);
        $kcData['station_code'] = $orderInfo['station_code'];
        $kcData['station_name'] = array_get($orderInfo, 'ext.station_name', '');
        $kcData['payable'] = floatval($orderInfo['oil_money']);
        $kcData['pay_amount'] = floatval($orderInfo['third_actual_fee']);
        $kcData['site_price'] = floatval($orderInfo['oil_price']);
        $kcData['dirver_price'] = floatval($orderInfo['third_actual_fee']);
        $kcData['oil_num'] = floatval($orderInfo['oil_num']);
        if (stripos(array_get($orderInfo, 'ext.oil_name_name', ''), "尿素") !== false) {
            $kcData['type'] = array_get($orderInfo, 'ext.oil_type_name', '');
            $kcData['gas_oil'] = 4;
        } elseif (stripos(array_get($orderInfo, 'ext.oil_name_name', ''), "柴油") !== false) {
            $kcData['type'] = array_get($orderInfo, 'ext.oil_type_name', '');
            $kcData['gas_oil'] = 3;
        } elseif (stripos(array_get($orderInfo, 'ext.oil_name_name', ''), "天然气") !== false) {
            $kcData['type'] = array_get($orderInfo, 'ext.oil_name_name', '') == '液化天然气' ? 'LNG' : 'CNG';
            $kcData['gas_oil'] = 2;
        } else {
            $kcData['type'] = array_get($orderInfo, 'ext.oil_type_name', '');
            $kcData['gas_oil'] = 1;
        }
        $kcData['goods_name'] = array_get($orderInfo, 'ext.goods', '');
        $kcData['status'] = $orderInfo['order_status'];
        $kcData['card_no'] = $orderInfo['card_no'];
        $kcData['user_phone'] = $orderInfo['driver_phone'];
        return $kcData;
    }

    //刷新订单获取订单详情
    public function reloadOrder($params = [])
    {
        //todo 请求卡车订单状态接口
        //todo 对比卡车订单状态与本地账单状态
        $condition['eq_order_id'] = $params['order_no'];
        //$condition['eq_order_flag'] = array_get($params,'orderFlag',"KC");
        $orderInfo = $this->newOrder->getOneOrderByParams($condition);
        if( !$orderInfo ){
            throw new RuntimeException("订单不存在", CommonError::BILL_NO_FOUND);
        }

        $station_name = array_get($orderInfo,'ext.station_name','');
        $resData = [
            'order_no'     => strval($orderInfo['order_id']),
            'createtime'   => $orderInfo['create_time'],
            'pay_time'     => $orderInfo['pay_time'],
            'station_id'   => $orderInfo['station_id'],
            'station_name' => $station_name,
            'remark_name'  => array_get($orderInfo,'ext.remark_name',''),
            'oil_name'     => array_get($orderInfo,'ext.goods',''),
            'oil_money'    =>$orderInfo['oil_money'],
            'oil_price'    =>$orderInfo['oil_price'],
            'oil_num'      =>$orderInfo['oil_num'],
            'status'       => $orderInfo['order_status'],
            'order_flag'   => $orderInfo['order_flag'],
            'status_txt'   => '未完成',
            'third_status' => 1,
        ];
        if( empty($resData['remark_name']) ){
            $resData['remark_name'] = $station_name;
        }

        if($orderInfo['order_flag'] == CardTradeConf::TRUCKFLAG){
            $thirdRes = $this->getThirdStatus($orderInfo['order_id']);
            $resData['third_status'] = $thirdRes['thirdStatus'];
            if( !empty($thirdRes['history_id']) ){
                $orderInfo['order_status'] = 2;
                $resData['status_txt'] = '已支付';
            }
        }

        //订单成功后，前端调整标识
        /*自有站二维码,val:1
        壳牌二维码,val:2
        万金油二维码,val:3
        支付成功页,val:4*/
        $type = 4;
        if( in_array( $orderInfo['ext']['trade_type'],[3,4]) ){
            if( in_array($orderInfo['supplier_code'],CommonDefine::getPayMentPcode()) ) {
                $type = 2;
                if( in_array($orderInfo['supplier_code'],CommonDefine::skipHtmlPcode()) ) {
                    $type = 3;
                }
            }
            if( in_array($orderInfo['supplier_code'],CommonDefine::exceptPcode()) ){
                $type = 4;
            }
            if( $orderInfo['order_status'] == 2 ){
                $resData['rewriteType'] = $type;
            }else{
                $resData['rewriteType'] = 10; //订单非支付成功
            }
        }elseif($orderInfo['ext']['trade_type'] == 1){
            if( in_array($orderInfo['order_status'],[1,2]) ) {
                $resData['rewriteType'] = 1;
            }else{
                $resData['rewriteType'] = 10; //订单非支付成功
            }
        }
        $resData['unit'] = CommonDefine::getOilUnit($resData['oil_name']);
        return $resData;
    }

    /**
     * @param array $params
     * @return array
     * 查询三方订单状态
     */
    public function getThirdStatus( $order_id = '')
    {
        try {
            //三方支付状态，（0未支付 1已支付 2已退款 3取消）
            $thirdOrder = (new TruckBaby())->getDetail(['id' => strval($order_id)]);
            $thirdCode = intval(array_get($thirdOrder, "statusCode", 0));
            $history_id = "";
            if ($thirdCode == 1) {
                $res = $this->payCallBack(['order_no' => $order_id, 'third_order_id' => $order_id], true);
                $history_id = array_get($res, "history_id", "");
            }
            return ['thirdStatus' => $thirdCode, "history_id" => $history_id];
        } catch (Exception $exception) {
            $code = $exception->getCode();
            if ($code == '10003') {//订单不存在
                return ['thirdStatus' => 0, "history_id" => ''];
            }
            throw new RuntimeException($exception->getMessage(), $code);
        }
    }

    //取消订单
    public function cancelOrder($params = [])
    {
        $condition['eq_order_id'] = $params['order_no'];
        $condition['eq_order_flag'] = array_get($params,'orderFlag',CardTradeConf::TRUCKFLAG);
        $orderInfo = $this->newOrder->getOneOrderByParams($condition);
        if( !$orderInfo ){
            throw new RuntimeException("订单不存在", CommonError::BILL_NO_FOUND);
        }

        if ($orderInfo['order_status'] == 2) {
            throw new RuntimeException("订单已支付不允许取消", 40001);
        }

        if ($orderInfo['order_status'] == 4) {
            return ['status' => $orderInfo['order_status'], "message" => '取消成功'];
        }

        if ($orderInfo['order_status'] == 6 || $orderInfo['order_status'] == 3) {
            return ['status' => $orderInfo['order_status'], "message" => '订单状态已变更'];
        }

        try {
            if ($orderInfo['order_flag'] == CardTradeConf::TRUCKFLAG) {
                $thirdRes = $this->getThirdStatus($orderInfo['order_id']);
                $thirdStatus = $thirdRes['thirdStatus'];
                if( !empty($thirdRes['history_id']) ){
                    throw new RuntimeException("订单已支付成功，请刷新重试", 40008);
                }
                if ($thirdStatus == 1) {
                    $cancelRes = (new TruckBaby())->refundOrder(['updateBy' => '系统', 'id' => strval($orderInfo['order_id'])]);
                    $cancelCode = array_get($cancelRes, 'code', 10003);
                    if ($cancelCode == 0) {
                        $this->orderService->cancelOrder(['order_id' => $params['order_no']]);
                    }
                } else {
                    $this->orderService->cancelOrder(['order_id' => $params['order_no']]);
                }
            }
        } catch (Exception $e) {
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return ['order_no'=>$params['order_no'],'message'=>'成功'];
    }

    //支付回调
    public function payCallBack($params = [],$refrese = false)
    {
        if( !$refrese ) {
            $this->feishu->sendExceptionToFeiShu(new Exception("收到三方服务支付回调，参数：" . json_encode($params), "1001"));
        }
        $cacheKey = $params['order_no']."-callBack";
        try {
            $condition['eq_order_id'] = $params['order_no'];
            $condition['eq_order_flag'] = array_get($params,'orderFlag',CardTradeConf::TRUCKFLAG);
            $orderInfo = $this->newOrder->getOneOrderByParams($condition);
            if (!$orderInfo) {
                throw new RuntimeException("订单不存在", CommonError::BILL_NO_FOUND);
            }
            //订单状态 1待支付 2支付成功 3支付失败 4订单取消 5退款中 6已退款
            if ($orderInfo['order_status'] == 2) {
                return ['status' => $orderInfo['order_status'], "message" => '支付成功','history_id'=>$orderInfo['history_id']];
            }
            /*if ($orderInfo['order_status'] == 3) {
                return ['status' => $orderInfo['order_status'], "message" => '订单支付失败','history_id'=>''];
            }*/
            if ($orderInfo['order_status'] != 1) {
                return ['status' => $orderInfo['order_status'], "message" => '订单状态已变更','history_id'=>''];
                //throw new RuntimeException("订单状态已变更:" . $orderInfo['order_status'] . ",order_id:" . $params['order_no'], 40001);
            }
            $existsPay = $this->cache->tryGetLock($cacheKey,strval($params['order_no']),6);
            if ( !$existsPay ) {
                throw new RuntimeException('支付中，请稍后重试!', 40013);
            }
            if ( isset($params['money']) && bccomp($orderInfo['third_actual_fee'], $params['money'], 2) != 0) {
                throw new RuntimeException("订单金额不一致:" . $orderInfo['oil_money'], 40002);
            }
            if (isset($params['user_phone']) && trim($params['user_phone']) != trim($orderInfo['driver_phone'])) {
                throw new RuntimeException("手机号不一致", 40003);
            }

            $trade_type = isset($orderInfo['ext']) && isset($orderInfo['ext']['trade_type']) ? $orderInfo['ext']['trade_type'] : 1;
            if( !$refrese ) {
                $channel = $orderInfo['order_channel'];
                if ($trade_type == 1) {
                    $channel = CardTradeConf::H5_PAY_CODE;
                }
                $mode = $this->orderService->getTradeMode($trade_type, array_get($orderInfo, 'supplier_code', ''), array_get($orderInfo, 'driver_source', 1));
                //更新订单状态
                $this->newOrder->updateOrderByOrderId($params['order_no'], [
                    #'third_order_id'=>$params['third_order_id'], //卡车的订单号与g7一样，不记录
                    'order_channel' => $channel,
                    'trade_mode'    => $mode,
                    'update_time'   => date("Y-m-d H:i:s", time())
                ]);
            }

            //1:扫一扫|2:感应卡|3:主动付款|4:小票机
            if( in_array($trade_type,[3,4]) ){
                $data['order_id'] = $params['order_no'];
                $data['third_order_id'] = $params['third_order_id'];
                $data['kcCallBack'] = 1;
                //为了保证卡车主卡资金安全，必须把主卡设置成验密，支付时不能验密
                $result = $this->paymentService->finishOrder($data);
                /*//todo 通知卡车支付结果
                $conf['apiUrl'] = '';
                $conf['app_secret'] = '';
                $baby = (new TruckBaby($conf));
                $baby->sendPayResult([]);*/
                $this->cache->releaseLock($cacheKey, strval($params['order_no']));
                return $result;
            } elseif (in_array($trade_type, [1])) {
                $this->cache->releaseLock($cacheKey, strval($params['order_no']));
                return ['status' => $orderInfo['order_status'], "message" => '出示付款码,待收银员扫码'];
            }
        } catch (Exception $e) {
            $this->feishu->sendExceptionToFeiShu($e);
            $this->cache->releaseLock($cacheKey, strval($params['order_no']));
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * @param array $params
     * 向三方推送订单
     */
    public function pushOrder2Third($params = [])
    {
        $condition['order_id'] = $params['order_no'];
        $orderInfo = $this->newOrder->getOrderInfoWithExt($condition);
        $pushType = array_get($params,"type",'OA');
        $pushData = [];
        if($pushType == CardTradeConf::TRUCKFLAG){
            $kcData['id'] = strval($orderInfo['order_id']);
            $kcData['siteId'] = $orderInfo['station_code'];
            $kcData['siteName'] = array_get($orderInfo,'ext.station_name','');
            $kcData['payable'] = floatval($orderInfo['oil_money']);
            $kcData['payAmount'] = floatval($orderInfo['third_actual_fee']);
            $kcData['sitePrice'] = floatval($orderInfo['oil_price']);
            $kcData['g7KcPrice'] = floatval($orderInfo['third_actual_fee']);
            $kcData['num'] = floatval($orderInfo['oil_num']);
            if(stripos(array_get($orderInfo,'ext.oil_name_name',''),"尿素") !== false){
                $kcData['type'] = array_get($orderInfo,'ext.oil_type_name','');
                $kcData['gasOil'] = 4;
            }elseif(stripos(array_get($orderInfo,'ext.oil_name_name',''),"柴油") !== false){
                $kcData['type'] = array_get($orderInfo,'ext.oil_type_name','');
                $kcData['gasOil'] = 3;
            }elseif(stripos(array_get($orderInfo,'ext.oil_name_name',''),"天然气") !== false){
                $kcData['type'] = array_get($orderInfo,'ext.oil_name_name','') == '液化天然气' ? 'LNG' : 'CNG';
                $kcData['gasOil'] = 2;
            }else{
                $kcData['type'] = array_get($orderInfo,'ext.oil_type_name','');
                $kcData['gasOil'] = 1;
            }
            $kcData['userPhone'] = $orderInfo['driver_phone'];
            $this->messageService->pushOrder2KC($kcData);
        }else{
            $message_type = 'PAY_LOG';
            if($orderInfo['driver_source'] == 2) { //下游订单
                $pushData = [
                    'id'               => strval($orderInfo['order_id']),
                    'truck_no'         => array_get($orderInfo,'truck_no',''),
                    'station_id'       => $orderInfo['station_id'],
                    'station_name'     => $orderInfo['station_name'],
                    'oil_num'          => $orderInfo['oil_num'],
                    'price'            => $orderInfo['oil_price'],
                    'money'            => $orderInfo['oil_money'],
                    'pay_price'        => $orderInfo['oil_price'],
                    'pay_money'        => $orderInfo['oil_money'],
                    'xpcode_pay_price' => array_get($orderInfo,'ext.supplier_price',0), // 进价
                    'xpcode_pay_money' => round(bcmul(array_get($orderInfo,'ext.supplier_price',0), array_get($orderInfo,'real_oil_num',0), 3), 2),
                    'card_no'          => array_get($orderInfo,'card_no',''),
                    'orgcode'          => array_get($orderInfo,'org_code',''),
                    'oil_name_val'     => array_get($orderInfo,'ext.oil_name_name',''),
                    'oil_type_val'     => array_get($orderInfo,'ext.oil_type_name',''),
                    'oil_level_val'    => array_get($orderInfo,'ext.oil_level_name',''),
                    'oil_name'         => array_get($orderInfo,'oil_name',''),
                    'oil_type'         => array_get($orderInfo,'oil_type',''),
                    'oil_level'        => array_get($orderInfo,'oil_level',''),
                    'extends'          => [],
                    'oil_time'         => date('Y-m-d H:i:s')
                ];
            }elseif( in_array($orderInfo['supplier_code'],CommonDefine::getSpecialPcode()) || in_array($orderInfo['supplier_code'],CommonDefine::getPcodeList()) ) { //车主邦订单
                $message_type = 'ONLINE_PAY_ORDER';
                $amountGun = array_get($params, 'amountGun', 0);
                $priceGun = array_get($params, 'priceGun', 0);
                $gunNumber = array_get($params, 'gunNumber', '');
                $actual_price = array_get($params, 'actual_price', 0);
                $pushData = [
                    'id'           =>  strval($orderInfo['order_id']),
                    'truck_no'     => array_get($orderInfo,'truck_no',''),
                    'driver_name'     => array_get($orderInfo,'driver_name',''),
                    'drivertel'    =>  array_get($orderInfo,'driver_phone',''),
                    'pcode'        =>$orderInfo['supplier_code'],
                    'oil_type_id'  =>  array_get($orderInfo,'oil_type',''),
                    'oil_name_id'  =>  array_get($orderInfo,'oil_name',''),
                    'oil_level_id' =>  array_get($orderInfo,'oil_level',''),
                    'trade_money'  => $orderInfo['oil_money'],//消费金额
                    'trade_price'  => $orderInfo['oil_price'],//油品单价
                    'station_id'   => $orderInfo['station_id'],//站点id
                    'pushExtends'  => json_encode([
                        'amountGun' => $amountGun,
                        'price'     => $actual_price,
                        'priceGun'  => $priceGun,
                        'gunNumber' => $gunNumber
                    ]),
                ];
            } elseif ( in_array($orderInfo['supplier_code'],CommonDefine::getPayMentPcode()) ){ //一键付订单
                $pushData['id'] = strval($orderInfo['order_id']);
                $pushData['drivertel'] = array_get($orderInfo,'driver_phone','');
                $pushData['oil_type_id'] = array_get($orderInfo,'oil_type','');
                $pushData['oil_name_id'] = array_get($orderInfo,'oil_name','');
                $pushData['oil_level_id'] = array_get($orderInfo,'oil_level','');
                $pushData['station_id'] = $orderInfo['station_id'];
                $pushData['vice_no'] = array_get($orderInfo,'card_no','');
                $pushData['trade_price'] = $orderInfo['oil_price'];
                $pushData['trade_money'] = $orderInfo['oil_money'];
                $pushData['trade_num'] = $orderInfo['oil_num'];
                $pushData['trade_place'] = array_get($orderInfo,'ext.station_name','');
                $pushData['trade_place_provice_name'] = array_get($orderInfo,'ext.province_name','');
                $pushData['trade_place_city_name'] = array_get($orderInfo,'ext.city_name','');
                $pushData['lng'] = array_get($orderInfo, 'ext.lng', '');
                $pushData['lat'] = array_get($orderInfo, 'ext.lat', '');
                $pushData['trade_time'] = array_get($orderInfo,'ext.oil_time','');
                $pushData['app_station_id'] = array_get($orderInfo,'ext.app_station_id','');
                $pushData['pcode'] = $orderInfo['supplier_code'];
                $pushData['supplier_pay_price'] = array_get($orderInfo,'ext.supplier_price','');
                $pushData['trades_no'] = array_get($orderInfo,'trade_no','');
                $pushData['truck_no'] = array_get($orderInfo,'truck_no','');
                $pushData['driver_name'] = array_get($orderInfo,'driver_name','');
                $pushData['oil_name'] = array_get($orderInfo,'ext.goods','');
                $pushData['org_name'] = array_get($orderInfo,'ext.org_name','');
                $extends['price'] = $orderInfo['oil_price'];
                $extends['priceGun'] = array_get($orderInfo,'ext.mac_price','');
                $extends['amountGun'] = array_get($orderInfo,'ext.mac_amount','');
                $extends['driver_source'] = 1;
                if( in_array($orderInfo['order_channel'],[CardTradeConf::H5_TICKET,CardTradeConf::H5_ON_LINE,CardTradeConf::WMP_TICKET,CardTradeConf::WMP_ON_LINE]) ){
                    $extends['driver_source'] = 2;
                }
                $pushData['pushExtends'] = $extends;
                $pushData['oil_unit'] = $orderInfo['oil_unit'];
                $third_coupon_flag = array_get($orderInfo, 'ext.third_coupon_flag', '');
                if (!empty($coupon_flag)) {
                    $couponG7 = $couponInfo = $this->couponSrv->checkCouponExist($coupon_flag);
                    $coupon_alias = array_get($couponInfo, "coupon_type_alias", "");
                    $third_coupon_flag = $couponG7->tripartite_voucher;
                }
                $pushData['coupon_alias'] = $coupon_alias;
                $pushData['tripartite_voucher'] = $third_coupon_flag;
                $message_type = "AUTONOMOUS_ORDER";
            }
            $this->messageService->pushData2OA($pushData, $message_type);
        }
        return $pushData;
    }

    /**
     * @param array $params
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 3:40 下午
     */
    public function addDriverRefundApplicationRecord(array $params)
    {
        DB::connection("mysql_gas")->beginTransaction();
        $orderInfo = $this->newOrder->getOneOrderByParams([
            'order_id' => $params['order_no'],
            'org_code' => $params['orgcode'],
        ]);
        if (empty($orderInfo)) {

            throw new RuntimeException("", CommonError::BILL_NO_FOUND);
        }
        try {

            $record = DriverRefundApplicationRecordModel::getOneRecord([
                'order_no'        => $params['order_no'],
                'approval_status' => [
                    DriverRefundApplicationRecordModel::WAIT_REVIEW,
                    DriverRefundApplicationRecordModel::REVIEW_PASSED_AND_REFUND_SUCCEED,
                ],
            ], ['id']);
            if ($record) {

                throw new RuntimeException("", CommonError::ORDER_REFUND_RECORD_ALREADY_EXISTS);
            }
            DriverRefundApplicationRecordModel::add([
                'order_no'           => $params['order_no'],
                'platform_order_no'  => $params['platform_order_no'],
                'applicant'          => $params['applicant'],
                'approval_status'    => 10,
                'application_reason' => $params['application_reason'],
                'created_at'         => date('Y-m-d H:i:s'),
            ]);
            $text = "客户退款申请单\n";
            $text .= "机构：{$orderInfo['ext']['org_name']}\n";
            $text .= "退款金额：{$orderInfo['oil_money']}\n";
            $text .= "退款油站：{$orderInfo['ext']['station_name']}\n";
            $text .= "G7订单号：{$orderInfo['order_id']}\n";
            $text .= "退款原因：{$params['application_reason']}\n";
            $text .= "申请人：{$params['applicant']}\n";
            $text .= "油站供应商：{$orderInfo['ext']['supplier_name']}\n";
            $text .= "备注：如果是上游供应商，请及时联系上游供应商进行退款！";
            Falcon::feishu(config("feishu")["trade_refund"], $text);
            DB::connection("mysql_gas")->commit();
        } catch (Throwable $throwable) {

            DB::connection("mysql_gas")->rollBack();
            throw $throwable;
        }
    }

    /**
     * @param array $params
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/6 3:40 下午
     */
    public function cancelOrderForPlatform(array $params)
    {
        DB::connection("mysql_gas")->beginTransaction();
        $orderRecord = $this->newOrderRepository->getOrderInfoByLock([
            'order_id'      => $params['order_no'],
            'left_org_code' => $params['org_code'],
        ], ['order_status', 'supplier_code'], false);
        if (!$orderRecord) {

            throw new RuntimeException("", CommonError::BILL_NO_FOUND);
        }
        if ($orderRecord['order_status'] != OrderModel::WAIT_PAY) {

            throw new RuntimeException("", CommonError::CANNOT_CANCEL_NON_PENDING_ORDERS);
        }
        $this->newOrderRepository->updateOrderByOrderId($params['order_no'], [
            'order_status' => OrderModel::CANCEL,
        ]);
        DB::connection("mysql_gas")->commit();
    }

    /**
     * @param array $params
     * @return array
     * @throws ParamInvalidException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/8 4:19 下午
     */
    public function createOrder(array $params): array
    {
        $params['driver_source'] = 1;
        $params['order_channel'] = 801;
        $params['order_sn'] = Common::getOnlyId();
        $params['open_api_org_code'] = $params['org_code'];
        $params['priceGun'] = $params['price_gun'] ?? 0.00;
        $params['gunNumber'] = $params['gun_number'] ?? '';
        $params['amountGun'] = $params['amount_gun'] ?? 0.00;
        $params['operator'] = $params['access_id'];
        $params['operator_id'] = $params['access_id'];
        $params['orderFlag'] = 'OPEN_API';
        $cardInfo = (new FossUser())->getCardInfoWithCurrAccount([
            'card_no' => $params['card_no'],
        ]);
        // 如开放平台客户机构代码与卡所属机构代码非平级及子级关系则不允许下单
        if ($params['org_code'] != substr($cardInfo['orgcode'], 0, strlen($params['org_code']))) {

            throw new RuntimeException(CommonError::$codeMsg[CommonError::INVALID_CARD_NO],
                CommonError::INVALID_CARD_NO);
        }
        $orderData = $this->orderService->proxyOrder($params);
        return [
            'order_no' => $orderData['order_id'],
        ];
    }

    /**
     * @param array $params
     * @return array
     * @throws Exception
     * @since 2021/4/8 4:19 下午
     * <AUTHOR> <<EMAIL>>
     */
    public function payOrder(array $params): array
    {
        $errorCodeService = new ErrorCodeService();
        $orderInfo = $this->newOrder->getOrderInfoWithExt([
            'order_id'      => $params['order_no'],
            'left_org_code' => $params['org_code'],
        ]);
        if (empty($orderInfo)) {

            throw new RuntimeException('支付失败,订单号不存在!', $errorCodeService->gotoPay(40302));
        }
        if (!in_array($orderInfo['trade_mode'], CardTradeConf::$trade_mode_smp)) {

            $params['order_id'] = $params['order_no'];
            $params['third_order_id'] = $orderInfo['third_order_id'];
            $this->paymentService->finishOrder($params);
            if (!in_array($orderInfo['trade_mode'], CardTradeConf::$trade_mode_need_write_off) or
                (in_array($orderInfo['trade_mode'], CardTradeConf::$trade_mode_need_write_off) and
                    in_array($orderInfo['supplier_code'], CommonDefine::exceptPcode()))) {

                return [
                    'show_verification_certificate' => 1,
                    'certificate_type'              => '',
                    'certificate_resource'          => '',
                ];
            }
        }
        $fossUser = new FossUser();
        // 验账号
        $cardInfo = $fossUser->getCardInfoWithCurrAccount([
            'card_no' => $orderInfo['card_no'],
        ]);
        if (empty($cardInfo)) {

            throw new RuntimeException('验密失败:账号错误!', $errorCodeService->gotoPay(40320));
        }
        if ($cardInfo['ischeck'] == 1) {

            try {
                // 验密
                $fossUser->checkPassword([
                    'vice_no'  => $orderInfo['card_no'],
                    'password' => $params['password']
                ]);
            } catch (Exception $e) {
                // G7能源账户锁定(锁卡)
                if ($e->getCode() == 3013102) {

                    $this->newOrderRepository->updateOrderByOrderId($orderInfo['order_id'], [
                        'order_status' => OrderModel::FAIL_PAY,
                        'remark'       => $e->getMessage(),
                        'updator'      => '系统'
                    ]);
                }
                throw $e;
            }
        }
        return $this->generateVerificationCertificate($orderInfo['order_id'], $orderInfo['trade_mode'],
            $orderInfo['card_no'], $orderInfo['supplier_code']);
    }

    /**
     * @param $orderId
     * @param $tradeMode
     * @param $cardNo
     * @param $supplierCode
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/9 6:11 下午
     */
    public function generateVerificationCertificate($orderId, $tradeMode, $cardNo, $supplierCode): array
    {
        if (in_array($tradeMode, CardTradeConf::$trade_mode_need_write_off) and
            in_array($supplierCode, CommonDefine::exceptPcode())) {

            return [
                'show_verification_certificate' => 1,
                'certificate_type'              => '',
                'certificate_resource'          => '',
            ];
        }
        if (in_array($tradeMode, CardTradeConf::$trade_mode_smp)) {

            $verificationCertificate = (new FossUser())->getVerificationCertificate([
                'card_no'  => $cardNo,
                'order_no' => $orderId,
                'source'   => 'oa',
                'pcode'    => $supplierCode,
            ]);
            return [
                'show_verification_certificate' => 2,
                'certificate_type'              => $verificationCertificate['certificate_type'],
                'certificate_resource'          => $verificationCertificate['certificate_resource'],
            ];
        }

        $verificationCertificate = (new Adapter())->createPayMentQrcode([
            'order_id'      => $orderId,
            'supplier_code' => $supplierCode,
        ]);
        return [
            'show_verification_certificate' => 2,
            'certificate_type'              => $verificationCertificate['certificate_type'],
            'certificate_resource'          => $verificationCertificate['certificate_resource'],
        ];
    }

    /**
     * @param array $params
     * @return array
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/9 6:23 下午
     */
    public function refreshVerificationCertificate(array $params): array
    {
        $errorCodeService = new ErrorCodeService();
        DB::connection("mysql_gas")->beginTransaction();
        $orderInfo = $this->newOrder->getOrderInfoByLock([
            'order_id'      => $params['order_no'],
            'left_org_code' => $params['org_code'],
        ]);
        if (empty($orderInfo)) {

            throw new RuntimeException('刷新核销二维码失败,订单号不存在!', $errorCodeService->gotoPay(40330));
        }
        if (in_array($orderInfo['trade_mode'], CardTradeConf::$trade_mode_smp) and
            $orderInfo['order_status'] != OrderModel::WAIT_PAY) {

            throw new RuntimeException('刷新核销二维码失败,订单状态异常!', $errorCodeService->gotoPay(40331));
        }
        if ($orderInfo['trade_mode'] == CardTradeConf::TRADE_MODE_SYZ_ZYS_ZDP_YJF and
            in_array($orderInfo['order_status'], [
                OrderModel::CANCEL,
                OrderModel::FAIL_PAY,
                OrderModel::REFUND,
                OrderModel::REFUNDING,
            ])) {

            throw new RuntimeException('刷新核销二维码失败,订单状态异常!', $errorCodeService->gotoPay(40331));
        }
        DB::connection("mysql_gas")->commit();
        $writeOffQrCode = $this->generateVerificationCertificate($orderInfo['order_id'], $orderInfo['trade_mode'],
            $orderInfo['card_no'], $orderInfo['supplier_code']);
        return [
            'certificate_type'     => $writeOffQrCode['certificate_type'],
            'certificate_resource' => $writeOffQrCode['certificate_resource'],
        ];
    }
}