replicaCount: 1

env:
  - name: SIDECAR__PORT
    value: '80'
  - name: SIDECAR__LISTEN_PORT
    value: '30081'
  - name: aliyun_logs_k8s-petroleum-card-logs-topic
    value: stdout
  - name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
    value: env=prod,product=petroleum-card,appid=foss-order-web

image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/foss-order:c992ec0b.2

nameOverride: "foss-order-web"
fullnameOverride: "foss-order-web"

configmap:
  envdata:
    APPLICATION__NAME: "foss-order-web"
    PHPENV: "local"
    APP__NAME: "foss-order"
    APP__ENV: "prod"
    CRONTAB__ENABLE: "on"
    RUN_SCRIPTS: "1"
    SUPERVISOR__ENABLE: "on"
    PHP_ERRORS_STDERR: "1"
    PHP_MEM_LIMIT: "1024"
    PHP_POST_MAX_SIZE: "2000"
    SERVICE_GROUP: "web"
resources:
  limits:
    cpu: 300m
    memory: 1000Mi
  requests:
    cpu: 200m
    memory: 500Mi

ports:
  - name: http
    containerPort: 80
    protocol: TCP

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-inner
  hosts:
    - host: foss.chinawayltd.com
      paths:
        - /order
