<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/7
 * Time: 3:18 PM
 */

namespace App\Console\Commands;

use App\Services\ConsumerService;
use App\Services\OrderExtendService;
use Illuminate\Console\Command;
use App\Library\Helper\Common;

class Consumer extends Command
{
    CONST LOG = 'queue_order_operation';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consume:queue:order:operation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '消费队列queue:order:operation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Common::log('info', 'queue_order_operation_start', []);

        $job = app(OrderExtendService::class);
        (new ConsumerService())->registerHandler([$job, 'updateOrderExtInfo'])
            ->start();
    }
}