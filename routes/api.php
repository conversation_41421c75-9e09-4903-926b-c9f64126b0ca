<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//Route::middleware('auth:api')->get('/user', function (Request $request) {
//    return $request->user();
//});

// 下单接口
Route::group(['prefix' => 'order/v1'], function () {
    Route::post('/pda/add', 'OrderController@createForPAD'); // pda下单
    Route::post('/self/add', 'OrderController@createForSelf'); // 自助付款下单
    Route::post('/oa/add', 'OrderController@createForOA'); // OA下单
    Route::post('/mini/add', 'OrderController@createForMiniProgram'); // 小程序下单


    //获取运营商配置
    Route::post('/mini/testPcode', 'OrderController@testCurlPcode');
    //运营商改用DB读取
    Route::post('/mini/operatorDb', 'OrderController@getOperatorFromDb');
});

//小程序
Route::group(['prefix' => 'mini/v1','middleware'=>['mini.api']], function () {
    Route::post('/station/getOilType', 'MiniProgramController@getOilType');
    Route::post('/selfPay/preOrder', 'MiniProgramController@createOrder');
    Route::post('/paySuccess/getTime', 'MiniProgramController@getCurrentTime');
    Route::post('/paySuccess/tradeInfo', 'MiniProgramController@getPayData');
    Route::post('/bill/tradeList', 'MiniProgramController@getTradesList');
    Route::post('/station/getTradePrice', 'MiniProgramController@oilTradePrice');
    Route::post('/station/getStationId', 'MiniProgramController@getStationId');
    Route::post('/payQrCode/checkPwd', 'MiniProgramController@checkPwd');
    //支付成功后,订单详情
    Route::post('/paySuccess/tradeDetail', 'MiniProgramController@getPayDetail');

    Route::post('/onLinePay/paySure', 'MiniProgramController@paySure');
    Route::post('/onLinePay/preOrder', 'MiniProgramController@createPreOrder');
    Route::post('/onLinePay/submitOrder', 'MiniProgramController@submitOrder');

    //生成壳牌支付码
    Route::post('/onLinePay/getQrcode', 'MiniProgramController@createPayMentQrCode');

    //G7能源账户(卡)流水接口
    Route::post('/bill/cardStream', 'MiniProgramController@getCardStream');
    //G7能源账户(卡)流水详情
    Route::post('/bill/cardStreamDetails', 'MiniProgramController@getStreamDetail');

    //获取营销活动开关
    Route::post('/activity/getConf', 'MiniProgramController@getActConf');

    // 查看历史订单
    Route::post('/getNewOrderPaginate', 'Order\OrderController@getNewOrderPaginate');

    //获取电子券类别列表
    Route::post('/station/getCouponType', 'MiniProgramController@getCouponType');

    //获取卡所属机构是否需车牌加油
    Route::post('/card/isMustTruck', 'MiniProgramController@isMustTruckNo');

    /*//小程序新下单流程
    Route::post('/onLine/createOrder', 'Order\OrderController@generateOrder');
    //小程序支付订单
    Route::post('/onLine/payOrder', 'Order\PaymentController@payOrder');*/

    /***********卡车宝贝相关接口***********/
    //获取待支付订单
    Route::post('/h5Order/getOrder', 'H5Controller@waitPayment');
    Route::post('/h5Order/orderDetail', 'H5Controller@refreshOrder');
    Route::post('/h5Order/cancelOrder', 'H5Controller@cancelOrder');

    Route::get('/order/getExpiredSoonTripartiteCouponList', 'MiniProgramController@getExpiredSoonTripartiteCouponList');

    Route::post('/order/getTruckNoThroughLastMonthOrderOfDriver', 'MiniProgramController@getTruckNoThroughLastMonthOrderOfDriver');

    Route::post('/parseQrCodeForElectricity', 'QrCodeController@parseQrCodeForElectricity');
    Route::post('/checkElectricityOrderAndAccount', 'Order\OrderController@checkElectricityOrderAndAccount');
    Route::post('/createElectricityOrder', 'Order\OrderController@createElectricityOrder');
    Route::post('/orderStopElectricity', 'Order\OrderController@orderStopElectricityForUser');
    Route::post('/getElectricityOrderList', 'Order\OrderController@getElectricityOrderList');
    Route::post('/getElectricityOrderProgress', 'Order\OrderController@getElectricityOrderProgress');
    Route::post('/getElectricityOrderInfo', 'Order\OrderController@getElectricityOrderInfo');
    Route::post('/getElectricityOrderDetail', 'Order\OrderController@getElectricityOrderDetail');
});

//H5,三方回调接口
Route::group(['prefix' => 'openapi'], function () {
    Route::post('/order/payCallBack', 'H5Controller@payCallBack');

    //向三方推送订单
    Route::post('/order/pushOrder', 'H5Controller@pushOrder2Third');

    //查询订单
    Route::post('/order/detail', 'H5Controller@getOrderDetail');

    //查询订单列表信息
    Route::post('/order/list', 'H5Controller@getOrderList');

    //下单
    Route::post('/order/create', 'H5Controller@createOrder');

    //支付订单
    Route::post('/order/pay', 'H5Controller@payOrder');

    //支付订单
    Route::post('/order/refreshVerificationCertificate', 'H5Controller@refreshVerificationCertificate');

    //取消订单
    Route::post('/order/cancel', 'H5Controller@cancelOrderForPlatform');

    //三方发起司机退款申请单
    Route::post('/order/driverRefundApplication', 'H5Controller@addDriverRefundApplicationRecord');

    //生成kafka消息
    Route::post('/producer/message', 'MiniProgramController@producerGmqMsg');
});

//为Gas提供接口
Route::group(['prefix' => 'services/v1'], function () {
    Route::post('/card/isMustTruck', 'MiniProgramController@isMustTruckNo');
    Route::post('/thirdOrder/pushMsg', 'MiniProgramController@pushMsg');
    Route::post('/asyncBatchSend/wxTemplateMessage', 'WxController@asyncBatchSendTemplateMessage');

    // 卡包验密、扣费
    Route::namespace('Order')->group(function () {
        //Route::post('/cardBoxPayment', 'PaymentController@cardBoxPayment');
        //todo 开启代码
        Route::post('/cardBoxPayment', 'PaymentController@payOrder');

        // 根据G7券标识获取订单信息
        Route::any('/order/batchGetByOrderId', 'OrderController@batchGetByOrderId');
    });

    //卡包新下单流程
    Route::post('/onLine/createOrder', 'Order\OrderController@generateOrder');
    //卡包支付订单
    Route::post('/onLine/payOrder', 'Order\PaymentController@payOrder');
    //卡包获取通过枪ID及卡号获取油品单价
    Route::post('/station/getOilPriceByGunAndCard', 'Order\OrderController@getOilPriceByGunAndCard');
    //获取电子券类别列表
    Route::post('/station/getCouponType', 'MiniProgramController@getCouponType');
    //获取壳牌支付码
    Route::post('/onLinePay/getQrcode', 'MiniProgramController@createPayMentQrCode');

    Route::post('/limit/stationConf', 'Order\OrderController@getLimitConfig');

});

// 补单
Route::group(['prefix' => 'additional/v1', 'namespace' => 'History', 'middleware' => ['api']], function () {
    // OA回调更新补单状态
    Route::post('/additional/updateApproveItem', 'HistoryAdditionalController@updateApproveDetail');
});

/**
 * 订单(新)
 */
Route::group(['prefix' => 'order', 'namespace' => 'Order', 'middleware' => ['order']], function () {
    // 获取商品信息
    Route::post('/getGoodsLimit', 'OrderController@getGoodsLimit');
    // 校验上笔订单状态
    Route::post('/checkStationLatestOrder', 'OrderController@checkStationLatestOrder');

    // 下单
    //Route::post('/createOrder', 'OrderController@createOrder');
    //统一下单入口
    //todo 开启代码
    Route::post('/createOrder', 'OrderController@generateOrder');

    // 申请退款
    Route::post('/refundApprove', 'OrderApproveController@refundApprove');
    // 订单取消
    Route::post('/cancelOrder', 'OrderController@cancelOrder');
    // 保存签名
    Route::post('/addDriverSignature', 'OrderExtendController@addDriverSignature');
    // 查看|打印小票
    Route::post('/getTicket', 'OrderController@getTicket');
    // 查看历史交易流水
    Route::post('/getHistoryPaginate', 'OrderController@getHistoryPaginate');

    Route::post('/getOperateOrderReason', 'OrderController@getOperateOrderReason');

    Route::post('/cashierEnterOrder', 'OrderController@cashierEnterOrder');
});

Route::group(['prefix' => 'order', 'namespace' => 'Order'], function () {
    // 分页获取订单列表
    Route::post('/getOrderPaginate', 'OrderController@getOrderPaginateNew');

    // 获取最大查询天数
    Route::post('/getMaxDay', 'OrderController@getMaxDay');

    // OA下单
    Route::post('/createOrderByOA', 'OrderController@createOrderByOA');

    // 获取订单详情
    Route::post('/getOrderItem', 'OrderController@getOrderItem');

    // OA支付回调
    Route::post('/oaPay', 'PaymentController@oaPay');

    // OA退款回调
    Route::post('/oaRefund', 'RefundController@oaRefund');

    //todo 由于不清楚哪里使用，暂时注释
    // 验密后支付
    //Route::post('/checkPasswordAndPay', 'PaymentController@checkPasswordAndPay');
    //统一支付接口
    //Route::post('/checkPasswordAndPay', 'PaymentController@payOrder');

    // 获取订单详情
    Route::post('/getBatchOrderItem', 'OrderController@getBatchOrderItem');

    // parse qr code for oa
    Route::post('/parseQrCodeForOa', 'QrCodeController@parseForOa');

    // 分页获取合作加油订单列表
    Route::post('/getCooperateOrderPaginate', 'OrderController@getCooperateOrderPaginate');

});

Route::group(['prefix' => 'qrCode/v1'], function () {
    // parse qr code for oa
    Route::post('/parseForOa', 'QrCodeController@parseForOa');
});

// OA订单路由
Route::group(['prefix' => 'oil_adapter', 'namespace' => 'Order'], function () {
    // 分页获取订单列表
    Route::post('/getOrderPaginate', 'OrderController@getOrderPaginate');
    // 下单【生成订单 or (生成订单 + 支付订单)】
    Route::post('/makeOrder', 'OrderController@oaMakeOrder');

    // 订单查询
    Route::post('/getOrderItem', 'OrderController@getOrderItem');

    // 是否订单发起退款
    Route::post('/getOrderRefundInfo', 'OrderApproveController@getOrderRefundInfo');

    // 退款
    Route::post('/oaRefund', 'RefundController@oaRefund');
    // 取消订单
    Route::post('/cancelOrder', 'OrderController@cancelOrder');

    //统一支付接口
//    Route::post('/oaPay', 'PaymentController@payOrder');
    Route::post('/oaPay', 'PaymentController@oaPayNew');

    //接收中石油电子券核销结果
    Route::post('/payment/receiveResult', 'OrderController@receivePayMentResult');
    //取消电子券类订单核销结果
    Route::post('/payment/cancelReceivePaymentResult', 'OrderController@cancelReceivePaymentResult');
    //接收非电子券类订单核销结果
    Route::post('/payment/receivePaymentResultWithOutCoupon', 'OrderController@receivePaymentResultWithOutCoupon');

    // 获取机构对账订单
    Route::post('/getOrgBillOrder', 'OrderController@getOrgBillOrder');
    // 聚合出行查询订单号
    Route::post('/getOrderIdsForJHCX', 'OrderController@getOrderIdsForJHCX');

    // 退款申请审核结果回调
    Route::post('/orderApproveResultCallback', 'OrderApproveController@approveResultCallback');
    // 判定订单是否扣费失败
    Route::post('/determineOrderIsDeductionFailed', 'OrderController@determineOrderIsDeductionFailed');
    // 对接客户发起退款申请
    Route::post('/oaRefundApplication', 'OrderApproveController@oaRefundApplication');
    // 预约加油完成
    Route::post('/reservationOrderToSaleOrder', 'PaymentController@reservationOrderToSaleOrder');
    // 通过预约加油订单查询关联的销售订单
    Route::post('/getOrderInfoByReservationOrder', 'OrderController@getOrderInfoByReservationOrder');
    // 获取订单日维度对账快照数据
    Route::post('/getDayOrderReconciliationSnapshotData', 'OrderController@getDayOrderReconciliationSnapshotData');
    // 充电订单启动充电结果回调
    Route::post('/orderStartElectricity', 'OrderController@orderStartElectricity');
    // 充电订单停止充电结果回调
    Route::post('/orderStopElectricity', 'OrderController@orderStopElectricity');
    // 充电订单充电状态回调
    Route::post('/orderGoingElectricity', 'OrderController@orderGoingElectricity');
    // 充电订单充电完成回调
    Route::post('/orderFinishElectricity', 'OrderController@orderFinishElectricity');
});

// gsp_fuel路由组
Route::group(['prefix' => 'gsp_fuel'], function () {
    // 通过流水ID查询该流水对应到gsp_fuel的单据类型
    Route::post('/order/getDocumentTypeByHistoryId', 'Order\OrderController@getDocumentTypeByHistoryId');
    // 通过订单id查询订单关联的原始单号
    Route::post('/order/getOrderItem', 'Order\OrderController@getOrderItem');
});