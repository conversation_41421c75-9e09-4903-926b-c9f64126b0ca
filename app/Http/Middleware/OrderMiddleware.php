<?php

namespace App\Http\Middleware;

use App\Library\Helper\Common;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use Closure;
use Ramsey\Uuid\Uuid;

class OrderMiddleware
{
    protected $fossUser;
    protected $fossStation;

    public function __construct
    (
        FossUser $fossUser,
        FossStation $fossStation
    )
    {
        $this->fossUser = $fossUser;
        $this->fossStation = $fossStation;
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     * @throws \Exception
     */
    public function handle($request, Closure $next)
    {
        Common::log('info','记录header信息', ['param' => $request->header()]);
        //用户信息
        $uid = $request->header('X-G7-Api-Uid');
        $requestSource = $request->header('appkey');
        $token = $request->header('token');
        try {
            /**
             * appkey校验
             */
            $appKeyInfo = $this->fossUser->getAppKeyConfig();
            $source = $appKeyInfo['source'][$requestSource] ?? '';
            if (empty($source)) {
                throw new \Exception('FOSS-USER授权失败，无权限访问', config('errorcode.STATUSCODE_NOTACCESS'));
            }

            /**
             * UID校验
             */
            if (empty($uid)) {
                throw new \Exception(config( '无效的用户ID,无权限访问','errorcode.STATUSCODE_NOTACCESS'));
            }

            $userInfo = $this->fossStation->getGasUserInfoById($uid);
            if (empty($userInfo)) {
                throw new \Exception( '登录用户不存在，无权限访问', config('errorcode.STATUSCODE_NOTACCESS'));
            }

            /**
             * 下单渠道枚举
             */
            $sourceNameEnum = [];
            foreach ($appKeyInfo['role_source'] as $key => $value) {
                if (array_key_exists($key, $appKeyInfo['source_name'])) {
                    $sourceNameEnum[$value] = $appKeyInfo['source_name'][$key];
                }
            }
            $request->offsetSet('user_name', $userInfo['truename']);
            $request->offsetSet('uid', $uid);
            $stationIdArray = function ($stationIds){
                  $stationIdsArray = explode(',', $stationIds);

                  $needArray = [];
                  foreach ($stationIdsArray as $item) {
                      $needArray[] = trim($item, "'");
                  }

                  return $needArray;
            };

            $request->offsetSet('station_id_array',$stationIdArray($userInfo['station_id']));
            $request->offsetSet('client_token', $token);
            $request->offsetSet('order_channel', $appKeyInfo['role_source'][$source] ?? '');
            $request->offsetSet('order_channel_enum', $sourceNameEnum);

            /**
             * 对接kafka日志
             */
            $requestId = !empty($request->header('X-Request-Id')) ? $request->header('X-Request-Id') : Common::uuid();
            $requestIp = !empty($request->header('X-Original-Forwarded-For')) ? $request->header('X-Original-Forwarded-For') : (!empty($request->header('X-Real-Ip')) ? $request->header('X-Real-Ip') : ($_SERVER['REMOTE_ADDR'] ?? ''));
            $headerInfo = [
                'source' => $source,
                'request_id' => $requestId,
                'trace_id' => Uuid::uuid1()->getHex(),
                'uid' => $uid,
                'user_name' => $userInfo['truename'],
                'request_real_ip' => $requestIp,
            ];
            $request->offsetSet('header_info', $headerInfo);

            return $next($request);
        } catch (\Exception $e) {
            $data = [
                'code' => $e->getCode(),
                'msg' => $e->getMessage(),
                'success' => false,
                'data' => []
            ];
            return response()->json($data);
        }
    }
}
