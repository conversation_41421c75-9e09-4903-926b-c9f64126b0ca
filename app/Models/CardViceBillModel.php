<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class CardViceBillModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_card_vice_bill';

    protected $guarded = ["id"];

    protected $fillable = ['id','card_no','res_type',"res_id","amount","pay_type","mobile","use_fanli","trade_desc","card_balance","trade_time"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}