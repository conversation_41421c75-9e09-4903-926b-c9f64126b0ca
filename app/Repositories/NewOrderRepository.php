<?php


namespace App\Repositories;


use App\Models\OrderModel;
use App\Servitization\FossStation;
use Exception;

class NewOrderRepository
{
    protected $orderModel;

    public function __construct
    (
        OrderModel $orderModel
    )
    {
        $this->orderModel = $orderModel;
    }

    /**
     * 获取单个订单信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $withExt
     * @return array
     */
    public function getOneOrderByParams($whereParams, $select = ['*'], $withExt = true)
    {
        return $this->orderModel->getOneOrderInfo($whereParams, $select, $withExt);
    }
 
    public function getOrderInfoByLock($whereParams, $select = ['*'], $withExt = true)
    {
        return $this->orderModel->getOneOrderForLock($whereParams, $select, $withExt);
    }

    /**
     * 获取批量订单信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchOrderByParams($whereParams, $select = ['*'], $toArray = true)
    {
        return $this->orderModel->getBatchOrderInfo($whereParams, $select, $toArray);
    }

    /**
     * 分页获取订单信息
     *
     * @param $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @return array
     */
    public function getOrderPaginate($whereParams, $select = ['*'], $page = 1, $limit = 10, $toArray = false)
    {
        return $this->orderModel->getOrderPaginate($whereParams, $select, $page, $limit, $toArray);
    }

    /**
     * 统计订单条数
     *
     * @param $whereParams
     * @return mixed
     */
    public function getOrderCount($whereParams)
    {
        return $this->orderModel->getOrderCount($whereParams);
    }

    /**
     * 分页获取导出订单的信息
     *
     * @param array $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @return array
     */
    public function getOrderExportPaginate($whereParams = [], $select = ['*'], $page = 1, $limit = 10, $toArray = false)
    {
        return $this->orderModel->getOrderExportPaginate($whereParams, $select, $page, $limit, $toArray);
    }

    /**
     * 创建订单
     *
     * @param array $insertArray
     * @return mixed
     */
    public function insertOrder(array $insertArray)
    {
        return $this->orderModel->insertOrder($insertArray);
    }

    /**
     * 修改订单状态
     *
     * @param $orderId
     * @param $updateArray
     * @return mixed
     */
    public function updateOrderByOrderId($orderId, $updateArray)
    {
        return $this->orderModel->updateOrderByOrderId($orderId, $updateArray);
    }

    /**
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getLatestOrder(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->orderModel->getLatestOrderByParams($whereParams, $select, $toArray);
    }

    public function getOrderInfoWithExt(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->orderModel->infoWithExt($whereParams, $select, $toArray);
    }

    /**
     * 删除订单
     * @param $orderId
     * @return int|mixed
     */
    public function deleteByOrderId($orderId)
    {
        return $this->orderModel->deleteByOrderId($orderId);
    }

    /**
     * 按机构树查找站点最接近当前机构的特殊定价机构
     * @param string $stationId
     * @param string $orgCode
     * @param string $orgTreeCode
     * @return string
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/28 8:31 下午
     */
    public static function getSpecialPriceOrg(string $stationId, string $orgCode, string $orgTreeCode = ""): string
    {
        $specialPriceOrgData = (new FossStation())->getStationSpecialPriceForOrg([
            'station_id'    => $stationId,
            'org_tree_code' => $orgTreeCode,
        ]);
        $returnOrgCode = $orgCode;
        if (!empty($specialPriceOrgData)) {

            for ($i = 0; $i <= (strlen($orgCode) - 6) / 2; $i++) {

                $otv = substr($orgCode, 0, strlen($orgCode) - $i * 2);
                foreach ($specialPriceOrgData as $v) {

                    if ($v == $otv) {

                        $returnOrgCode = $v;
                        break 2;
                    }
                }
            }
        }
        return $returnOrgCode;
    }
}