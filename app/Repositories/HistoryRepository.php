<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\HistoryModel;
use App\Models\Tidb\HistoryAllModel;

class HistoryRepository extends \App\Repositories\EloquentRepository
{
    protected $historyModel;
    public function __construct(HistoryModel $historyModel)
    {
        $this->model = $historyModel;
        $this->historyModel = $historyModel;
    }

    //订单入库
    public function addData(array $data)
    {
        $this->rules['STORE'] = $this->model->getFillAble();
        return $this->storeModel($data,$this->historyModel);
    }

    //更新订单
    public function updateOrder(array $data,$condition)
    {
        return $this->historyModel->find($condition)->update($data);
    }

    //获取订单详情
    public function getTradeById(array $condition)
    {
        $data = $this->historyModel->select("id","pcode","log_type","old_id")->withCondition($condition)->first();
        return $data;
    }

    //锁查询
    public function getInfoByLock(array $condition)
    {
        //$this->debugstart('mysql_gas');
        $data = $this->historyModel->select("id","pcode")->withCondition($condition)->lockForUpdate()->first();
        //$sql = $this->debugend('mysql_gas');
        //print_r($sql);
        return $data;
    }

    /**
     * 获取交易流水所需参数
     *
     * @param $params
     * @param $page
     * @param $limit
     * @param $toArray
     * @return array
     */
    public function getHistoryPaginate($params, $page, $limit, $toArray = true)
    {

        $select = ['id', 'pcode', 'card_no', 'log_type', 'station_id', 'oil_type', 'oil_name', 'oil_level', 'xpcode_pay_money',
                   'money', 'pay_money', 'data_type', 'pay_price', 'xpcode_pay_price', 'oil_num', 'gascode', 'driver_name',
                   'driver_phone', 'truck_no', 'serial_num', 'imgData', 'data_type', 'stream_no', 'truename', 'createtime',
                   'oil_time', 'gun_id', 'provice_code', 'city_code', 'old_id'];

        $historyModel = new HistoryAllModel();
        return $historyModel->getHistoryInfoByLimit($params, $page, $limit, $select, $toArray);
    }

    /**
     * 交易流水导出分页查询
     *
     * @param $whereParams
     * @param $page
     * @param $limit
     * @return array
     */
    public function getExportHistoryPaginate($whereParams, $page, $limit)
    {
        $select = ['id', 'pcode','card_no', 'log_type', 'station_id', 'oil_type', 'oil_name', 'oil_level', 'xpcode_pay_money',
            'money', 'pay_money', 'data_type', 'pay_price', 'xpcode_pay_price', 'oil_num', 'gascode', 'driver_name',
            'driver_phone', 'truck_no', 'serial_num', 'imgData', 'data_type', 'stream_no', 'truename', 'createtime',
            'oil_time', 'gun_id', 'provice_code', 'city_code','old_id'];
            //G7WALLET-6589【GMS】订单/流水查询导出性能优化
        $historyModel = new HistoryAllModel();
        return $historyModel->getExportHistoryPaginate($whereParams, $page, $limit, $select);
    }

    /**
     * 统计指定交易流水的数量
     *
     * @param array $whereParams
     * @return mixed
     */
    public function getHistoryCountByParams(array $whereParams)
    {
        //G7WALLET-6589【GMS】订单/流水查询导出性能优化
        $historyModel = new HistoryAllModel();
        return $historyModel->getHistoryCountByParams($whereParams);
    }

    /**
     * 根据指定条件获取1条交易流水
     *
     * @param array $whereParas
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneHistoryByParams(array $whereParas, $select = ['*'], $toArray = true)
    {
        return $this->historyModel->getOneHistoryByParams($whereParas, $select, $toArray);
    }

    /**
     * 添加交易流水
     *
     * @param $params
     * @return mixed
     */
    public function insertHistory($params)
    {
        return $this->historyModel->insertHistory($params);
    }

    /**
     * 更新交易流水
     *
     * @param array $whereParams
     * @param array $update
     * @return mixed
     */
    public function updateHistory(array $whereParams, array $update)
    {
        return $this->historyModel->updateHistory($whereParams, $update);
    }
}