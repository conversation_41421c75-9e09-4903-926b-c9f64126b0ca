<?php

namespace Tests\Unit;

use App\Http\Defines\CommonError;
use App\Http\Defines\CouponDefine;
use App\Models\CouponsModel;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\NewOrderRepository;
use App\Services\CouponService;
use App\Services\MiniOrderService;
use App\Services\PaymentService;
use Tests\TestCase;

class CouponServiceTest extends TestCase
{
    /**
     * A basic test example.
     *
     * @return void
     */
//    public function testGetCanUseCoupon()
//    {
////        try {
////            $coupon_type_id = 9;
////            $res = app(CouponService::class)->getCanUseCoupon($coupon_type_id);
////
////            echo "\n 电子券标识：" . $res. PHP_EOL;
////        } catch (\Exception $e) {
////            echo "\n 获取可用电子券错误信息: " . CommonError::getMsgByCode($e->getCode()) . PHP_EOL;
////        }
//    }
//
//    /**
//     * 未占用 -> 占用
//     * coupon_type_id 12  used 0 un_used 120
//     *
//     * 占用 - 未核销
//     *
//     * 未核销 - 核销
//     *
//     * 未核销 - 未占用
//     */
//    public function testUpdateCoupon()
//    {
////        try {
////            $coupon_flag = 2109241455190004;
////            $data = [
////                'status' => CouponDefine::NO_USE,
////                'charge_off_time' => null
////            ];
////            $enableTransaction = true;
////
////            $res = app(CouponService::class)->updateCoupon($coupon_flag, $data, $enableTransaction);
////
////            var_dump($res);
////        }  catch (\Exception $e) {
////
////            echo "\n 电子券信息更新 失败 : " . $e->getTraceAsString() . PHP_EOL;
////        }
//    }
//
//    public function testReceiveHxResult()
//    {
//        $data = [
////            "voucher"       => "16387243161835475138",
//            "voucher"       => "16370527838945971749",
//            "stationcode"   => "VS0A",
//            "usedtime"      => "2021-12-06 01:12:04",
//            "amount"        => "1000"
//        ];
//
//        $res = app(CouponService::class)->receiveHxResult($data);
//    }

//    public function testUpdateCouponInfo()
//    {
//        $couponG7 = CouponsModel::where('voucher','2208121708390012')->first();
//        $pushResult = <<<EOT
//{
//        "status":1,
//        "codeList":[
//            "202186474023392717"
//        ],
//        "ticketDetail":{
//            "amount":100,
//            "effectiveTime":"2022-08-15",
//            "expiredDate":"2022-09-14",
//            "ticketStatus":2,
//            "limitDetail":{
//                "name":"1元测试券",
//                "requestCode":"1660190274565",
//                "status":2,
//                "amount":100,
//                "limitAmount":0,
//                "effectiveTime":"2022-08-15",
//                "expirdDate":"2022-09-14",
//                "isReuse":"否"
//            },
//            "name":"1元测试券",
//            "requestCode":"1660190274565"
//        },
//        "info":"成功",
//        "order_id":"2022081500424010025",
//        "secondaryPayment":true
//    }
//EOT;
//        $pushResult = json_decode($pushResult, true);
//
//        $voucher = app(PaymentService::class)->updateCouponInfo($couponG7, $pushResult, true);
//
//        echo "voucher: ", $voucher, "\n";
//
//    }

    public function testGetCouponRemark()
    {
        $orderInfo = app(NewOrderRepository::class)->getOrderInfoWithExt(['order_id' => "2022081500424010025"]);

        $remark = app(MiniOrderService::class)->getCouponRemark($orderInfo, "20003CAMMM");

        echo "remark : ", $remark, "\n";
    }
}
