<?php


namespace App\Services;


use App\Events\RefundNotice;
use App\Http\Defines\CommonDefine;
use App\Library\Export\ExportCsv;
use App\Library\Helper\NumberUtil;
use App\Library\Request;
use App\Library\Upload\Upload;
use App\Models\GasHistoryApproveModel;
use App\Models\HistoryModel;
use App\Models\OilOrgModel;
use App\Models\OrderModel;
use App\Repositories\CityRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryApproveRepository;
use App\Repositories\HistoryPayRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationGunRepository;
use App\Repositories\StationPcodeRepository;
use App\Repositories\StationRepository;
use App\Repositories\StationTankRepository;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use App\Servitization\FossStation;
use App\Servitization\FossUser;
use App\Servitization\Gas;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class HistoryApproveService
{
    protected $errorCodeService;
    protected $redisService;
    protected $orderApproveService;
    protected $stationRepository;
    protected $historyRepository;
    protected $historyPayRepository;
    protected $historyApproveRepository;
    protected $newOrderRepository;
    protected $stationGunRepository;
    protected $stationTankRepository;
    protected $stationPcodeRepository;
    protected $orgRepository;
    protected $cityRepository;
    protected $dictRepository;
    protected $fossStation;
    protected $fossUser;
    protected $adapter;
    protected $gas;
    protected $numberUtil;
    protected $exportCsv;

    protected $messageService;

    public function __construct
    (
        ErrorCodeService $errorCodeService,
        RedisService $redisService,
        OrderApproveService $orderApproveService,
        StationRepository $stationRepository,
        HistoryRepository $historyRepository,
        HistoryPayRepository $historyPayRepository,
        HistoryApproveRepository $historyApproveRepository,
        NewOrderRepository $newOrderRepository,
        StationGunRepository $stationGunRepository,
        StationTankRepository $stationTankRepository,
        StationPcodeRepository $stationPcodeRepository,
        OrgRepository $orgRepository,
        CityRepository $cityRepository,
        DictRepository $dictRepository,
        FossStation $fossStation,
        FossUser $fossUser,
        Adapter $adapter,
        Gas $gas,
        NumberUtil $numberUtil,
        ExportCsv            $exportCsv,
        MessageService $messageService
    )
    {
        $this->errorCodeService = $errorCodeService;
        $this->redisService = $redisService;
        $this->orderApproveService = $orderApproveService;
        $this->stationRepository = $stationRepository;
        $this->historyRepository = $historyRepository;
        $this->historyPayRepository = $historyPayRepository;
        $this->historyApproveRepository = $historyApproveRepository;
        $this->newOrderRepository = $newOrderRepository;
        $this->stationGunRepository = $stationGunRepository;
        $this->stationTankRepository = $stationTankRepository;
        $this->stationPcodeRepository = $stationPcodeRepository;
        $this->orgRepository = $orgRepository;
        $this->cityRepository = $cityRepository;
        $this->dictRepository = $dictRepository;
        $this->fossStation = $fossStation;
        $this->fossUser = $fossUser;
        $this->adapter = $adapter;
        $this->gas = $gas;
        $this->numberUtil = $numberUtil;
        $this->exportCsv = $exportCsv;
        $this->messageService = $messageService;
    }

    /**
     * 分页获取审核结果
     *
     * @param $params
     * @return array
     */
    public function getApprovePaginate($params)
    {
        $page = array_get($params, 'page', 1);
        $limit = array_get($params, 'limit', 10);

        $whereParams = [];
        if (!empty($params['history_type'])) {
            $whereParams['history_type'] = explode(',', $params['history_type']);
        }
        if (!empty($params['approve_status'])) {
            $whereParams['approve_status'] = explode(',', $params['approve_status']);
        }
        if (!empty($params['station_id'])) {
            $whereParams['station_id'] = explode(',', $params['station_id']);
        }
        if (!empty($params['card_no'])) {
            $whereParams['card_no'] = $params['card_no'];
        }
        if (!empty($params['truck_no'])) {
            $whereParams['truck_no'] = $params['truck_no'];
        }
        if (!empty($params['driver_phone'])) {
            $whereParams['driver_phone'] = $params['driver_phone'];
        }
        if (!empty($params['creator'])) {
            $whereParams['creator'] = $params['creator'];
        }
        if (! empty($params['order_id'])) {
            $whereParams['order_id'] = $params['order_id'];
        }
        if (!empty($params['third_order_id'])) {
            $whereParams['third_order_id'] = $params['third_order_id'];
        }
        // 创建开始时间
        if (! empty($params['ge_createtime'])) {
            $whereParams['ge_create_time'] = $params['ge_createtime'];
        }
        // 创建结束时间
        if (! empty($params['le_createtime'])) {
            $whereParams['le_create_time'] = $params['le_createtime'];
        }
        //订单类型
        if (! empty($params['order_sale_type'])) {
            $whereParams['order_sale_type'] = $params['order_sale_type'];
        }

        //退款系统
        if (!empty($params['refund_system'])) {
            $whereParams['refund_system'] = $params['refund_system'];
        }

        $result = $this->historyApproveRepository->getApprovePaginate($whereParams, ['gas_history_approve.*', 'gas_order_new.order_sale_type'], $page, $limit, true);
        if (empty($result['data'])) {
            return $result;
        }

        $needArray = [];
        foreach ($result['data'] as $item) {
            // 组合数据
            $item = array_merge($item, json_decode($item['approve_mirror'], true));
            unset($item['approve_mirror']);
            $item['money'] = sprintf('%.2f', $item['money'] ?? 0);
            $item['pay_money'] = sprintf('%.2f', $item['pay_money'] ?? 0);
            $item['price'] = sprintf('%.2f', $item['price'] ?? 0);
            $item['pay_price'] = sprintf('%.2f', $item['pay_price'] ?? 0);
            $item['oil_num'] = sprintf('%.2f', $item['oil_num'] ?? 0);
            $item['history_type_name'] = GasHistoryApproveModel::$HISTORY_TYPE_ENUM[$item['history_type']];
            $item['approve_status_name'] = GasHistoryApproveModel::$APPROVE_STATUS_ENUM[$item['approve_status']];

            $item['mac_price'] = sprintf('%.2f', $item['mac_price'] ?? 0);
            $item['mac_amount'] = sprintf('%.2f', $item['mac_amount'] ?? 0);
            $item['order_id'] = strval($item['order_id']);
            $item['order_sale_type_name'] = OrderModel::ORDER_SALE_TYPE[$item['order_sale_type']] ?? '';

            $needArray[] = $item;
        }
        $result['data'] = $needArray;

        return $result;
    }

    /**
     * 手机管站获取退款审核单列表
     *
     * @param $params
     * @return array
     */
    public function getMtApprovePaginate($params)
    {
        $whereParams = [];
        if (!empty($params['station_id'])) {
            $whereParams['station_id'] = $params['station_id'];
        }
        if (!empty($params['status'])) {
            switch ($params['status']) {
                case 1: // 待审核
                    $whereParams['approve_status'] = [GasHistoryApproveModel::WAIT_APPROVE, GasHistoryApproveModel::FAIL_AFTER_APPROVE];
                    break;
                case 2: // 已退款
                    $whereParams['approve_status'] = [GasHistoryApproveModel::SUCCESS_AFTER_APPROVE, GasHistoryApproveModel::FINISH_AFTER_APPROVE];
                    break;
                case 3: // 已驳回
                    $whereParams['approve_status'] = [GasHistoryApproveModel::REJECT_APPROVE];
                    break;
            }
        }
        if (!empty($params['source'])) {
            $whereParams['source'] = $params['source'];
        }
        if (!empty($params['province_code'])) {
            $whereParams['province_code'] = $params['province_code'];
        }
        if (!empty($params['city_code'])) {
            $whereParams['city_code'] = $params['city_code'];
        }
        if (!empty($params['history_type'])) {
            $whereParams['history_type'] = $params['history_type'];
        }
        if (!empty($params['ge_create_time'])) {
            $whereParams['ge_create_time'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
        }
        if (!empty($params['le_create_time'])) {
            $whereParams['le_create_time'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }

        $result = $this->historyApproveRepository->getApprovePaginate($whereParams, ['*'], array_get($params, 'page', 1), array_get($params, 'limit', 10), true);
        if (empty($result['data'])) {
            return $result;
        }
        $needArray = [];
        $approveStatusEnum = [
            GasHistoryApproveModel::WAIT_APPROVE          => '待审核',
            GasHistoryApproveModel::FAIL_AFTER_APPROVE    => '待审核',
            GasHistoryApproveModel::SUCCESS_AFTER_APPROVE => '已退款',
            GasHistoryApproveModel::FINISH_AFTER_APPROVE  => '已退款',
            GasHistoryApproveModel::REJECT_APPROVE        => '已驳回',
        ];
        $approveStatus = [
            GasHistoryApproveModel::WAIT_APPROVE          => 1,
            GasHistoryApproveModel::FAIL_AFTER_APPROVE    => 1,
            GasHistoryApproveModel::SUCCESS_AFTER_APPROVE => 2,
            GasHistoryApproveModel::FINISH_AFTER_APPROVE  => 2,
            GasHistoryApproveModel::REJECT_APPROVE        => 3,
        ];
        $supplierAndOrgInfo = (new Foss())->getSupplierAndOrgInfoByOrderId([
            'order_ids' => array_column($result['data'], 'order_id'),
            'supplier_info_fields' => [
                'settlement_docker'
            ],
            'org_info_fields' => [
                'belongto_saler',
            ],
        ]);
        $orderInfo = app(OrderModel::class)->getBatchOrderAndExtInfo([
            'order_id' => array_column($result['data'], 'order_id'),
        ], [
            'order_id',
            'third_order_id',
            'real_oil_num'
        ], [
            'order_id',
            'supplier_price'
        ])->keyBy('order_id')
          ->toArray();
        $currentOrgInfo = (new OilOrgModel())->getBatchOrgByParams([
            'orgcode' => array_column($result['data'], 'org_code'),
        ], [
            'is_system_orgcode',
            'orgcode',
        ])->keyBy('orgcode');
        foreach ($result['data'] as $item) {
            $mirror = json_decode($item['approve_mirror'], true);
            $array = [
                'id'                  => $item['id'],
                'order_id'            => $item['order_id'],
                'history_id'          => $item['history_id'],
                'approve_status'      => $approveStatus[$item['approve_status']],
                'approve_status_name' => $approveStatusEnum[$item['approve_status']],
                'province_name'       => $mirror['province_name'],
                'city_name'           => $mirror['city_name'],
                'station_name'        => empty($mirror['remark_name']) ? $mirror['station_name'] : $mirror['remark_name'],
                'pay_money'           => sprintf('%.2f', $mirror['pay_money']),
                'goods'               => $mirror['goods'],
                'oil_num'             => sprintf('%.2f', $mirror['oil_num']),
                'pay_price'           => sprintf('%.2f', $mirror['pay_price']),
                'card_no'             => $item['card_no'],
                'org_name'            => $mirror['supplier_name'],
                'create_time'         => $item['create_time'],
                'creator'             => $item['creator'],
                'remark'              => $item['remark'],
                'ticket_image'        => $mirror['ticket_image'],
                'truck_image'         => $mirror['truck_image'],
                'other_image'         => $mirror['other_image'],
                'supplier_money'      => sprintf(
                    '%.2f',
                    round(
                        bcmul(
                            $orderInfo[$item['order_id']]['real_oil_num'] ?? '',
                            $orderInfo[$item['order_id']]['ext']['supplier_price'] ?? '',
                            3
                        ),
                        2
                    )
                ),
                'settlement_docker'   => $supplierAndOrgInfo[$item['order_id']]['settlement_docker'] ?? '',
                'belongto_saler'      => $supplierAndOrgInfo[$item['order_id']]['belongto_saler'] ?? '',
                'third_order_id'      => $currentOrgInfo[$item['org_code']]['is_system_orgcode'] == 2 ?
                    ($orderInfo[$item['order_id']]['third_order_id'] ?? '') : ''
            ];
            $needArray[] = $array;
        }
        $result['data'] = $needArray;

        return $result;
    }

    /**
     * 审核单详情
     *
     * @param $params
     * @return array
     */
    public function getApproveDetail($params)
    {
        $id = array_get($params, 'id', 0);
        if (empty($id)) {
            return [];
        }

        $result = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $id], ['*'], true);
        if (!empty($result)) {
            $approve_mirror = json_decode($result['approve_mirror'], true);
            $result = array_merge($result, json_decode($result['approve_mirror'], true));

            $result['other_image'] = Upload::getSignUrl(array_get($result,"other_image",""));
            $result['ticket_image'] = Upload::getSignUrl(array_get($result,"ticket_image",""));
            $result['truck_image'] = Upload::getSignUrl(array_get($result,"truck_image",""));
            //bigint类型转string
            $result['order_id'] = $result['order_id']."";

            unset($result['approve_mirror']);
            $result['money']        = $result['supplier_money']     = sprintf('%.2f', $result['money'] ?? 0);
            $result['pay_money']    = $result['oil_money']          = sprintf('%.2f', $result['pay_money'] ?? 0);
            $result['price']        = $result['supplier_price']     = sprintf('%.2f', $result['price'] ?? 0);
            $result['pay_price']    = $result['platform_price']     = sprintf('%.2f', $result['pay_price'] ?? 0);
            $result['discount_rate'] = $approve_mirror['approveData']['discount_rate'] ?? ($approve_mirror['discount_rate'] ?? 100);
            $result['settlement_type'] = $approve_mirror['approveData']['settlement_type'] ?? ($approve_mirror['settlement_type'] ?? 1);

            $result['oil_num'] = sprintf('%.2f', $result['oil_num'] ?? 0);
            $result['history_type_name'] = GasHistoryApproveModel::$HISTORY_TYPE_ENUM[$result['history_type']];
            $result['approve_status_name'] = GasHistoryApproveModel::$APPROVE_STATUS_ENUM[$result['approve_status']];

            if (! isset($result['mac_price'])) {
                $result['mac_price'] = $result['mac_amount'] = '';
            }
            if (! isset($result['oil_unit'])) {
                if (isset($result['approveData']) && isset($result['approveData']['oil_unit'])) {
                    $result['oil_unit'] = $result['approveData']['oil_unit'];
                }
            }
        }

        return $result;
    }

    /**
     * 创建｜编辑补录申请单
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function additionalAddOrUpdate($params)
    {
        list(
            $id, $card_no, $card_account_type, $driver_phone, $truck_no, $driver_name,
            $station_id, $oil_type, $oil_name, $oil_level, $tank_id, $gun_id, $oil_time,
            $oil_unit, $pay_money, $pay_price, $oil_num, $remark, $ticket_image, $truck_image,
            $other_image, $source, $force
            ) = array_values($params);

        $cardAccountArray = [1 => '现金账户'];
        $sourceNameArray = [1 => 'GMS'];
        $operator = Request::get('user_name');

        $imgHost = config('oss')['host'];
        $params['ticket_image'] = empty($params['ticket_image']) ? '' : (strstr($params['ticket_image'], $imgHost) ? $params['ticket_image'] : $imgHost . $params['ticket_image']);
        $params['truck_image'] = empty($params['truck_image']) ? '' : (strstr($params['truck_image'], $imgHost) ? $params['truck_image'] : $imgHost . $params['truck_image']);
        $params['other_image'] = empty($params['other_image']) ? '' : (strstr($params['other_image'], $imgHost) ? $params['other_image'] : $imgHost . $params['other_image']);
        $recordMirror = [
            'source_name'            => $sourceNameArray[$source],
            'oil_unit'               => $oil_unit,
            'oil_unit_name'          => HistoryModel::$OIL_UTIL[$oil_unit],
            'pay_money'              => $pay_money,
            'pay_price'              => $pay_price,
            'oil_num'                => $oil_num,
            'oil_time'               => $oil_time,
            'driver_name'            => $driver_name,
            'card_account_type'      => $card_account_type,
            'card_account_type_name' => $cardAccountArray[$card_account_type],
            'ticket_image'           => empty($ticket_image) ? '' : (strstr($ticket_image, $imgHost) ? $ticket_image : $imgHost . $ticket_image),
            'truck_image'            => empty($truck_image) ? '' : (strstr($truck_image, $imgHost) ? $truck_image : $imgHost . $truck_image),
            'other_image'            => empty($other_image) ? '' : (strstr($other_image, $imgHost) ? $other_image : $imgHost . $truck_image),
        ];


        /**
         * 补录时间校验
         */
        if (strtotime($oil_time) > time()) {
            throw new Exception('加油时间不得晚于当前时间,请核对', $this->errorCodeService->editApproveError(40301));
        }

        /**
         * 价格校验 1指定金额 2指定加油升数
         */
        if ($oil_unit == 1) {
            $realOilNum = $this->numberUtil->formatNumber($pay_money / $pay_price, 2);
            if (abs($realOilNum - $oil_num)) {
                throw new Exception('[客户应付金额]指定金额加油,升数' . $oil_num . '和可加升数' . $realOilNum . '不匹配,请核对', $this->errorCodeService->editApproveError(40303));
            }
        } else {
            $realMoney = $this->numberUtil->formatNumber($pay_price * $oil_num, 2);
            if (abs($realMoney - $pay_money)) {
                throw new Exception('[客户应付金额]指定升数加油,金额' . $pay_money . '和可加金额' . $realMoney . '不匹配,请核对', $this->errorCodeService->editApproveError(40303));
            }
        }

        /**
         * 卡片、司机信息的校验
         */
        if (strcmp(strlen($card_no), 16)) {
            throw new Exception('卡号长度不是16位,请核对', $this->errorCodeService->editApproveError(40304));
        }
        $cardInfo = $this->fossUser->getCardInfoByCardNo(['vice_no' => $card_no]);
        if (empty($cardInfo)) {
            throw new Exception('无效的卡号:' . $card_no . ',请核对', $this->errorCodeService->editApproveError(40308));
        }
        if (!empty($truck_no) && strcmp($cardInfo['truck_no'], $truck_no)) {
            throw new Exception('1号卡绑定的车牌号和填写的不一致,请核对', $this->errorCodeService->editApproveError(40309));
        }

        if (strcmp($cardInfo['driver_tel'], $driver_phone)) {
            throw new Exception('1号卡绑定的司机手机号和填写的不一致,请核对', $this->errorCodeService->editApproveError(40310));
        }

        if (strcmp($cardInfo['card_owner'], $driver_name)) {
            throw new Exception('1号卡绑定的司机和填写的不一致,请核对', $this->errorCodeService->editApproveError(40311));
        }

        if ((empty($force)) && ($cardInfo['card_remain'] < $pay_money)) {
            throw new Exception('账户余额:' . $cardInfo['card_remain'] . '不足以支付本次补录,是否继续提交审核?', $this->errorCodeService->editApproveError(40312));
        }

        /**
         * 油站信息校验
         */
        $stationInfo = $this->fossStation->getStation($station_id);
        if (empty($stationInfo)) {
            throw new Exception('油站[id:' . $station_id . ']不存在,请核对', $this->errorCodeService->editApproveError(40305));
        }
        if ($stationInfo['card_classify'] != 2) {
            throw new Exception('油站:' . $stationInfo['station_name'] . '已下线,不允许退款,请核对', $this->errorCodeService->editApproveError(40313));
        }
        // 省、市名
        $provinceCityDict = $this->cityRepository->getProvinceCityDict(false)->keyBy('city_code')->toArray();
        $recordMirror = array_merge($recordMirror, [
            'station_code'  => $stationInfo['station_code'],
            'station_name'  => $stationInfo['station_name'],
            'supplier_name' => $stationInfo['supplier_name'],
            'province_code' => $stationInfo['provice_code'],
            'city_code'     => $stationInfo['city_code'],
            'province_name' => $provinceCityDict[$stationInfo['provice_code']]['city_name'],
            'city_name'     => $provinceCityDict[$stationInfo['city_code']]['city_name'],
        ]);


        // 校验油站
        $oilInfo = array_get($stationInfo, 'oil', []);
        if (empty($oilInfo)) {
            throw new Exception('所选油站无油品,请核对', $this->errorCodeService->editApproveError(40306));
        }

        // 校验油罐、油枪、油品
        $checkGunAndGoods = false;
        foreach ($oilInfo as $item) {
            $gunInfo = array_get($item, 'gun', []);
            if (!empty($gunInfo)) {
                foreach ($gunInfo as $value) {
                    if (!strcmp($value['gun_id'], $gun_id) && !strcmp($value['tank_id'], $tank_id)) {
                        $recordMirror = array_merge($recordMirror, [
                            'gun_id'    => $gun_id,
                            'gun_name'  => $value['gun_name'],
                            'tank_id'   => $tank_id,
                            'tank_name' => $value['tank_name'],
                        ]);
                        if (!strcmp($item['oil_type'], $oil_type) && !strcmp($item['oil_name'], $oil_name) && !strcmp($item['oil_level'], $oil_level)) {

                            $checkGunAndGoods = true;
                            break 2;
                        }
                    }
                }
            }
        }

        if (!empty($gun_id) && ($checkGunAndGoods == false)) {
            throw new Exception('所选站点的油罐、油枪不包含指定的油品,请核对', $this->errorCodeService->editApproveError(40307));
        }
        // 商品名称
        $oilDict = $this->dictRepository->getOilDict();
        $recordMirror = array_merge($recordMirror, [
            'oil_type'       => $oil_type,
            'oil_type_name'  => array_get($oilDict, 'oil_type.' . $oil_type, ''),
            'oil_name'       => $oil_name,
            'oil_name_name'  => array_get($oilDict, 'oil_name.' . $oil_name, ''),
            'oil_level'      => $oil_level,
            'oil_level_name' => array_get($oilDict, 'oil_level.' . $oil_level, ''),
            'goods'          =>
                array_get($oilDict, 'oil_type.' . $oil_type, '')
                . array_get($oilDict, 'oil_name.' . $oil_name, '')
                . array_get($oilDict, 'oil_level.' . $oil_level, ''),
        ]);

        /**
         * 价格校验
         */
        $selectPriceArray = [
            'spcode'     => $stationInfo['pcode'],
            'station_id' => $station_id,
            'oil_name'   => $oil_name,
            'oil_type'   => $oil_type,
            'oil_level'  => $oil_level,
            'time'       => $oil_time,
        ];

        $salePrice = $this->fossStation->getSalePrice($selectPriceArray);
        if (empty($salePrice)) {
            throw new Exception('补单选择的油站、油品不存在销价,请核对', $this->errorCodeService->editApproveError(40308));
        }

//        if (abs($pay_price - $salePrice['platform_price'])) {
//            throw new \Exception('司机结算单价:'.$pay_price.'和系统中司机结算单价'.$salePrice['platform_price'].'不一致,请核对', $this->errorCodeService->editApproveError(40309));
//        }

        if (empty($salePrice['supplier_price'])) {
            throw new Exception('站点应收单价为0,价格异常,无法补单,请核对', $this->errorCodeService->editApproveError(40310));
        }
        // 特殊规则处理:定金额加油,如果上游、下游单价相同,则直接使用下游金额
        $recordMirror = array_merge($recordMirror, [
            'price' => $salePrice['supplier_price'],
            'money' => ($salePrice['supplier_price'] == $pay_price) ? $pay_money : $this->numberUtil->formatNumber($salePrice['supplier_price'] * $oil_num, 2),
        ]);

        $insertOrUpdateArray = [
            'history_type'   => GasHistoryApproveModel::INSERT_HISTORY,
            'approve_mirror' => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
            'approve_status' => GasHistoryApproveModel::WAIT_APPROVE,
            'source'         => $source,
            'remark'         => $remark,
            'truck_no'       => $truck_no,
            'card_no'        => $card_no,
            'driver_phone'   => $driver_phone,
            'province_code'  => $stationInfo['provice_code'],
            'city_code'      => $stationInfo['city_code'],
            'station_id'     => $station_id,
            'creator'        => $operator,
            'updator'        => $operator
        ];

        return $this->approveInsertOrUpdate($insertOrUpdateArray, $id);
    }

    /**
     * 生成退款审核单
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function refundAdd($params)
    {
        $history_id = array_get($params, 'history_id', '');
        $remark = array_get($params, 'remark', '');
        $ticketImage = array_get($params, 'ticket_image', '');
        $truckImage = array_get($params, 'truck_image', '');
        $otherImage = array_get($params, 'other_image', '');
        $force = array_get($params, 'force', 0);
        $approvePass = array_get($params, 'approve_pass', 0);
        $source = Request::get('order_channel');
        $sourceName = Request::get('order_channel_enum')[$source];
        $operator = Request::get('user_name');

        /**
         * 订单退款走订单
         */
        $orderInfo = $this->newOrderRepository->getOneOrderByParams(['history_id' => $history_id], ['order_id', 'history_id'], true);
        if (!empty($orderInfo)) {
            $needParams = [
                'order_id'      => $orderInfo['order_id'],
                'remark'        => $remark,
                'ticket_image'  => $ticketImage,
                'truck_image'   => $truckImage,
                'other_image'   => $otherImage,
                'source'        => $source,
                'source_name'   => $sourceName,
                'operator'      => $operator,
                'force'         => $force,
                'approve_pass'  => $approvePass,
                'refund_system' => $params['refund_system'] ?? 1,
            ];
            return $this->orderApproveService->refundApprove($needParams);
        }


        /**
         * 交易流水
         */
        $select = [
            'card_no', 'truck_no', 'driver_phone', 'station_id', 'unit', 'xpcode_pay_money', 'data_type', 'money',
            'pay_money', 'xpcode_pay_price', 'price', 'pay_price', 'oil_num', 'oil_time', 'imgData', 'oil_type', 'oil_name',
            'oil_level', 'gun_id', 'tank_id', 'gascode', 'provice_code', 'city_code', 'log_type', 'pcode', 'driver_name',
            'old_id',
        ];
        $historyInfo = $this->historyRepository->getOneHistoryByParams(['id' => $history_id], $select, true);
        if (empty($historyInfo)) {
            throw new Exception('交易流水ID:' . $history_id . '不存在,请核对', $this->errorCodeService->editApproveError(40320));
        }
        if ($params['refund_system'] == 2) {
            $orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $historyInfo['old_id']], ['order_id', 'history_id'], true);
            if (!empty($orderInfo)) {
                $needParams = [
                    'order_id'      => $orderInfo['order_id'],
                    'remark'        => $remark,
                    'ticket_image'  => $ticketImage,
                    'truck_image'   => $truckImage,
                    'other_image'   => $otherImage,
                    'source'        => $source,
                    'source_name'   => $sourceName,
                    'operator'      => $operator,
                    'force'         => $force,
                    'approve_pass'  => $approvePass,
                    'refund_system' => $params['refund_system'],
                ];
                return $this->orderApproveService->refundApprove($needParams);
            }
        }
        if (!in_array($historyInfo['log_type'], [HistoryModel::COST_SUCCESS, HistoryModel::COST_ABNORMAL, HistoryModel::COST_SUCCESS_ADD])) {
            throw new Exception('当前交易流水不允许退款,请核实', $this->errorCodeService->editApproveError(40330));
        }

        /**
         * 退款幂等校验
         */
        $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
            'original_history_id' => $history_id,
            'approve_status' => [
                GasHistoryApproveModel::WAIT_APPROVE,
                GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                GasHistoryApproveModel::FINISH_AFTER_APPROVE
            ]
        ], ['id', 'approve_status'], true);
        if (!empty($approveInfo)) {
            throw new Exception('交易流水已存在退款生效状态的审核单,不允许重复发起退款,请核对', $this->errorCodeService->editApproveError(40321));
        }

        /**
         * 验证是否对G7能源账户开放
         */
        $oneCardPcode = $this->stationPcodeRepository->getOneCardPcode();
        if (!in_array($historyInfo['pcode'], $oneCardPcode)) {
            throw new Exception('非G7能源账户开放站点的交易流水,不允许退款,请核对!', $this->errorCodeService->editApproveError(40322));
        }

        /**
         * 四川壳牌询问OA
         */
        if (empty($force)) {
            if (in_array($historyInfo['pcode'], explode(',', env('PCODE_OA_CHECK_BEFORE_REFUND')))) {
                $orderId = array_get($this->historyPayRepository->getHistoryPayInfoByParams(['history_id' => $history_id]), 'order_id', $history_id);
                $orderInfo = $this->adapter->queryOrderExchange(['order_id' => $orderId]);
                if (abs(array_get($orderInfo, 'platform_order_status', 0) - 2) > 0) {
                    throw new Exception('这笔订单为壳牌的订单,可能已被核销,请确认后再发起退款!', $this->errorCodeService->editApproveError(40323));
                }
            }

            throw new Exception('这笔订单确定要发起退款吗?', $this->errorCodeService->editApproveError(40323));
        }

        $select = [
            'id', 'station_name', 'pcode', 'isstop', 'card_classify', 'station_type', 'station_brand', 'trade_type',
            'rebate_grade', 'provice_code', 'city_code', 'station_code'
        ];
        // 站信息
        $stationInfo = $this->stationRepository->getOneStationBasicInfoByParams(['id' => $historyInfo['station_id']], $select);
        if ($stationInfo['card_classify'] != 2) {
            throw new Exception('油站:' . $stationInfo['station_name'] . '已下线,不允许退款,请核对', $this->errorCodeService->editApproveError(40313));
        }
        // 机构信息
        $select = ['orgcode', 'orgname'];
        $orgInfo = $this->orgRepository->getOneOrgByParams(['orgcode' => $historyInfo['gascode']], $select);
        // 省市信息
        $provinceCityDict = $this->cityRepository->getProvinceCityDict(false)->keyBy('city_code')->toArray();
        // 油枪信息
        $gunInfo = $this->stationGunRepository->getOneGunByParams(['id' => $historyInfo['gun_id']]);
        // 油罐信息
        $tankInfo = $this->stationTankRepository->getOneTankByParams(['id' => $historyInfo['tank_id']]);
        // 商品信息
        $oilDict = $this->dictRepository->getOilDict();
        /**
         * 退款审核信息补充
         */
        $recordMirror = [
            'source_name'            => $sourceName,
            'oil_unit'               => $historyInfo['unit'],
            'oil_unit_name'          => HistoryModel::$OIL_UTIL[$historyInfo['unit']],
            'money'                  => $historyInfo['xpcode_pay_money'], //站点应收金额
            'pay_money'              => $historyInfo['data_type'] == '13' ? $historyInfo['money'] : $historyInfo['pay_money'], //客户应收金额
            'price'                  => $historyInfo['xpcode_pay_price'], //站点应收单价
            'pay_price'              => $historyInfo['pay_price'], //客户应付单价
            'oil_num'                => $historyInfo['oil_num'],
            'oil_time'               => $historyInfo['oil_time'],
            'driver_name'            => $historyInfo['driver_name'],
            'card_account_type'      => 1,
            'card_account_type_name' => '现金账户',
            'ticket_image'           => !empty($ticketImage) ? Upload::getSignUrl($ticketImage)  : '',
            'truck_image'            => !empty($truckImage) ? Upload::getSignUrl($truckImage)  : '',
            'other_image'            => !empty($otherImage) ? Upload::getSignUrl($otherImage)  : '',
            'station_code'           => $stationInfo['station_code'],
            'station_name'           => $stationInfo['station_name'],
            'supplier_name'          => $orgInfo['orgname'],
            'province_code'          => $historyInfo['provice_code'],
            'city_code'              => $historyInfo['city_code'],
            'province_name'          => $provinceCityDict[$historyInfo['provice_code']]['city_name'],
            'city_name'              => $provinceCityDict[$historyInfo['city_code']]['city_name'],
            'gun_id'                 => $historyInfo['gun_id'],
            'gun_name'               => $gunInfo['name'],
            'tank_id'                => $historyInfo['tank_id'],
            'tank_name'              => $tankInfo['name'],
            'oil_type'               => $historyInfo['oil_type'],
            'oil_type_name'          => array_get($oilDict, 'oil_type.' . $historyInfo['oil_type'], ''),
            'oil_name'               => $historyInfo['oil_name'],
            'oil_name_name'          => array_get($oilDict, 'oil_name.' . $historyInfo['oil_name'], ''),
            'oil_level'              => $historyInfo['oil_level'],
            'oil_level_name'         => array_get($oilDict, 'oil_level.' . $historyInfo['oil_level'], ''),
            'goods'                  =>
                array_get($oilDict, 'oil_type.' . $historyInfo['oil_type'], '')
                . array_get($oilDict, 'oil_name.' . $historyInfo['oil_name'], '')
                . array_get($oilDict, 'oil_level.' . $historyInfo['oil_level'], ''),
        ];


        $insertArray = [
            'original_history_id' => $history_id, // 退款单流水号
            'history_type'        => GasHistoryApproveModel::CANCEL_HISTORY,
            'approve_mirror'      => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
            'approve_status'      => GasHistoryApproveModel::WAIT_APPROVE,
            'source'              => $source,
            'remark'              => $remark,
            'truck_no'            => $historyInfo['truck_no'],
            'card_no'             => $historyInfo['card_no'],
            'driver_phone'        => $historyInfo['driver_phone'],
            'province_code'       => $historyInfo['provice_code'],
            'city_code'           => $historyInfo['city_code'],
            'station_id'          => $historyInfo['station_id'],
            'creator'             => $operator,
            'updator'             => $operator,
            'refund_system'       => $params['refund_syste'] ?? 0,
        ];

        $approveId = $this->historyApproveRepository->approveInsert($insertArray);

        if (!empty($approvePass) && !empty($approveId)) {
            $this->approvePass(['id' => $approveId]);
        } else {
            // 发送待审核消息通知
            event(new RefundNotice($approveId));
        }
        return true;
    }

    /**
     * G7能源账户审核单审核通过
     *
     * @param $params
     * @return bool
     * @throws Exception|Throwable
     */
    public function approvePass($params)
    {
        // 加锁
        $this->redisService->addApprovePassLock($params['id']);
        $operator = Request::get('user_name');
        $approvalReason = array_get($params, 'approval_reason', '');

        /**
         * 订单维度补录 or退款
         */
        // 退款审核通过
        try {

            $approveInfo = $this->getApproveInfoById($params['id']);
            if ($approveInfo['refund_system'] == 2) {
                throw new Exception('操作失败！退客户系统的异常订单不允许2次审核，可去【订单列表】页再次发起退款',
                    $this->errorCodeService->approveError(40303));
            }
            // 已驳回
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::REJECT_APPROVE) {
                throw new Exception('审核单已驳回,无法通过审核,请核实', $this->errorCodeService->approveError(40310));
            }
            // 已完结无法审核通过
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::FINISH_AFTER_APPROVE) {
                throw new Exception('审核单已完结,无法通过审核,请核实', $this->errorCodeService->approveError(40311));
            }
            // 审核通过
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::SUCCESS_AFTER_APPROVE) {
                throw new Exception('该审核单已通过,请勿重复操作', $this->errorCodeService->approveError(40303));
            }
            if (!empty($approveInfo['order_id']) && ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY)) {
                return $this->orderApproveService->approvePass($params);
            }
            // 补录审核通过
            if (empty($approveInfo['order_id']) && ($approveInfo['history_type'] == GasHistoryApproveModel::INSERT_HISTORY)) {
                return $this->orderApproveService->approvePass($params);
            }
            // 异常修改审核通过
            if (!empty($approveInfo['order_id']) && ($approveInfo['history_type'] == GasHistoryApproveModel::UPDATE_HISTORY)) {
                return $this->orderApproveService->approvePass($params);
            }
        } catch (Throwable $t) {

            $this->redisService->delApprovePassLock($params['id']);
            throw $t;
        }

        try {
            // 交易流水是否已作废
            if ($approveInfo['original_history_id']) {
                $logType = $this->historyRepository->getOneHistoryByParams(['id' => $approveInfo['original_history_id']], ['id', 'log_type'], true)['log_type'];
                if (in_array($logType, [HistoryModel::COST_INVALID, HistoryModel::COST_INVALID_2, HistoryModel::COST_ORIGINAL])) {
                    $updateArray = [
                        'approve_status'          => GasHistoryApproveModel::FINISH_AFTER_APPROVE,
                        'approve_abnormal_reason' => '交易流水已作废',
                        'updator'                 => $operator
                    ];
                    $this->historyApproveRepository->approveUpdateById($params['id'], $updateArray);

                    throw new Exception('交易流水ID:' . $approveInfo['original_history_id'] . '已作废,无法退款,请核实', $this->errorCodeService->approveError(40312));
                }
            }

            // 审核通过
            $updateArray = [
                'approve_abnormal_reason' => $approvalReason,
                'approve_status'          => GasHistoryApproveModel::SUCCESS_AFTER_APPROVE,
                'updator'                 => $operator
            ];

            try {
                // 退款
                if ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {
                    // 调用gas生成退款流水
                    $needArray = [
                        'history_id' => $approveInfo['original_history_id'],
                    ];
                    $result = $this->gas->makeOilRecordVoid($needArray);
                    $updateArray['history_id'] = array_get($result, 'new_history.id', '');
                } elseif ($approveInfo['history_type'] == GasHistoryApproveModel::INSERT_HISTORY) {
                    // 补录
                    $approveMirror = json_decode($approveInfo['approve_mirror'], true);
                    // 显示金额是gas判断是否使用gms传递的金额计算的标识,gms无显示金额的概念
                    $needArray = [
                        'card_no'      => $approveInfo['card_no'],
                        'oil_num'      => $approveMirror['oil_num'],
                        'amount'       => $approveMirror['pay_money'], //显示金额
                        'price'        => $approveMirror['pay_price'], //显示单价
                        'pay_money'    => $approveMirror['pay_money'],
                        'pay_price'    => $approveMirror['pay_price'],
                        'xpcode_price' => $approveMirror['price'],
                        'xpcode_money' => $approveMirror['money'],
                        'oil_time'     => $approveMirror['oil_time'],
                        'station_id'   => $approveInfo['station_id'],
                        'tank_id'      => $approveMirror['tank_id'],
                        'gun_id'       => $approveMirror['gun_id'],
                        'truck_no'     => $approveInfo['truck_no'],
                        'driver_name'  => $approveMirror['driver_name'],
                        'img_urls'     => [
                            'ticketUrl' => $approveMirror['ticket_image'],
                            'truckUrl'  => $approveMirror['truck_image'],
                            'carnumUrl' => $approveMirror['other_image'],
                        ],
                        'log_type'     => HistoryModel::COST_SUCCESS_ADD
                    ];
                    // 调用gas生成补录流水
                    $result = $this->gas->makeNewOilRecord($needArray);
                    $updateArray['history_id'] = $result['id'];
                }
                $this->historyApproveRepository->approveUpdateById($params['id'], $updateArray);

                // 退款审核通过发送通知
                if ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {
                    event(new RefundNotice($params['id']));
                }
                // 解锁
                $this->redisService->delApprovePassLock($params['id']);
                return true;
            } catch (Exception $e) {
                $updateArray = [
                    'approve_status'          => GasHistoryApproveModel::FAIL_AFTER_APPROVE,
                    'approve_abnormal_reason' => $e->getMessage(),
                    'updator'                 => $operator
                ];
                $this->historyApproveRepository->approveUpdateById($params['id'], $updateArray);
                throw new Exception('审核通过且失败,原因:' . $e->getMessage(), $this->errorCodeService->approveError(40312));
            }
        } catch (Exception $e) {
            // 解锁
            $this->redisService->delApprovePassLock($params['id']);
            throw $e;
        }
    }

    /**
     * 审核驳回
     *
     * @param $params
     * @return bool|mixed
     * @throws Exception
     */
    public function approveRefuse($params)
    {
        $id = array_get($params, 'id', 0);
        $operator = Request::get('user_name', '系统');
        $approvalReason = array_get($params, 'approval_reason', '');
        DB::connection('mysql_gas')->beginTransaction();
        try {

            $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams([
                'id' => $id,
            ], [
                'id',
                'approve_status',
                'original_history_id',
                'order_id',
                'history_type',
                'approve_mirror',
                'source',
                'refund_system',
            ], true, true);
            if ($approveInfo['refund_system'] == 2) {
                throw new Exception('操作失败！退客户系统的异常订单不允许2次审核，可去【订单列表】页再次发起退款',
                    $this->errorCodeService->approveError(40303));
            }
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::REJECT_APPROVE) {
                throw new Exception('该审核单已驳回,请勿重复操作', $this->errorCodeService->approveError(40303));
            }
            // 已审核完成无法驳回
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::SUCCESS_AFTER_APPROVE) {
                throw new Exception('审核单已审核通过,无法驳回,请核实', $this->errorCodeService->approveError(40302));
            }
            // 已完结无法驳回
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::FINISH_AFTER_APPROVE) {
                throw new Exception('审核单已完结,无法驳回,请核实', $this->errorCodeService->approveError(40303));
            }
            // 已完结无法驳回
            if ($approveInfo['approve_status'] == GasHistoryApproveModel::REFUND_SUCCESS_AFTER_APPROVE) {
                throw new Exception('审核单已完结,无法驳回,请核实', $this->errorCodeService->approveError(40303));
            }

            if ($approveInfo['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {
                // 订单状态
                $orderInfo = $this->newOrderRepository->getOneOrderByParams(['order_id' => $approveInfo['order_id']], ['order_id', 'order_status', 'third_order_id', 'supplier_code']);
                if (in_array($orderInfo['supplier_code'], CommonDefine::linkedRefundApproveSupplier()) and (!isset($params['source']) or $params['source'] != 'oa')) {
                    throw new Exception('请联系' . $orderInfo['ext']['supplier_name'] . '运营人员进行审核', $this->errorCodeService->approveError(40342));
                }
            }

            $updateArray = [
                'approve_status'          => GasHistoryApproveModel::REJECT_APPROVE,
                'updator'                 => $operator,
                'approve_abnormal_reason' => $approvalReason,
            ];
            $this->historyApproveRepository->approveUpdateById($id, $updateArray);
            if (!empty($approveInfo['order_id'])) {
                $updateOrder = [
                    'updator'      => $operator,
                    'order_status' => OrderModel::SUCCESS_PAY
                ];
                $this->newOrderRepository->updateOrderByOrderId($approveInfo['order_id'], $updateOrder);
            }
            DB::connection('mysql_gas')->commit();
        } catch (Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw $e;
        }
        //bugfix下游客户申请退款驳回回调OA
        if($approveInfo['source'] == 6) {
            $this->messageService->pushData2OA([
                'order_id' => $approveInfo['order_id'],
                'org_code' => json_decode($approveInfo['approve_mirror'], true)['org_code'],
                'audit_status' => 2,
                'audit_reason' => $approvalReason,
            ], 'REFUND_APPLY_AUDIT');
        }
        // 驳回发送消息通知
        event(new RefundNotice($id));
        return true;
    }

    /**
     * 根据ID获取审核单详情
     *
     * @param $approveId
     * @param array $select
     * @return array
     * @throws Exception
     */
    public function getApproveInfoById($approveId, $select = ['*'])
    {
        $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $approveId], $select, true);
        if (empty($approveInfo)) {
            throw new Exception('无效的审核单ID,请核实', $this->errorCodeService->approveError(40301));
        }

        return $approveInfo;
    }

    /**
     * 根据订单ID获取审核单详情
     *
     * @param $orderId
     * @param array $select
     * @return array
     * @throws Exception
     */
    public function getApproveInfoByOrderId($orderId, $select = ['*'])
    {
        $res = [];
        $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['order_id' => $orderId], $select, true);
        if ($approveInfo) {
            $res = $approveInfo;
        }

        return $res;
    }

    /**
     * 创建｜更新审核单
     *
     * @param $insertOrUpdateArray
     * @param string $id
     * @return mixed
     * @throws Exception
     */
    protected function approveInsertOrUpdate($insertOrUpdateArray, $id = '')
    {
        if ($id) {
            $approveInfo = $this->historyApproveRepository->getOneApproveInfoByParams(['id' => $id], ['id', 'approve_status'], true);
            if (empty($approveInfo)) {
                throw new Exception('无效的审核单ID:' . $id . ',请核对', $this->errorCodeService->selectApproveError(40310));
            }
            if (in_array($approveInfo['approve_status'], [GasHistoryApproveModel::SUCCESS_AFTER_APPROVE, GasHistoryApproveModel::FINISH_AFTER_APPROVE, GasHistoryApproveModel::REJECT_APPROVE])) {
                throw new Exception('审核单已完成,不支持修改,请核实', $this->errorCodeService->selectApproveError(40311));
            }
            return $this->historyApproveRepository->approveUpdateById($id, $insertOrUpdateArray);
        } else {
            return $this->historyApproveRepository->approveInsert($insertOrUpdateArray);
        }
    }



    public function exportApprovePaginate($params){
        //获取导出数据总量
        $whereParams = [];
        if (!empty($params['order_id'])) {
            $whereParams['order_id'] = explode(',', $params['order_id']);
        }
        if (!empty($params['history_type'])) {
            $whereParams['history_type'] = explode(',', $params['history_type']);
        }
        if (!empty($params['approve_status'])) {
            $whereParams['approve_status'] = explode(',', $params['approve_status']);
        }
        if (!empty($params['station_id'])) {
            $whereParams['station_id'] = explode(',', $params['station_id']);
        }
        if (!empty($params['card_no'])) {
            $whereParams['card_no'] = $params['card_no'];
        }
        if (!empty($params['truck_no'])) {
            $whereParams['truck_no'] = $params['truck_no'];
        }
        if (!empty($params['driver_phone'])) {
            $whereParams['driver_phone'] = $params['driver_phone'];
        }
        if (!empty($params['creator'])) {
            $whereParams['creator'] = $params['creator'];
        }
        // 创建开始时间
        if (! empty($params['ge_createtime'])) {
            $whereParams['ge_create_time'] = $params['ge_createtime'];
        }
        // 创建结束时间
        if (! empty($params['le_createtime'])) {
            $whereParams['le_create_time'] = $params['le_createtime'];
        }
        //订单类型
        if (isset($params['order_sale_type'])) {
            $whereParams['order_sale_type'] = $params['order_sale_type'];
        }
        $count = $this->historyApproveRepository->getHistoryApproveCount($whereParams);

        if (empty($count)) {
            throw new \Exception('查询结果为空,无数据导出!');
        }
        /**
         * 同步导出
         */
        $fileName = date('YmdHis').'异常订单审核.csv';
        $this->exportCsv->getCsvHeader($fileName);
        $resource = fopen('php://output', 'a');
        $exportHeader = [
            '申请单ID','订单类型', '异常订单类型','退款类型','审核状态','异常原因','账号','站点名称','商品', '枪金额', '油机价','站点应收总额','站点应收单价','客户应结总额','客户应付单价',
            '数量','车牌号','司机姓名','手机号','原始单号','新订单号','三方订单号','实际加油时间','数据源','备注','创建人','创建时间','更新人','更新时间'
        ];
        fwrite($resource, "\xEF\xBB\xBF".implode(',', $exportHeader)."\n");
        /**
         * 每次导出1000条
         */
        $limit = 1000;

        for ($page = 1; $page <= ceil($count/$limit); $page++) {
            $result = $this->historyApproveRepository->getApprovePaginate($whereParams, ['gas_history_approve.*','gas_order_new.order_sale_type'], $page, $limit, true);
            $exportStr = '';
            foreach ($result['data'] as $item) {
                $item = array_merge($item, json_decode($item['approve_mirror'], true));
                unset($item['approve_mirror']);
                $refundSystemName = '';
                if ($item['history_type'] == GasHistoryApproveModel::CANCEL_HISTORY) {
                    if ($item['refund_system'] != 0) {
                        $refundSystemName = $item['refund_system'] == 1 ? '退GMS' : '退客户系统';
                    }
                }
                $exportArray = [
                    'id'                        => $item['id'],
                    'order_sale_type_name'      => OrderModel::ORDER_SALE_TYPE[$item['order_sale_type']] ?? '',
                    'history_type_name'         => GasHistoryApproveModel::$HISTORY_TYPE_ENUM[$item['history_type']],
                    'refund_system_name'        => $refundSystemName,
                    'approve_status_name'       => GasHistoryApproveModel::$APPROVE_STATUS_ENUM[$item['approve_status']],
                    'approve_abnormal_reason'   => $item['approve_abnormal_reason']??'',
                    'card_no'                   => isset($item['card_no'])?$item['card_no']."\t":'',
                    'station_name'              => $item['station_name']??'',
                    'goods'                     => $item['goods']??"",
                    'money'                     => sprintf('%.2f', $item['money'] ?? 0),
                    'mac_price'                 => sprintf('%.2f', $item['mac_price'] ?? 0),
                    'mac_amount'                => sprintf('%.2f', $item['mac_amount'] ?? 0),
                    'price'                     => sprintf('%.2f', $item['price'] ?? 0),
                    'pay_money'                 => sprintf('%.2f', $item['pay_money'] ?? 0),
                    'pay_price'                 => sprintf('%.2f', $item['pay_price'] ?? 0),
                    'oil_num'                   => sprintf('%.2f', $item['oil_num'] ?? 0),
                    'truck_no'                  => $item['truck_no']??'',
                    'driver_name'               => $item['driver_name']??'',
                    'driver_phone'              => $item['driver_phone']??'',
                    'original_history_id'       => isset($item['original_history_id'])?$item['original_history_id']."\t":'',
                    'history_id'                => $item['history_id']??'',
                    'third_order_id'            => $item['third_order_id']??'',
                    'oil_time'                  => $item['oil_time']??'',
                    'source_name'               => $item['source_name']??'',
                    'remark'                    => $item['remark']??'',
                    'creator'                   => $item['creator'],
                    'create_time'               => $item['create_time'],
                    'updator'                   => $item['updator'],
                    'update_time'               => $item['update_time'],
                ];
                $exportStr .= implode(',', $exportArray)."\n";
            }
            fwrite($resource, $exportStr);

            if (ob_get_level() > 0) {
                ob_flush();
                flush();
            }
        }
        fclose($resource);
    }
}