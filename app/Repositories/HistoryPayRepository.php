<?php


namespace App\Repositories;


use App\Models\HistoryPayModel;

class HistoryPayRepository
{
    protected $historyPayModel;

    public function __construct
    (
       HistoryPayModel $historyPayModel
    )
    {
        $this->historyPayModel = $historyPayModel;
    }

    /**
     * 根据指定条件批量获取订单信息
     *
     * @param $params
     * @param string $select
     * @param bool $toArray
     * @return array
     */
    public function getHistoryPayInfoByParamsBatch($params, $select = '*', $toArray = true)
    {
        return $this->historyPayModel->getHistoryPayInfoByParamsBatch($params, $select, $toArray);
    }

    /**
     * 根据指定条件获取1条订单信息
     *
     * @param $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getHistoryPayInfoByParams($whereParams, $select = ['*'], $toArray = true)
    {
        return $this->historyPayModel->getHistoryPayInfoByParams($whereParams, $select, $toArray);
    }
}