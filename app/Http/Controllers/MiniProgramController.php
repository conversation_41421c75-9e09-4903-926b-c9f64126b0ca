<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2020/02/05
 * Time: 16:35
 */
namespace App\Http\Controllers;
use App\Library\Helper\Common;
use App\Library\Request;
use App\Services\CardService;
use App\Services\CouponService;
use App\Services\ElectricityOrderService;
use App\Services\MessageService;
use App\Services\MiniOrderService;
use App\Services\NewOrderService;
use App\Services\PaymentService;
use App\Services\QrCodeService;
use App\Services\StationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class MiniProgramController extends Controller
{

    protected $paymentService;
    protected $messageSrv;
    protected $conponSrv;

    public function __construct(PaymentService $payMent,MessageService $msgSrv,CouponService $couponSrv)
    {
        $this->paymentService = $payMent;
        $this->messageSrv = $msgSrv;
        $this->conponSrv = $couponSrv;

    }
    //获取油站,油品数据
    public function getOilTypeArr(Request $request)
    {
        $params = $request->all();
        $rule = [
            'station_id' => 'required|string|min:8',
        ];
        $messages = [
            'station_id.required' => "请选择油站",
        ];
        $this->validator($params,$rule,$messages);

        $data = (new StationService())->stationExtInfo($params);
        return response()->json(['code'=>0,'data'=> $data ]);
    }

    public function getOilType(Request $request)
    {
        //app(NewOrderService::class)->checkGray('18610390918');
        //echo 111;exit;
        $params = $request->all();
        $rule = [
            'station_id' => 'required|string|min:8',
            //'card_no' => 'required|string|min:8',
        ];
        $messages = [
            'station_id.required' => "请选择油站",
            //'card_no.required' => "请选择G7能源账户",
        ];
        $this->validator($params,$rule,$messages);

        //过滤无用参数
        $condition['station_id'] = $params['station_id'];
        //$condition['card_no'] = $params['card_no'];
        $header = Request::httpHeaders();
        $condition['user_id'] = $header['x-g7-api-uid'][0];
        $data = (new StationService())->getStationGoods($condition);
        return response()->json(['code'=>0,'data'=> $data ]);
    }

    //获取油品单价
    public function oilTradePrice(Request $request)
    {
        $params = $request->all();
        $rule = [
            'card_no' => 'required|string|min:8',
            'oilGunId' => 'required|string|min:8',
        ];
        $messages = [
            'card_no.required' => "请选择账户",
            'oilGunId.required' => "请选择枪号",
        ];
        $this->validator($params,$rule,$messages);

        $data = (new StationService())->getOilTradePrice($params);
        //gms获取单价
        //$data = (new StationService())->getGmsOilPrice($params);
        return response()->json(['code'=>0,'data'=> $data ]);
    }

    //油站主动付款下单接口
    public function createOrder(Request $request)
    {
        $params = $request->all();
        $rule = [
            'station_id' => 'required|string|min:8',
            'oilGunId' => 'required|string|min:8',
            'card_no' => 'required|string|min:8',
            #'money' => 'required|numeric',
            'price' => 'required|numeric',
            'custom_history_id' => 'required|string|min:32',
        ];
        $messages = [
            'station_id.required' => "请选择油站",
            'oilGunId.required' => "请选择枪号",
            'card_no.required' => "请选择账户",
            #'money.numeric' => '请输入合法的金额',
            'price.numeric' => '结算单价不能为空',
            'custom_history_id.required' => '请生成交易流水号',
        ];
        $this->validator($params,$rule,$messages);
        //todo调用gas下单接口
        $data = (new MiniOrderService())->submitStationPay($params);
        return response()->json(['code'=>0,'data'=> $data ]);
    }

    //生成壳牌付款码
    public function createPayMentQrCode(Request $request)
    {
        $params = $request->all();
        $rule = [
            'history_id' => 'required|string|min:2',
        ];
        $messages = [
            'history_id.required' => "交易ID不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = (new MiniOrderService())->payMentQrcode($params);
        return response()->json(['code'=>0,'data'=> $data ]);
    }

    //支付成功页,获取服务器时间
    public function getCurrentTime()
    {
        $data['timestamp'] = time();
        $data['time'] = Common::nowTime();
        return response()->json(['code' => 0,'data'=>$data]);
    }

    //订单列表,获取支付信息
    public function getPayData(Request $request)
    {
        $params = $request->all();
        $rule = [
            'history_id' => 'required|string|min:2',
        ];
        $messages = [
            'history_id.required' => "交易ID不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = (new StationService())->getTradeDetail($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //支付完成后,订单详情
    public function getPayDetail(Request $request)
    {
        $params = $request->all();
        $rule = [
            'history_id' => 'required|string|min:2',
        ];
        $messages = [
            'history_id.required' => "交易ID不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = (new StationService())->deductSuccessDetail($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //获取账单列表
    public function getTradesList(Request $request)
    {
        $params = $request->all();
        if(!isset($params['pageNo']) || empty($params['pageNo'])){
            $params['page'] = 1;
        }else{
            $params['page'] = $params['pageNo'];
        }
        unset($params['pageNo']);
        if(!isset($params['pageSize']) || empty($params['pageSize'])){
            $params['limit'] = 10;
        }else{
            $params['limit'] = $params['pageSize'];
        }
        unset($params['pageSize']);
        if(!isset($params['bill_time']) || empty($params['bill_time'])){
            $params['bill_time'] = date("Y-m-01");
        }else{
            $params['bill_time'] = $params['bill_time']."-01";
        }
        $header = Request::httpHeaders();
        $params['user_id'] = $header['x-g7-api-uid'][0];
        $params['bill_end_time'] = date("Y-m-d",strtotime($params['bill_time']."+1 month"));
        $data = (new StationService())->getTrades($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //获取G7能源账户流水
    public function getCardStream(Request $request)
    {
        $params = $request->all();
        if(!isset($params['pageNo']) || empty($params['pageNo'])){
            $params['page'] = 1;
        }else{
            $params['page'] = $params['pageNo'];
        }
        unset($params['pageNo']);
        if(!isset($params['pageSize']) || empty($params['pageSize'])){
            $params['limit'] = 10;
        }else{
            $params['limit'] = $params['pageSize'];
        }
        unset($params['pageSize']);
        if(!isset($params['bill_time']) || empty($params['bill_time'])){
            $params['bill_time'] = date("Y-m-01");
        }else{
            $params['bill_time'] = $params['bill_time']."-01";
        }
        $header = Request::httpHeaders();
        $params['user_id'] = $header['x-g7-api-uid'][0];
        $params['bill_end_time'] = date("Y-m-d",strtotime($params['bill_time']."+1 month"));
        $data = (new CardService())->getCardBill($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //获取G7能源账户流水详情
    public function getStreamDetail(Request $request)
    {
        $params = $request->all();
        $rule = [
            'res_type' => 'required',
            'res_id' => 'required',
        ];
        $messages = [
            'res_type.required' => "类型不能为空",
            'res_id.required' => "资源id不能为空",
        ];
        $this->validator($params,$rule,$messages);
        switch ($params['res_type']){
            case 20: case 30:
                $data = (new CardService())->getAssignDetail(['detail_id'=>$params['res_id']]);
                if($data['actual_money'] > 0){
                    $data['assign_money'] = $data['actual_money'];
                }
                $data['res_type'] = $params['res_type'];
                $data['type_txt'] = $params['res_type'] == 30 ? "圈回" : "分配";
                $data['status_txt'] = $data['type_txt']."成功";
                $data['remark_work'] = array_get($data,"remark_work","");
                $data['money_txt'] = array_get($data,"assign_money","");
            break;
            case 40:case 50:
                $oil_com = array_get($params,"oil_com","20");
                $data = (new CardService())->getTransferDetail(['id'=>$params['res_id']]);
                $data['res_type'] = $params['res_type'];
                $data['type_txt'] = $params['res_type'] == 50 ? "划拨到账" : "油费划拨";
                $data['oil_com_txt'] = $oil_com == 30 ? "发财账户" : "充值账户";
                $data['status_txt'] = $params['res_type'] == 50 ? "到账成功" : "划拨成功";
                $data['remark_work'] = $data['remark_work'] ? $data['remark_work'] : "";
                $data['from_orgname'] = $data['from_orgname'] ? $data['from_orgname'] : "";
                $data['into_orgname'] = $data['into_orgname'] ? $data['into_orgname'] : "";
                $data['money_txt'] = $params['res_type'] == 40 ? number_format(($data['money'] * -1),2,".","") : $data['money'];
            break;
            default:
                $condition['history_id'] = $params['res_id'];
                $data = (new CardService())->getTradeDetail($condition);
        }
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //扫码加油,获取油站id
    public function getStationId(Request $request)
    {
        $params = $request->all();
        $rule = [
            'qr_link' => 'required|string|min:8',
        ];
        $messages = [
            'qr_link.required' => "二维码内容不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = (new StationService())->getStationByUrl($params);
        return response()->json(['code'=>0,"data"=>['station_id'=>$data]]);
    }

    //长连接,发送消息
    public function pushMsg(Request $request)
    {
        $params = $request->all();
        $rule = [
            'pre_history_id' => 'required|string|min:4',
            'card_no'        => 'required|string|min:8'
        ];
        $messages = [
            'pre_history_id.required' => "待支付订单不能为空",
            'card_no.required' => "账户不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = (new StationService())->msg2User($params);
        return response()->json(['code'=>0,"data"=>['success']]);
    }

    //验证密码及下单
    public function checkPwd(Request $request)
    {
        $params = $request->all();
        $rule = [
            'pre_history_id' => 'required|string|min:4',
            "password"       => 'required|string|min:6',
            'card_no'        => 'required|string|min:8',
        ];
        $messages = [
            'pre_history_id.required' => "待支付订单不能为空",
            'password.required' => "请输入账户密码",
            'card_no.required'  => '请选择账户',
        ];
        $this->validator($params,$rule,$messages);
        $source = array_get($params,"source",0);

        if( isset($params['is_new']) && $params['is_new'] == 1 ){
            $params['orderId'] = $params['pre_history_id'];
            unset($params['pre_history_id']);
            $res = (new MiniOrderService())->submitOrder($params);
            $data = array_get($res,"history_id","");
        }else{
            $params['trade_from'] = array_get($params,"from",201);
            if($source == 1){
                $params['order_id'] = $params['pre_history_id'];
                unset($params['pre_history_id']);
                $payRes = $this->paymentService->checkPasswordAndPay($params);
                if( is_array($payRes) ){
                    $data = array_get($payRes,"history_id","");
                }else{
                    $data = $payRes;
                }
            }else {
                $data = (new MiniOrderService())->createTrades($params);
            }
        }
        return response()->json(['code'=>0,"data"=>["history_id"=>$data]]);
    }

    //在线付款支付确认
    public function paySure(Request $request)
    {
        $params = $request->all();
        $rule = [
            'oilGunId' => 'required|string|min:8',
            'card_no' => 'required|string|min:8',
            #'money' => 'required|numeric',
        ];
        $messages = [
            'oilGunId.required' => "请选择枪号",
            'card_no.required' => "请选择账户",
            #'money.numeric' => '请输入金额',
        ];
        $this->validator($params,$rule,$messages);
        $data = (new MiniOrderService())->paySure($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //车主邦下单
    public function createPreOrder(Request $request)
    {
        $params = $request->all();
        $rule = [
            'oilGunId' => 'required|string|min:8',
            'card_no' => 'required|string|min:8',
            'actucal_money' => 'required|numeric', //实付金额
            'gunNumber' => 'required|numeric',
            'priceGun' => 'required|numeric', //油枪价格
            'amountGun' => 'required|numeric', //输入金额
            'price' => 'required|numeric', //结算价
            'actual_price' => 'required|numeric', //原始结算价
        ];
        $messages = [
            'card_no.required' => "请选择账户",
            'oilGunId.required' => "请选择枪号",
            'actucal_money.numeric' => '请输入实付金额',
            'gunNumber.required' => '请输入枪号',
            'amountGun.required'  => '请输入支付金额',
            'priceGun.required'  => '油枪单价不能为空',
            'price.required'  => '结算单价不能为空',
            'actual_price.required' => '结算价不能为空',
        ];
        $this->validator($params,$rule,$messages);
        $header = Request::httpHeaders();
        $params['user_id'] = $header['x-g7-api-uid'][0];
        $data = (new MiniOrderService())->placeOrder($params);
        return response()->json(['code'=>0,"data"=>['orderId'=>$data['orderId']]]);
    }

    //车主邦支付
    public function submitOrder(Request $request)
    {
        $params = $request->all();
        $rule = [
            'orderId' => 'required|string|min:8',
            'card_no' => 'required|string|min:8',
            'station_id' => 'required|string|min:8',
        ];
        $messages = [
            'orderId.required' => "预支付订单不能为空",
            'card_no.required' => '请选择账户',
            'station_id.required' => '请选择油站',
        ];
        $this->validator($params,$rule,$messages);
        $data = (new MiniOrderService())->submitOrder($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //获取营销活动
    public function getActConf(Request $request)
    {
        $params = $request->all();

        $header = Request::httpHeaders();
        $params['user_id'] = $header['x-g7-api-uid'][0];

        $data = (new MiniOrderService())->getActSwitch($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    //获取电子券类别
    public function getCouponType(Request $request)
    {
        $params = $request->all();
        $rule = [
            'station_id' => 'required|string|min:8',
            'oil_name'   => 'nullable|alpha_num',
        ];
        $messages = [
            'station_id.required' => "请选择油站",
            'oil_name.alpha_num' => '油品名称编码不正确',
        ];
        $this->validator($params,$rule,$messages);
        $data = $this->conponSrv->getCouponType($params);
        return response()->json(['code'=>0,"data"=>$data]);
    }

    public function getExpiredSoonTripartiteCouponList(): JsonResponse
    {
        $data = app(NewOrderService::class)->getExpiredSoonTripartiteCouponsForDriver(
            Request::httpHeaders()['x-g7-api-uid'][0]);
        return response()->json(['code' => 0, "data" => $data]);
    }

    /**
     * 通过司机上个月的订单获取使用过的车牌号
     * @return JsonResponse
     */
    public function getTruckNoThroughLastMonthOrderOfDriver(): JsonResponse
    {
        return response()->json([
            'code' => 0,
            "data" => app(NewOrderService::class)->getTruckNoThroughLastMonthOrderOfDriver(
                Request::httpHeaders()['x-g7-api-uid'][0]
            )
        ]);
    }

    //检测是否需要拍车牌
    public function isMustTruckNo(Request $request)
    {
        $params = $request->all();
        $rule = [
            'vice_no' => 'required|string',
        ];
        $messages = [
            'vice_no.required' => "卡号不能为空",
        ];
        $this->validator($params,$rule,$messages);

        $data = (new CardService())->isMustTruckNo($params);

        return response()->json(['code'=>0,"data"=>$data]);
    }
}