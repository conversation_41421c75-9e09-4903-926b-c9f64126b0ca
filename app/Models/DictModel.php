<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

class DictModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_dict';

    protected $guarded = ["id"];

    protected $fillable = ['id','dict_type',"dict_data"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 获取油品字典
     *
     * @return array
     */
    public function getOilDict()
    {
        $rows = [];
        $oil = self::query()->select('id','parent_id','dict_type','dict_data')->whereIn('dict_type', ['oil_type','oil_name','oil_level'])->orderBy('orderby', 'asc')->get();
        $oil = $oil->groupBy('dict_type');
        $oil['oil_name_relation_type'] = $oil['oil_type']->filter(function($item){
            return !in_array($item['parent_id'], ['163ab68d9fd1da1544bbdd606b47c4f2','2f70c2d470187123edc70a64fb851570','ffed5fdabbd8574b0fc87a3720fc6d3f']);
        })->groupBy('parent_id');
        $rows['oil_type'] = $oil['oil_type']->mapWithKeys(function($item){
            return [$item['id'] => $item['dict_data']];
        });
        $rows['oil_name'] = $oil['oil_name']->mapWithKeys(function($item){
            return [$item['id'] => $item['dict_data']];
        });
        $rows['oil_level'] = $oil['oil_level']->mapWithKeys(function($item){
            return [$item['id'] => $item['dict_data']];
        });
        $rows['oil_name_relation_type'] = $oil['oil_name_relation_type']->map(function ($oilNameRelationType) {
            $oilNameType = $oilNameRelationType->mapWithKeys(function ($item) {
                return [$item['id'] => $item['dict_data']];
            });
            return $oilNameType;
        });
        return $rows;
    }

    public function getOperateReason(): array
    {
        return self::query()
                   ->select('dict_data')
                   ->where('dict_type', 'operate_order_reason')
                   ->orderBy('orderby')
                   ->pluck('dict_data')
                   ->toArray();
    }
}