<?php


namespace App\Models;


class AdditionalOrderRecord extends BaseModel
{
    protected $connection = 'mysql_gas';

    public $table = 'gas_additional_order_record';

    protected $fillable = [
        'id', 'station_id', 'orgcode', 'gun_id', 'oil_type', 'oil_name', 'oil_level', 'record_type', 'oil_sail_price',
        'oil_num', 'oil_money', 'oil_time', 'third_card_no', 'driver_name', 'driver_phone', 'truck_no', 'image',
        'record_mirror', 'approve_status', 'remark', 'create_time', 'creator', 'update_time', 'updator', 'mac_money',
        'supplier_price', 'supplier_money', 'third_party_order_no',
    ];

    public $timestamps = false;

    CONST RECORD_TYPE_MONEY = 1; //按金额
    CONST RECORD_TYPE_OIL = 2; //按升

    CONST APPROVE_STATUS_WAIT = 1; //待审核
    CONST APPROVE_STATUS_ALLOW = 2; //审核通过
    CONST APPROVE_STATUS_FORBID = 3; //审核拒绝

    public static $APPROVE_STATUS = [
        self::APPROVE_STATUS_WAIT => '待审核',
        self::APPROVE_STATUS_ALLOW => '审核通过',
        self::APPROVE_STATUS_FORBID => '审核不通过',
    ];

    /**
     * 创建一条补单记录
     *
     * @param $insertArray
     * @return mixed
     */
    public function insertOneRecord($insertArray)
    {
        return self::insert($insertArray);
    }

    /**
     * 更新一条补单记录
     *
     * @param $id
     * @param $updateArray
     * @return mixed
     */
    public function updateOneRecordById($id, $updateArray)
    {
        return self::where(['id' => $id])->update($updateArray);
    }

    /**
     * 查询一条补单记录
     *
     * @param array $params
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneRecordByParams(array $params, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $params)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}