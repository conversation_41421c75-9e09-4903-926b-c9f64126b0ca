<?php


namespace App\Http\Controllers;


use App\Http\Defines\CommonError;
use App\Services\QrCodeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Throwable;


class QrCodeController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/19 5:08 下午
     */
    public function parse(Request $request): JsonResponse
    {
        $rule = [
            'qr_code' => 'required',
        ];
        $messages = [
            'qr_code.required' => CommonError::QR_CODE_INVALID,
        ];
        $this->validatorThrowRuntimeException($request->all(), $rule, $messages);
        return response()->json(QrCodeService::parse(array_merge($request->all(), [
            'uid' => $request->header('X-G7-Api-Uid', ''),
        ])));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/2/3 5:54 下午
     */
    public function parseForOa(Request $request): JsonResponse
    {
        $rule = [
            'qr_code'    => 'required',
            'station_id' => 'required',
        ];
        $messages = [
            'qr_code.required'    => CommonError::QR_CODE_INVALID,
            'station_id.required' => CommonError::STATION_ID_MISSING,
        ];
        $this->validatorThrowRuntimeException($request->all(), $rule, $messages);
        return response()->json(QrCodeService::parseForOa($request->all()));
    }

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function parseQrCodeForElectricity(Request $request): JsonResponse
    {
        $params = $request->all();
        $rule = [
            'qr_code' => 'required|string',
        ];
        $messages = [
            'qr_code.required' => "二维码不能为空",
        ];
        $this->validator($params, $rule, $messages);
        return response()->json([
                'code' => 0,
                "data" => (new QrCodeService())->parseForElectricity($params['qr_code'])
            ]
        );
    }
}