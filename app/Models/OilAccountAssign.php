<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OilAccountAssign extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_account_assign';

    protected $guarded = ["id"];

    protected $fillable = ['id','no','org_id',"money_total","service_money","actual_money","status"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}