<?php


namespace App\Http\Middleware;


use App\Library\Helper\KafkaLog;
use Closure;
use Ramsey\Uuid\Uuid;
use function MongoDB\BSON\toJSON;

class WriteLog
{
    public function __construct()
    {

    }

    /**
     * @param $request
     * @param Closure $next
     * @return mixed
     * @throws \Exception
     */
    function handle($request, Closure $next) {
        $response = $next($request);
        $headerInfo = $request->get('header_info');
        $kafkaLogParam = [
            'path' => $request->path(),
            'cost' => intval(1000 * (microtime(true) - START)),
            'param' => $request->all(),
            'way' => env('APPLICATION__NAME') ?? 'foss-order',
            'call' => 2,
            'source' => strtolower($headerInfo['source']) ?? '',
            //nginx产生的request_id
            'request_id' => $headerInfo['request_id'] ?? '',
            //trace_id全局唯一,上游服务透传的日志id
            'trace_id' => $headerInfo['trace_id'] ?? Uuid::uuid1()->getHex(),
            //上游服务透传的授权的用户id
            'uid' => $headerInfo['uid'] ?? '',
            //上游服务透传的用户昵称
            'user_name' => $headerInfo['user_name'] ?? '',
            //上游服务透传的访客ip
            'request_real_ip' => $headerInfo['request_real_ip'] ?? '',
            'third_way' => env('APP_NAME'),
        ];
        KafkaLog::operationLog($kafkaLogParam, collect($request->all())->toJSON() ,$response->getContent());
        return $response;
    }
}