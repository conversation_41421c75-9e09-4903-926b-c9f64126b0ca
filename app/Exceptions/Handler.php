<?php

namespace App\Exceptions;

use App\Http\Defines\CommonError;
use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Library\Monitor\Report;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Auth\Access\AuthorizationException::class,
        \Symfony\Component\HttpKernel\Exception\HttpException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Illuminate\Session\TokenMismatchException::class,
        \Illuminate\Validation\ValidationException::class,
        ParamInvalidException::class,
        RuntimeException::class
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Exception $exception)
    {
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->logException($exception);
        }
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param Exception $e
     * @return Response|JsonResponse
     */
    public function render($request, Exception $e)
    {
        $extra = [
            '服务:' => env('APP_NAME'),
            '环境:' => App()->environment()
        ];
        if ($this->shouldReport($e)) {
            //Report::sendExceptionToDingding($e, $extra, env('DINGDING_TOKEN'));
            Report::sendExceptionToFeishu($e, $extra, env('FEISHU_TOKEN'));
        }

        if ($e instanceof NotFoundHttpException) {
            return response()->json([
                'code' => CommonError::SYSTEM_NOFOUND,
                'msg'  => CommonError::getMsgByCode(CommonError::SYSTEM_NOFOUND),
                'data' => [],
            ]);
        }

        if ($e instanceof MethodNotAllowedHttpException) {
            return response()->json([
                'code' => CommonError::SYSTEM_ERROR,
                'msg'  => CommonError::getMsgByCode(CommonError::SYSTEM_ERROR),
                'data' => [],
            ]);
        }

        if ($e instanceof RuntimeException) {
            $code = $e->getCode() != 0 ? strval($e->getCode()) : CommonError::SYSTEM_ERROR;
            $msg = $e->getMessage();

            $defMsg = CommonError::getMsgByCode($code);
            $errorMsg = $defMsg ?: $msg;
            if (stripos($errorMsg, "油站信息有更新") !== false) {
                $code = CommonError::STATION_OVERDUE;
                $errorMsg = CommonError::getMsgByCode($code);
            }
            if (stripos($errorMsg, "服务器异常") !== false || stripos($errorMsg, "服务内部错误") !== false ||
                stripos($errorMsg,"抱歉") !== false || stripos($errorMsg,"报告老板") !== false
            ) {
                $code = CommonError::SYSTEM_ERROR;
                $errorMsg = '服务内部错误，请稍后再试';
            }
            if (empty($e->getMessage())) {
                $extra['错误描述:'] = $errorMsg;
            }
            Report::sendExceptionToFeishu($e, $extra, env('FEISHU_TOKEN'));

            return response()->json([
                'code' => $code,
                'msg'  => CommonError::formatMsg($errorMsg),
                'data' => [],
            ]);
        }

        if ($e instanceof ValidationException || $e instanceof ParamInvalidException) {
            $msg = $e->getMessage();
            $defMsg = CommonError::getMsgByCode(CommonError::SYSTEM_NO_PARAMS);
            $errorMsg = $msg ?: $defMsg;
            return response()->json([
                'code' => CommonError::SYSTEM_NO_PARAMS,
                'msg'  => CommonError::formatMsg($errorMsg),
                'data' => [],
            ]);
        }

        if (App()->environment() == 'prod' || App()->environment() == 'pre') {
            return response()->json([
                'code' => CommonError::SYSTEM_ERROR,
                'msg'  => CommonError::getMsgByCode(CommonError::SYSTEM_ERROR),
                'data' => [],
            ]);
        } else {
            return parent::render($request, $e);
        }
    }
}
