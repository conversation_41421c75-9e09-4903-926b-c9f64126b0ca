<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

/**
 * Class CardModel
 * @package App\Models
 * @property $card_level
 * @property $left_seconds
 */
class CardModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_card';

    protected $guarded = ["id"];

    public $incrementing = false;

    protected $fillable = ['id','card_no',"password","pcode","orgcode","gascode","pwd_errornum","ischeck","driverphone","pwd_errornum"];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function org()
    {
        return $this->belongsTo(OrgModel::class, 'gascode', 'orgcode');
    }

    /**
     * 批量获取G7能源账户信息
     *
     * @param $params
     * @param array $select
     * @param $toArray
     * @return array
     */
    public function getCardInfoByParamsBatch($params, $select = ['*'], $toArray)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $params)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 获取G7能源账户信息
     *
     * @param array $selectParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneCardByParams(array $selectParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $selectParams, true)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }
}