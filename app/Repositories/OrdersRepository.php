<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\OrderModel;
use App\Models\OrdersModel;
use Exception;
use Illuminate\Support\Facades\Redis;
use InvalidArgumentException;
use Ramsey\Uuid\Uuid;

class OrdersRepository extends EloquentRepository
{
    protected $orderModel;

    public function __construct(OrdersModel $orderModel)
    {
        $this->model = $orderModel;
        $this->orderModel = $orderModel;
    }

    //订单入库
    public function addData(array $data)
    {
        $this->rules['STORE'] = $this->model->getFillAble();
        return $this->storeModel($data,$this->orderModel);
    }

    //更新订单
    public function updateOrder(array $data,$condition)
    {
        return $this->orderModel->find($condition)->update($data);
    }

    //锁查询
    public function getInfoByLock(array $condition)
    {
        //$this->debugstart('mysql_gas');
        $data = $this->orderModel->select("id", "pcode")->withCondition($condition)->lockForUpdate()->first();
        //$sql = $this->debugend('mysql_gas');
        //print_r($sql);
        return $data;
    }

    /**
     * 生成下单令牌
     * @param string $tokenData token对应的缓存数据
     * @param int $cacheTime token缓存周期
     * @return int
     * @throws InvalidArgumentException|Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 6:22 下午
     */
    public static function generateOrderToken(string $tokenData, int $cacheTime = 3600): int
    {
        $token = crc32(Uuid::uuid4());
        Redis::connection()->setex("order_token_" . $token, $cacheTime, $tokenData);
        return $token;
    }

    public function getListByFilter($params, $fields=['*'], $master=false)
    {
        if (empty($params))
            return [];

        if ($master) {
            return OrderModel::onWriteConnection()
                ->withCondition($params)
                ->select($fields)->get()->toArray();
        } else {
            return OrderModel::withCondition($params)
                ->select($fields)->get()->toArray();
        }
    }
}