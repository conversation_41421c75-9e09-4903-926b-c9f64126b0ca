<?php


namespace App\Http\Controllers\History;


use App\Http\Controllers\Controller;
use App\Services\HistoryAdditionalService;

class HistoryAdditionalController extends Controller
{
    protected $historyAdditionalService;

    public function __construct
    (
        HistoryAdditionalService $historyAdditionalService
    )
    {
        $this->historyAdditionalService = $historyAdditionalService;
    }

    /**
     * 创建｜编辑补单信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function addOrUpdate()
    {
        $field = [
            'id'                   => '',
            'station_id'           => '',
            'orgcode'              => '',
            'gun_id'               => '',
            'oil_type'             => '',
            'oil_name'             => '',
            'oil_level'            => '',
            'oil_unit'             => '',
            'oil_num'              => null,
            'oil_time'             => '',
            'third_card_no'        => '',
            'driver_name'          => '',
            'driver_phone'         => '',
            'truck_no'             => '',
            'ticket_image'         => '', // 图片返回相对路径,去掉url强校验
            'truck_image'          => '',
            'other_image'          => '',
            'remark'               => '',
            'mac_price'            => '',
            'mac_money'            => '',
            'platform_price'       => '',
            'platform_money'       => '',
            'supplier_price'       => '',
            'supplier_money'       => '',
            'third_party_order_no' => '',
        ];

        $rule = [
            'id'             => '',
            'station_id'     => 'required|string',
            'orgcode'        => 'required|string',
            'gun_id'         => 'string',
            'oil_type'       => 'string',
            'oil_name'       => 'required|string',
            'oil_level'      => 'string',
            'oil_unit'       => 'required|integer|in:1,2',
            'platform_price' => 'required|money',
            'supplier_price' => 'required|money',
            'mac_price'      => 'required|money',
            'oil_num'        => 'required|numeric',
            'platform_money' => 'required|money',
            'supplier_money' => 'required|money',
            'mac_money'      => 'required|money',
            'oil_time'       => 'required|date_format:Y-m-d H:i:s',
            'third_card_no'  => 'string',
            'driver_name'    => 'string|max:40',
            'driver_phone'   => 'required|phone_no',
            'truck_no'       => 'required|string',
            'ticket_image'   => 'string', // 图片返回相对路径,去掉url强校验
            'truck_image'    => 'string',
            'other_image'    => 'string',
            'remark'         => 'required|string',
        ];

        $message = [
            'station_id.required'     => '站点ID必传',
            'orgcode.required'        => '机构编码必传',
            'oil_name.required'       => '油品名称必传',
            'oil_unit.required'       => '加油方式必传 1按金额 2按升',
            'platform_price.required' => '油品销价必传',
            'supplier_price.required' => '油品进价必传',
            'mac_price.required'      => '油品油机价必传',
            'oil_num.required'        => '油品升数必传',
            'platform_money.required' => '加油金额必传',
            'supplier_money.required' => '应付金额必传',
            'mac_money.required'      => '油机金额必传',
            'oil_time.required'       => '加油时间必传, 格式:年-月-日 时:分:秒',
            'driver_name.required'    => '司机姓名必传',
            'driver_phone.required'   => '司机手机号必传',
            'driver_phone.phone_no'   => '手机号格式错误,请核对',
            'truck_no.required'       => '车牌号必传',
            'truck_no.truck_no'       => '车牌号格式错误,请核对',
            'remark.required'         => '申请原因不能为空',
        ];
        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyAdditionalService->addOrUpdate($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 审核补单申请
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve()
    {
        $field = [
            'id'             => null,
            'approve_status' => 0,
        ];

        $rule = [
            'id'             => 'required|integer',
            'approve_status' => 'required|in:1,2,3',
        ];

        $message = [
            'id.required'    => '补单信息ID必传',
            'approve_status' => '审核结果必传:1待审核 2审核成功',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyAdditionalService->approve($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 更新补单详情
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateApproveDetail()
    {
        $field = [
            'order_id'           => '',
            'order_status'       => null,
            'order_msg'          => '',
            'third_order_id'     => '',
            'third_order_status' => null,
            'third_order_msg'    => '',
        ];

        $rule = [
            'order_id'           => 'required|string',
            'order_status'       => 'required|integer|in:1,2,3',
            'order_msg'          => 'string|max:255',
            'third_order_id'     => 'string',
            'third_order_status' => 'integer|in:1,2,3',
            'third_order_msg'    => 'string|max:255',
        ];

        $message = [
            'order_id.required'     => 'G7订单号必传',
            'order_status.required' => 'G7订单状态必传:1待支付 2已支付 3支付失败',
            'third_order_status.in' => '三方订单状态: 1待支付 2已支付 3支付失败',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->historyAdditionalService->updateApproveDetail($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 分页获取补录列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function additionalPaginate()
    {
        $field = [
            'orgcode'            => '',
            'station_id'         => '',
            'station_code'       => '',
            'pcode'              => '',
            'approve_status'     => 0,
            'truck_no'           => '',
            'driver_phone'       => '',
            'ge_oil_time'        => '',
            'le_oil_time'        => '',
            'ge_update_time'     => '',
            'le_update_time'     => '',
            'order_id'           => '',
            'order_status'       => '',
            'third_order_id'     => '',
            'third_order_status' => '',
            'page'               => 1,
            'limit'              => 10
        ];

        $params = $this->getParamAll($field);

        try {
            $result = $this->historyAdditionalService->additionalPaginate($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 获取补单详情
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function additionalDetail()
    {
        $field = [
            'id' => 0
        ];

        $params = $this->getParamAll($field);

        try {
            $result = $this->historyAdditionalService->additionalDetail($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 补单机构配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function additionalOrgFuzzySearch()
    {
        $field = [
            'org_name' => '',
            'page'     => 1,
            'limit'    => 10,
        ];

        $params = $this->getParamAll($field);

        try {
            $result = $this->historyAdditionalService->additionalOrgFuzzySearch($params);

            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }
}