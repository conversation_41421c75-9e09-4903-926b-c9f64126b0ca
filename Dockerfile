FROM g7cr-registry.cn-hangzhou.cr.aliyuncs.com/g7-base/energy-php7.2:0.0.3
WORKDIR /data/web
RUN  rm -rf /etc/nginx/sites-available/default*
ADD ./conf/php/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d
ADD ./conf/nginx/conf/vhosts /etc/nginx/sites-available
ADD ./ /data/web
RUN mkdir -p /data/web/storage/framework/cache && \
    mkdir -p /data/web/storage/framework/sessions && \
    mkdir -p /data/web/storage/framework/views && \
    chown -R nginx:nginx /data/web/storage
RUN composer install -vvv
RUN mkdir -p /var/log/supervisor