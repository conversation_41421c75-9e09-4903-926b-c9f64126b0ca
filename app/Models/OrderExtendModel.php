<?php


namespace App\Models;


class OrderExtendModel extends BaseModel
{
    const UPDATED_AT='update_time';

    public $incrementing = false;
    protected $connection = 'mysql_gas';
    protected $table = 'gas_order_new_extend';
    protected $fillable = ['order_id', 'driver_signature', 'create_time', 'update_time',
        'upstream_settle_price', 'upstream_settle_money', 'cal_cost_price', 'cal_cost_money', 'plummet_rebate_money', 'later_rebate_money','charge_rebate_money'];

    /**
     * 关联电子券使用记录表
     */
    public function coupons()
    {
        return $this->hasOne(CouponsModel::class, 'voucher', 'g7_coupon_flag');
    }
    /**
     * 插入或更新
     *
     * @param $orderId
     * @param $updateOrInsertArray
     * @return mixed
     */
    public function upOrInsert($orderId, $updateOrInsertArray)
    {
        return self::updateOrInsert(['order_id' => $orderId], $updateOrInsertArray);
    }

    /**
     * 获取一条扩展记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneExtendByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = self::select($select)->where($whereParams)->first();
        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    public function getPluckExtendByParams(array  $whereParams,$pluck){
        $sqlObj = BaseModel::scopeWithCondition(self::select(),$whereParams,$pluck);

        $result = $sqlObj->pluck($pluck);

        return !$result ? [] : $result->toArray();
    }
}