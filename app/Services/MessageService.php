<?php


namespace App\Services;


use App\Http\Defines\CommonDefine;
use App\Servitization\Adapter;
use App\Servitization\FeiEYun;
use App\Servitization\FossUser;
use App\Servitization\Gas;
use App\Servitization\Notify;
use App\Servitization\TruckBaby;
use Exception;
use Illuminate\Support\Facades\Log;

class MessageService
{
    protected $fossUser;
    protected $gas;
    protected $notify;
    protected $feieyun;
    protected $adapter;
    protected $truckBaby;

    public function __construct
    (
        FossUser $fossUser,
        Gas $gas,
        Notify $notify,
        FeiEYun $feieyun,
        Adapter $adapter,
        TruckBaby $baby
    )
    {
        $this->fossUser = $fossUser;
        $this->gas = $gas;
        $this->notify = $notify;
        $this->feieyun = $feieyun;
        $this->adapter = $adapter;
        $this->truckBaby = $baby;
    }

    /**
     * 下发验密的长链接消息
     *
     * @param $cardNo
     * @param $orderId
     * @return array|mixed
     */
    public function driverCheckPassword($cardNo, $orderId)
    {
        return $this->fossUser->sendMsg([
            'card_no'  => $cardNo,
            'order_id' => $orderId,
            'msg_type' => 1, // 消息类型 1 G7能源账户密码(卡密码)
            'source'   => 1 // 消息来源 1新版PDA
        ]);
    }

    /**
     * 支付成功 消息通知
     * @param $cardNo
     * @param $orderId
     * @param string $mobile
     * @return array|mixed
     */
    public function driverPaySuccess($cardNo, $orderId, $mobile = '')
    {
        $pushData = [
            'card_no'  => $cardNo,
            'order_id' => $orderId,
            'msg_type' => 2, // 消息类型 2支付成功
            'source'   => 1, // 消息来源 1新版PDA
        ];
        if (!empty($mobile)) {
            $pushData['mobile'] = $mobile;
        }

        return $this->fossUser->sendMsg($pushData);
    }

    /**
     * 支付失败
     *
     * @param $cardNo
     * @param $orderId
     * @param string $msg
     * @param int $code
     * @return array|mixed
     */
    public function driverPayFail($cardNo, $orderId, $msg = '', $code = 0, $phone = '')
    {
        $pushData = [
            'card_no'  => $cardNo,
            'order_id' => $orderId,
            'msg_type' => 3, // 消息类型 3支付失败
            'source'   => 1,// 消息来源 1新版PDA
            'msg'      => $msg,
            'code'     => $code
        ];
        if (!empty($phone)) {
            $pushData['mobile'] = $phone;
        }
        return $this->fossUser->sendMsg($pushData);
    }

    /**
     * 推送消息给PDA-H5加油员
     *
     * @param $token
     * @param $msg
     * @throws \Exception
     */
    public function pushMsgToDoper($token, $msg)
    {
        if (empty($token)) {
            return false;
        }
        $this->notify->pushMessage([
            'token' => (string)$token,
            'group' => 'pdah5',
            'msg'   => is_array($msg) ? json_encode($msg, JSON_UNESCAPED_UNICODE) : $msg,
        ]);
    }

    /**
     * 司机微信卡包支付的消息推送
     *
     * @param $cardNo
     * @param $orderId
     * @return mixed|void
     */
    public function cardDriverCheckPassword($cardNo, $orderId)
    {
        return $this->gas->pushMsg([
            'user_id'  => $cardNo,
            'msg_type' => 'card_need_pass',
            'content'  => 'pda-h5' . ',' . (string)$orderId
        ]);
    }

    /**
     * 推送adapter
     *
     */
    public function pushData2OA($params, $message_type = "PAY_LOG")
    {
        $oaCreateOrder['data'] = $params;
        $oaCreateOrder['message_type'] = $message_type;
        return $this->adapter->sendCreateOrderMsg($oaCreateOrder);
    }

    /**
     * 推送卡车订单
     */
    public function pushOrder2KC($pushData = [])
    {
        return $this->truckBaby->pushOrder($pushData);
    }

    /**
     * 小票机站点，推送打印消息
     * http://www.feieyun.com/open/index.html
     */
    public function pushFeiEYunPrint($info = [], $sn = "",$style = 'default')
    {
        if (empty($sn)) {
            return true;
        }
        //样式见：接口列表->小票机打印订单  http://help.feieyun.com/document.php
        //指令有效期默认一小时
        $print_expired = 3600;
        switch ($style) {
            case 'style-1':
                $print_expired = 300;//5分钟
                $orderInfo = '<CB>车队加油凭证</CB><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '站点：  ' . $info['station_name'] . '<BR>';
                $orderInfo .= '品号：  ' . $info['oil_name'] . '<BR>';
                $orderInfo .= '升数：  <B>' . $info['oil_num'] . '</B><BR>';
                $orderInfo .= '时间：  ' . date('Y-m-d H:i:s', strtotime($info['createtime']) - 10) . '<BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '司机签字:';
                $orderInfo .= '<BR><BR><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '<BOLD>请按照小票显示升数为司机加油</BOLD><BR><BR>';
                $orderInfo .= 'LS: '.$info['serial_num'].'<BR>ZH: '.$info['card_no'].'<BR>';
                break;
            case 'style-2':
                $orderInfo = '<CB>车队加油凭证</CB><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '流水：  ' . $info['serial_num'] . '<BR>';
                if ($info['orgname_show']) {
                    $orderInfo .= '机构：  ' . $info['orgname'] . '<BR>';
                }
                $orderInfo .= '站点：  ' . $info['station_name'] . '<BR>';
                $orderInfo .= '账号：  ' . $info['card_no'] . '<BR>';
                $orderInfo .= '商品：  ' . $info['oil_name'] . '<BR>';
                $orderInfo .= '数量：  ' . $info['oil_num'] . '<BR>';
                if ($info['price_show']) {
                    $orderInfo .= '单价：  ' . $info['price'] . '<BR>';
                }
                $orderInfo .= '金额：  <B>' . $info['mac_amount'] . '</B><BR>';
                $orderInfo .= '时间：  ' . date('Y-m-d H:i:s', strtotime($info['createtime']) - 10) . '<BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '司机签字:';
                $orderInfo .= '<BR><BR><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '<C>本小票仅用于加油站核对司机身份</C>';
                break;
            default:
                $orderInfo = '<CB>车队加油凭证</CB><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '流水：  ' . $info['serial_num'] . '<BR>';
                if ($info['orgname_show']) {
                    $orderInfo .= '机构：  ' . $info['orgname'] . '<BR>';
                }
                $orderInfo .= '站点：  ' . $info['station_name'] . '<BR>';
                $orderInfo .= '账号：  ' . $info['card_no'] . '<BR>';
                $orderInfo .= '车牌：  ' . $info['truck_no'] . '<BR>';
                $orderInfo .= '商品：  ' . $info['oil_name'] . '<BR>';
                $orderInfo .= '数量：  ' . $info['oil_num'] . '<BR>';
                if ($info['price_show']) {
                    $orderInfo .= '单价：  ' . $info['price'] . '<BR>';
                }
                $orderInfo .= '金额：  <B>' . $info['pay_money'] . '</B><BR>';
                $orderInfo .= '时间：  ' . date('Y-m-d H:i:s', strtotime($info['createtime']) - 10) . '<BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '司机签字:';
                $orderInfo .= '<BR><BR><BR>';
                $orderInfo .= '------------------------------<BR>';
                $orderInfo .= '<C>本小票仅用于加油站核对司机身份</C>';
                break;
        }
//        $orderInfo .= '<BR><BR><RIGHT>打印时间:' . date('Y-m-d H:i:s') . '</RIGHT>';


        $printUser = '<EMAIL>';
        $printKey = 'GwJKf3DmG3PfeWyz';
        $params = [
            'user'    => $printUser,
            'stime'   => time(),
            'apiname' => 'Open_printMsg',
            'debug'   => 0 //debug=1返回非json格式的数据。仅测试时候使用
        ];

        $params['sig'] = sha1($printUser . $printKey . $params['stime']);
        //5分钟有效，超过5分钟不打印
        $params['expired'] = time() + $print_expired ;

        $params['sn'] = $sn;
        $params['times'] = 1;
        $params['content'] = $orderInfo;
        $res = $this->feieyun->sendPost($params);
        $res['printInfo'] = $orderInfo;
        return $res;

    }


    /**
     * 推送h5长连接消息
     * @param $params
     * @param $msgType
     * @return bool
     */
    public function pushMsg2H5($params, $msgType)
    {
        $topic = $this->getTopicAndCheckParams($msgType, $params);
        if (empty($params['mobile'])) {
            throw new \RuntimeException("服务异常，请稍后再试", 4005001);
        }

        $uniqueCert = !empty($params['unique_cert']) ? $params['unique_cert'] : '';
        if (empty($uniqueCert)) {
            $user = (new FossUser())->getUserInfoByPhone(['mobile'=>$params['mobile']]);
            if(!isset($user['unique_cert']) || empty($user['unique_cert'])){
                throw new \RuntimeException("服务异常，请稍后再试",4005001);
            }
            $uniqueCert = $user['unique_cert'];
        }

        return $this->notify->pushMessage([
            'token' => (string)$uniqueCert,
            'group' => 'driver_h5',
            'msg'   => json_encode([
                'topic' => $topic,
                'data'  => $this->getPushData($params, $topic),
            ], JSON_UNESCAPED_UNICODE),
        ]);
    }

    /**
     * 获取各topic下的推送内容
     * @param $params
     * @param $topic
     * @return array
     */
    private function getPushData($params, $topic)
    {
        unset($params['unique_cert']);
        $ret = $params;
        $ret['source'] = 1;
//
//        if ($topic == 'pay_error') {
//            $ret['code'] = !empty($params['code']) ? $params['code'] : '';
//            $ret['msg'] = !empty($params['msg']) ? $params['msg'] : '';
//        }

        return $ret;
    }

    /**
     * 获取topic并验证参数
     * @param $msgType
     * @param $params
     * @return string
     */
    public function getTopicAndCheckParams($msgType, $params)
    {
        $topic = CommonDefine::getTopic($msgType);
        if (in_array($topic, [1, 2, 3])) {
            if (empty($params['id'])) {
                throw new \RuntimeException("订单号不能空", 2001);
            }
            if (empty($params['mobile']) && empty($params['card_no'])) {
                throw new \RuntimeException("缺少手机号或卡号", 2001);
            }
        }

        return $topic;
    }

    /**
     * 推送司机退款申请单审核结果给卡车
     * @param array $pushData
     * @return mixed
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 11:33 下午
     */
    public function pushDriverRefundApprovalStatusToKc($pushData = [])
    {
        return $this->truckBaby->pushDriverRefundApprovalStatusToKc($pushData);
    }

    /**
     * 生产kafka消息
     * @param string $data
     * @param string $topic
     * @return void
     * @throws Exception
     */
    public static function createKafkaMessage(string $data = "", string $topic = 'oil-order-verification-result')
    {
        /*//使用foss-api的kafka发送消息
        $uuid = Common::uuid();
        $requestData = [
            //日志主键id，索引唯一
            //不设置的话es会自动生成, 但是此时数据唯一性不保证
            "id"      => $uuid,
            //es索引名，对应数据表名【建议和kafka topic对应上】
            "index"   => $topicChannel,
            //操作类型"insert|update|delete|upsert
            "op"      => "insert",
            //null字段是否修改，不传默认false
            "options" => ["writeMapNullValue" => true],
            // 消息的产生时间
            "time"    => date('Y-m-d H:i:s'),
            //业务方的实际需要存储的数据,和es索引字段一一对应,字段不能有额外的，否则会插入失败
            "data"    => $data,
            //kafka的topic名称，通常与索引名一一对应
            "topic"   => $topicChannel,
            //卡夫卡的key值，随意设置
            "key"     => "order-callback",
        ];
        Kafka::send($requestData);
        return $data;*/
        $conf = new \RdKafka\Conf();
        $conf->set('log_level', (string)LOG_DEBUG);
        $conf->set('debug', 'all');
        $conf->set('api.version.request', 'false');
        $conf->set('broker.version.fallback', '0.9.0.1');
        $conf->set("message.timeout.ms", 5000);
        $conf->setDrmSgCb(function ($kafka, $result) {

            Log::info("Kafka message sending result", [
                'result' => $result,
            ]);
        });
        $conf->setErrorCb(function ($kafka, $err, $reason) {

            Log::info("Kafka message sending error", [
                'err'    => $err,
                'reason' => $reason
            ]);
        });
//        if (in_array(App::environment(), ['prod', 'pro', 'pre'])) {
//            $conf->set('security.protocol', 'sasl_plaintext');
//            $conf->set('sasl.kerberos.principal', '<EMAIL>');
//            $conf->set('sasl.kerberos.keytab', resource_path('keytab/<EMAIL>'));
//            $conf->set('sasl.kerberos.kinit.cmd', 'kinit -r 5d -k -t "%{sasl.kerberos.keytab}" %{sasl.kerberos.principal}');
//            $conf->set('sasl.kerberos.min.time.before.relogin', 3600000);
//        }
        $rk = new \RdKafka\Producer($conf);
        $rk->addBrokers(env("OPEN_API_EVENT_PUSH_KAFKA_BROKERS"));
        @$rk->setLogLevel(LOG_DEBUG); //兼容rdKafka 3.x/5.x
        $topic = $rk->newTopic($topic);
        $topic->produce(RD_KAFKA_PARTITION_UA, 0, $data);
        if (method_exists($rk, 'flush')) {

            $result = $rk->flush(-1);
            if (RD_KAFKA_RESP_ERR_NO_ERROR !== $result) {

                throw new Exception('Was unable to flush, messages might be lost!');
            }
            return;
        }
        $len = $rk->getOutQLen();
        while ($len > 0) {

            $len = $rk->getOutQLen();
            $rk->poll(1);
        }
    }
}