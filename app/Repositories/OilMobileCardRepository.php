<?php

namespace App\Repositories;

use App\Models\OilMobileCard;

class OilMobileCardRepository extends \App\Repositories\EloquentRepository
{
    /**
     * 创建司机关系
     * @param $params
     * @param $degree
     * @return object
     */
    public function addCard($params)
    {
        $data = OilMobileCard::addData($params);
        return $data;
    }

    public function getInfo($params)
    {
        return OilMobileCard::getByViceNo($params);
    }
}
