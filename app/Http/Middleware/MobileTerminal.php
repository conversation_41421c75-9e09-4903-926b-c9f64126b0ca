<?php

namespace App\Http\Middleware;

use App\Library\Helper\Common;
use App\Servitization\FossUser;
use Closure;
use Ramsey\Uuid\Uuid;

class MobileTerminal
{
    protected $fossUser;

    public function __construct
    (
        FossUser $fossUser
    )
    {
        $this->fossUser = $fossUser;
    }

    /**
     * @param $request
     * @param Closure $next
     * @return \Illuminate\Http\JsonResponse|mixed
     * @throws \Exception
     */
    public function handle($request, Closure $next)
    {
        Common::log('info','记录header信息_手机管站', ['param' => $request->header()]);
        //用户信息
        $uid = $request->header('X-G7-Api-Uid');
        $requestSource = $request->header('appkey');
        $appKeyInfo = $this->fossUser->getAppKeyConfig();
        $source = $appKeyInfo['source'][$requestSource] ?? '';
        if (empty($uid)) {
            $data = ['code' => config('errorcode.STATUSCODE_NOTACCESS'), 'msg' => 'FOSS-USER授权失败，无权限访问', 'success' => false, 'data' => []];
            return response()->json($data);
        }

        $headers = [
            'X-G7-Api-Uid' => $uid,
            'appkey' => $requestSource
        ];
        $user = $this->fossUser->getAdminBaseInfoByUid(['uid' => $uid,'status' => 1, 'source' => 1], $headers);
        if (!isset($user['role_id'])) {
            $data = ['code' => config('errorcode.STATUSCODE_NOTACCESS'), 'msg' => 'FOSS-USER授权失败，无权限访问', 'success' => false, 'data' => []];
            return response()->json($data);
        }
        $userName = isset($user['nick_name']) && !empty($user['nick_name']) ? $user['nick_name'] : '系统';

        /**
         * 下单渠道枚举
         */
        $sourceNameEnum = [];
        foreach ($appKeyInfo['role_source'] as $key => $value) {
            if (array_key_exists($key, $appKeyInfo['source_name'])) {
                $sourceNameEnum[$value] = $appKeyInfo['source_name'][$key];
            }
        }
        $request->offsetSet('order_channel', $appKeyInfo['role_source'][$source] ?? '');
        $request->offsetSet('order_channel_enum', $sourceNameEnum);

        /**
         * 对接kafka日志
         */
        $requestId = !empty($request->header('X-Request-Id')) ? $request->header('X-Request-Id') : Common::uuid();
        $requestIp = !empty($request->header('X-Original-Forwarded-For')) ? $request->header('X-Original-Forwarded-For') : $_SERVER ['REMOTE_ADDR'];
        $headerInfo = [
            'source' => $source,
            'request_id' => $requestId,
            'trace_id' => Uuid::uuid1()->getHex(),
            'uid' => $uid,
            'user_name' => $userName,
            'request_real_ip' => $requestIp,
        ];
        $request->offsetSet('header_info', $headerInfo);
        $request->offsetSet('uid', $uid);
        $request->offsetSet('user_name', $userName);

        return $next($request);
    }
}
