<?php


namespace App\Servitization;


use App\Library\Helper\Common;

class FossApi
{
    public $config;

    public function __construct()
    {
        $this->config = config('fossApi');
    }

    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    protected function exec($type = 'POST', $args, callable $callback = NULL)
    {
        $params = $args;
        //将要求int类型的入参转换
        if (isset($params['data']) && $params['data']) {
            $params['data'] = Foss::convertFiledType($params['data']);
            if ($type == 'get') {
                $params['data'] = Foss::clearBlankParams($params['data']);
            }
        }
        try {
            // 拼接url
            $url = $this->config['host'].array_get($params, 'method', '');
            $data = Common::requestJson($url, $params['data']);
            $data = json_decode($data, true);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 推送websocket消息
     *
     * @param array $params
     * @return mixed
     */
    public function pushMessage(array $params)
    {
        /*
         * {
         *     "group" : "pdah5",
         *     "msg"   : "",
         *     "token" : "用户token"
         * }
         */
        $result = $this->exec('POST', [
            'method' => '/api/notify/msgSend',
            'data' => $params
        ]);

        return $result;
    }
}