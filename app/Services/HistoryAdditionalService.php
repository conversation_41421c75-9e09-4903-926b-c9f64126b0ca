<?php


namespace App\Services;


use App\Library\Helper\NumberUtil;
use App\Library\Request;
use App\Models\AdditionalOrderItem;
use App\Models\AdditionalOrderRecord;
use App\Repositories\CityRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryAdditionalRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationRepository;
use App\Servitization\Adapter;
use App\Servitization\Foss;
use App\Servitization\FossStation;
use Illuminate\Support\Facades\DB;

class HistoryAdditionalService
{
    protected $numberUtil;
    protected $errorCodeService;
    protected $fossStation;
    protected $adapter;
    protected $orgRepository;
    protected $historyAdditionalRepository;
    protected $cityRepository;
    protected $dictRepository;
    protected $stationRepository;

    public function __construct
    (
        NumberUtil                  $numberUtil,
        ErrorCodeService            $errorCodeService,
        FossStation                 $fossStation,
        Adapter                     $adapter,
        OrgRepository               $orgRepository,
        HistoryAdditionalRepository $historyAdditionalRepository,
        CityRepository              $cityRepository,
        DictRepository              $dictRepository,
        StationRepository           $stationRepository
    )
    {
        $this->numberUtil = $numberUtil;
        $this->errorCodeService = $errorCodeService;
        $this->fossStation = $fossStation;
        $this->adapter = $adapter;
        $this->orgRepository = $orgRepository;
        $this->historyAdditionalRepository = $historyAdditionalRepository;
        $this->cityRepository = $cityRepository;
        $this->dictRepository = $dictRepository;
        $this->stationRepository = $stationRepository;
    }

    /**
     * 创建｜编辑补单信息
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function addOrUpdate($params)
    {
        list($id, $station_id, $orgcode, $gun_id, $oil_type, $oil_name, $oil_level, $oil_unit,
            $oil_num, $oil_time, $third_card_no, $driver_name, $driver_phone, $truck_no, $ticket_image,
            $truck_image, $other_image, $remark, $mac_price, $mac_money, $platform_price, $platform_money,
            $supplier_price, $supplier_money,) = array_values($params);

        $operator = Request::get('user_name');
        // 保存镜像信息的变量
        $recordMirror = [];
        // 加油时间校验
        if (strtotime($oil_time) > time()) {
            throw new \Exception('加油时间不得晚于当前时间,请核对', $this->errorCodeService->handleAdditionalError(40312));
        }
        // 金额、升数精度调整
        $oil_sale_price = (float)$platform_price;
        /*
         * 金额不校验--王新
        $oil_num = (float)$oil_num;
        $oil_money = (float)$oil_money;
        // 校验金额(只保留小数点后两位,末尾舍去)
        $realMoney = $this->numberUtil->formatNumber($oil_sale_price*$oil_num, 2);
        if (strcmp($oil_money, $realMoney)) {
            throw new \Exception('加油金额和加油数量不匹配,请核对', $this->errorCodeService->handleAdditionalError(40301));
        }*/
        if (bccomp($oil_num, 0.00, 2) < 1) {
            throw new \Exception('下单失败，单次交易升数小于0.01升', '40335');
        }

        /**
         * 机构
         */
        // 白名单
        $configOrg = array_column($this->additionalOrgFuzzySearch([
            'limit' => 10000,
        ]), 'orgcode');
        if (!in_array($orgcode, $configOrg)) {
            throw new \Exception('机构不在补录白名单,请核对', $this->errorCodeService->handleAdditionalError(40310));
        }
        $orgInfo = collect($this->orgRepository->getOilOrgById(['orgcode' => $orgcode]))->toArray();
        if (empty($orgInfo)) {
            throw new \Exception('机构不存在,请核对', $this->errorCodeService->handleAdditionalError(40302));
        }
        $recordMirror['org_name'] = $orgInfo['org_name'];

        /**
         * 油站、油枪、油品
         */
        $stationInfo = $this->fossStation->getStation($station_id);
        if (empty($stationInfo)) {
            throw new \Exception('油站[id:' . $station_id . ']不存在,请核对', $this->errorCodeService->handleAdditionalError(40303));
        }
        // 省、市名
        $provinceCityDict = $this->cityRepository->getProvinceCityDict(false)->keyBy('city_code')->toArray();
        $recordMirror['station_code'] = $stationInfo['station_code'];
        $recordMirror['station_name'] = $stationInfo['station_name'];
        $recordMirror['supplier_name'] = $stationInfo['supplier_name'];
        $recordMirror['province_code'] = $stationInfo['provice_code'];
        $recordMirror['city_code'] = $stationInfo['city_code'];
        $recordMirror['province_name'] = $provinceCityDict[$stationInfo['provice_code']]['city_name'];
        $recordMirror['city_name'] = $provinceCityDict[$stationInfo['city_code']]['city_name'];

        // 校验油站
        $oilInfo = array_get($stationInfo, 'oil', []);
        if (empty($oilInfo)) {
            throw new \Exception('所选油站无油品,请核对', $this->errorCodeService->handleAdditionalError(40304));
        }

        // 校验油枪、油品
        $checkGunAndGoods = false;
        foreach ($oilInfo as $item) {
            $gunInfo = array_get($item, 'gun', []);
            if (!empty($gunInfo) && !empty($gun_id)) {
                foreach ($gunInfo as $value) {
                    if (!strcmp($value['gun_id'], $gun_id)) {
                        $recordMirror['gun_name'] = $value['gun_name'];
                        $recordMirror['tank_name'] = $value['tank_name'];
                        if (!strcmp($item['oil_type'], $oil_type) && !strcmp($item['oil_name'], $oil_name) && !strcmp($item['oil_level'], $oil_level)) {

                            $checkGunAndGoods = true;
                            break 2;
                        }
                    }
                }
            }
        }

        if (!empty($gun_id) && ($checkGunAndGoods == false)) {
            throw new \Exception('所选站点的油枪不包含指定的油品,请核对', $this->errorCodeService->handleAdditionalError(40305));
        }
        // 商品名称
        $oilDict = $this->dictRepository->getOilDict();
        $recordMirror['oil_type_name'] = array_get($oilDict, 'oil_type.' . $oil_type, '');
        $recordMirror['oil_name_name'] = array_get($oilDict, 'oil_name.' . $oil_name, '');
        $recordMirror['oil_level_name'] = array_get($oilDict, 'oil_level.' . $oil_level, '');
        $recordMirror['goods'] =
            array_get($oilDict, 'oil_type.' . $oil_type, '')
            . array_get($oilDict, 'oil_name.' . $oil_name, '')
            . array_get($oilDict, 'oil_level.' . $oil_level, '');

        /**
         * 销价
         */
        $selectPriceArray = [
            'spcode'     => $stationInfo['pcode'],
            'station_id' => $station_id,
            'oil_name'   => $oil_name,
            'oil_type'   => $oil_type,
            'oil_level'  => $oil_level,
            'time'       => $oil_time,
            'orgcode'    => $orgcode,
        ];

        $salePrice = $this->fossStation->getSalePrice($selectPriceArray);
        if (empty($salePrice)) {
            throw new \Exception('补单选择的油站、油品不存在销价,请核对', $this->errorCodeService->handleAdditionalError(40308));
        }
//
//        if (abs($oil_sale_price - $salePrice['platform_price'])) {
//            throw new \Exception('油品单价:' . $oil_sale_price . '和G7销价:' . $salePrice['platform_price'] . '不一致,请核对', $this->errorCodeService->handleAdditionalError(40309));
//        }
        $recordMirror['mac_price'] = (float)$mac_price;
        $recordMirror['mac_money'] = (float)$mac_money;
        $recordMirror['supplier_price'] = (float)$supplier_price;
        $recordMirror['supplier_money'] = (float)$supplier_money;
        $recordMirror['oil_unit'] = $oil_unit;
        $imgHost = config('oss')['host'];
        $commonArray = [
            'station_id'     => $station_id,
            'orgcode'        => $orgcode,
            'gun_id'         => $gun_id,
            'oil_type'       => $oil_type,
            'oil_name'       => $oil_name,
            'oil_level'      => $oil_level,
            'record_type'    => $oil_unit,
            'oil_sale_price' => $oil_sale_price,
            'oil_num'        => $oil_num,
            'oil_money'      => $platform_money,
            'oil_time'       => $oil_time,
            'third_card_no'  => $third_card_no,
            'driver_name'    => $driver_name,
            'driver_phone'   => $driver_phone,
            'truck_no'       => $truck_no,
            'image'          => json_encode([
                'ticket_image' => empty($ticket_image) ? '' : (strstr($ticket_image, $imgHost) ? $ticket_image : $imgHost . $ticket_image),
                'truck_image'  => empty($truck_image) ? '' : (strstr($truck_image, $imgHost) ? $truck_image : $imgHost . $truck_image),
                'other_image'  => empty($other_image) ? '' : (strstr($other_image, $imgHost) ? $other_image : $imgHost . $other_image),
            ], JSON_UNESCAPED_UNICODE),
            'record_mirror'  => json_encode($recordMirror, JSON_UNESCAPED_UNICODE),
            'approve_status' => AdditionalOrderRecord::RECORD_TYPE_MONEY,
            'remark'         => $remark,
            'updator'        => $operator,
            'third_party_order_no' => $params['third_party_order_no'] ?? '',
        ];

        // 更新
        if ($id) {
            // 已审核的不允许编辑
            $idResult = $this->historyAdditionalRepository->getOneRecordByParams(['id' => $id]);
            if (empty($idResult)) {
                throw new \Exception('无效的审核ID,请核对', $this->errorCodeService->handleAdditionalError(40310));
            }
            if ($idResult['approve_status'] == AdditionalOrderRecord::APPROVE_STATUS_ALLOW) {
                throw new \Exception('审核通过的补录信息不允许编辑', $this->errorCodeService->handleAdditionalError(40311));
            }
            $this->historyAdditionalRepository->updatedOneAdditionRecordById($id, $commonArray);
        } else {
            // 创建
            $commonArray['creator'] = $operator;
            $this->historyAdditionalRepository->insertOneAdditionRecord($commonArray);
        }

        return true;
    }

    /**
     * 审核补单申请
     *
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function approve($params)
    {
        $id = array_get($params, 'id', 0);
        $checkStatus = array_get($params, 'approve_status', 0);
        $operator = Request::get('user_name');

        $recordInfo = $this->historyAdditionalRepository->getOneRecordByParams(['id' => $id], ['*'], true);
        if (empty($recordInfo)) {
            throw new \Exception('当前审核的补单记录不存在,请核实', $this->errorCodeService->handleAdditionalError(40306));
        }
        if ($recordInfo['approve_status'] == AdditionalOrderRecord::APPROVE_STATUS_ALLOW) {
            throw new \Exception('审核已通过,无法再次审核', $this->errorCodeService->handleAdditionalError(40308));
        }
        if (!strcmp($checkStatus, $recordInfo['approve_status'])) {
            return true;
        }
        if (!array_key_exists($checkStatus, AdditionalOrderRecord::$APPROVE_STATUS)) {
            throw new \Exception('无效的审核状态,请核实', $this->errorCodeService->handleAdditionalError(40307));
        }

        try {
            DB::connection('mysql_gas')->beginTransaction();
            // 更新审核状态
            $updateArray = [
                'approve_status' => $checkStatus,
                'updator'        => $operator,
            ];
            $result = $this->historyAdditionalRepository->updatedOneAdditionRecordById($id, $updateArray);
            // 审核通过,推送消息给OA
            if ($checkStatus == AdditionalOrderRecord::APPROVE_STATUS_ALLOW) {
                $image = json_decode($recordInfo['image'], true);
                $mirror = json_decode($recordInfo['record_mirror'], true);
                $sendParams = [
                    'station_id'           => $recordInfo['station_id'],
                    'province_code'        => $mirror['province_code'],
                    'city_code'            => $mirror['city_code'],
                    'orgcode'              => $recordInfo['orgcode'],
                    'gun_id'               => $recordInfo['gun_id'],
                    'oil_type'             => $recordInfo['oil_type'],
                    'oil_name'             => $recordInfo['oil_name'],
                    'oil_level'            => $recordInfo['oil_level'],
                    'oil_sale_price'       => $recordInfo['oil_sale_price'],
                    'oil_num'              => $recordInfo['oil_num'],
                    'oil_money'            => $recordInfo['oil_money'],
                    'oil_time'             => $recordInfo['oil_time'],
                    'third_card_no'        => $recordInfo['third_card_no'],
                    'driver_name'          => $recordInfo['driver_name'],
                    'driver_phone'         => $recordInfo['driver_phone'],
                    'truck_no'             => $recordInfo['truck_no'],
                    'ticket_image'         => $image['ticket_image'] ?? '',
                    'truck_image'          => $image['truck_image'] ?? '',
                    'other_image'          => $image['other_image'] ?? '',
                    'remark'               => $recordInfo['remark'],
                    'mac_price'            => $mirror['mac_price'] ?? '',
                    'mac_money'            => $mirror['mac_money'] ?? '',
                    'supplier_price'       => $mirror['supplier_price'] ?? '',
                    'supplier_money'       => $mirror['supplier_money'] ?? '',
                    'third_party_order_no' => $recordInfo['third_party_order_no'],
                    'oil_unit'             => $mirror['oil_unit'] ?? 0,
                ];

                $oaInfo = $this->adapter->sendAdditionalToOa($sendParams);
                $insertArray = [
                    'record_id'          => $recordInfo['id'],
                    'order_id'           => $oaInfo['order_id'],
                    'order_status'       => AdditionalOrderItem::ORDER_STATUS_WAIT,
                    'third_order_id'     => $oaInfo['third_order_id'] ?? '',
                    'third_order_status' => AdditionalOrderItem::ORDER_STATUS_WAIT,
                ];

                $result = $this->historyAdditionalRepository->insertOneAdditionalItem($insertArray);
            }
            DB::connection('mysql_gas')->commit();
        } catch (\Exception $e) {
            Db::connection('mysql_gas')->rollBack();

            throw $e;
        }

        return true;
    }

    /**
     * 更新补单详情
     *
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function updateApproveDetail($params)
    {
        list($order_id, $order_status, $order_msg, $third_order_id, $third_order_status, $third_order_msg) = array_values($params);
        // 根据G7订单号,查询交易记录
        $itemResult = $this->historyAdditionalRepository->getOneAdditionItemByParams(['order_id' => $order_id]);
        if (empty($itemResult)) {
            throw new \Exception('无效的G7订单号,请核对', $this->errorCodeService->handleAdditionalItemError(40301));
        }
        if (!array_key_exists($order_status, AdditionalOrderItem::$ORDER_STATUS)) {
            throw new \Exception('无效的G7订单状态,请核实', $this->errorCodeService->handleAdditionalItemError(40302));
        }
        if (!empty($third_order_status) && !array_key_exists($third_order_status, AdditionalOrderItem::$ORDER_STATUS)) {
            throw new \Exception('无效的三方订单状态,请核实', $this->errorCodeService->handleAdditionalItemError(40303));
        }
        if ($itemResult['order_status'] == AdditionalOrderItem::ORDER_STATUS_SUCCESS) {
            throw new \Exception('G7订单已扣款,无法修改订单状态', $this->errorCodeService->handleAdditionalItemError(40304));
        }
        if ($itemResult['third_order_status'] == AdditionalOrderItem::ORDER_STATUS_SUCCESS) {
            throw new \Exception('三方订单已扣款,无法修改订单状态', $this->errorCodeService->handleAdditionalItemError(40305));
        }
        $updateArray = [];
        if (strcmp($itemResult['order_status'], AdditionalOrderItem::ORDER_STATUS_SUCCESS)) {
            $updateArray['order_status'] = $order_status;
            $updateArray['order_msg'] = $order_msg;
        }

        if (strcmp($itemResult['third_order_status'], $third_order_status)) {
            $updateArray['third_order_id'] = $third_order_id;
            $updateArray['third_order_status'] = $third_order_status;
            $updateArray['third_order_msg'] = $third_order_msg;
        }

        if (empty($updateArray)) {
            return true;
        }

        $result = $this->historyAdditionalRepository->updateOneAdditionalItemByOrderId($order_id, $updateArray);

        return true;
    }

    /**
     * 分页获取补录列表
     *
     * @param $params
     * @return array
     */
    public function additionalPaginate($params)
    {
        list($orgCode, $stationId, $stationCode, $pcode, $approveStatus, $truckNo, $driverPhone, $geOilTime,
            $leOilTime, $geUpdateTime, $leUpdateTime, $orderId, $orderStatus, $thirdOrderId, $thirdOrderStatus,
            $page, $limit) = array_values($params);

        /**
         * 站点过滤
         */
        $stationIdArray = [];
        if (!empty($stationId)) {
            $stationIdArray = explode(',', $stationId);
        }
        if (!empty($stationCode)) {
            $stationInfo = [array_get($this->stationRepository->getOneStationBasicInfoByParams(['station_code' => $stationCode]), 'id', '')];
            if (empty($stationInfo)) {
                return ['count' => 0, 'data' => [], 'page' => $page, 'limit' => $limit];
            }

            $stationIdArray = empty($stationIdArray) ? $stationInfo : array_intersect($stationIdArray, $stationInfo);
            if (empty($stationIdArray)) {
                return ['count' => 0, 'data' => [], 'page' => $page, 'limit' => $limit];
            }
        }

        if (!empty($pcode)) {
            $stationInfo = collect($this->stationRepository->getBatchStationInfoByParams(['pcode' => $pcode]))->pluck('id')->toArray();
            if (empty($stationInfo)) {
                return ['count' => 0, 'data' => [], 'page' => $page, 'limit' => $limit];
            }

            $stationIdArray = empty($stationIdArray) ? $stationInfo : array_intersect($stationIdArray, $stationInfo);
            if (empty($stationIdArray)) {
                return ['count' => 0, 'data' => [], 'page' => $page, 'limit' => $limit];
            }
        }

        $whereParams = [];
        if (!empty($orgCode)) {
            $whereParams['record.orgcode'] = explode(',', $orgCode);
        }
        if (!empty($stationIdArray)) {
            $whereParams['record.station_id'] = $stationIdArray;
        }
        if (!empty($approveStatus)) {
            $whereParams['record.approve_status'] = $approveStatus;
        }
        if (!empty($truckNo)) {
            $whereParams['record.truck_no'] = $truckNo;
        }
        if (!empty($driverPhone)) {
            $whereParams['record.driver_phone'] = $driverPhone;
        }
        if (!empty($geOilTime)) {
            $whereParams['ge_record.oil_time'] = date('Y-m-d H:i:s', strtotime($geOilTime));
        }
        if (!empty($leOilTime)) {
            $whereParams['le_record.oil_time'] = date('Y-m-d H:i:s', strtotime($leOilTime));
        }
        if (!empty($geUpdateTime)) {
            $whereParams['ge_record.update_time'] = date('Y-m-d H:i:s', strtotime($geUpdateTime));
        }
        if (!empty($leUpdateTime)) {
            $whereParams['le_record.update_time'] = date('Y-m-d H:i:s', strtotime($leUpdateTime));
        }
        if (!empty($orderId)) {
            $whereParams['item.order_id'] = $orderId;
        }
        if (!empty($orderStatus)) {
            $whereParams['item.order_status'] = explode(',', $orderStatus);;
        }
        if (!empty($thirdOrderId)) {
            $whereParams['item.third_order_id'] = $thirdOrderId;
        }
        if (!empty($thirdOrderStatus)) {
            $whereParams['item.third_order_status'] = explode(',', $thirdOrderStatus);
        }
        // 查询结果集
        $result = $this->historyAdditionalRepository->additionalPaginate($whereParams, $page, $limit);
        // 格式处理
        $result['data'] = collect($result['data'])->map(function ($item, $key) {
            $item['approve_type'] = '消费——补录';
            $item['approve_status_name'] = AdditionalOrderRecord::$APPROVE_STATUS[$item['approve_status']];
            $item['order_id'] = $item['order_id'] ?? '';
            $item['order_status'] = $item['order_status'] ?? 0;
            $item['order_msg'] = $item['order_msg'] ?? '';
            $item['third_order_id'] = $item['third_order_id'] ?? '';
            $item['third_order_status'] = $item['third_order_status'] ?? 0;
            $item['third_order_msg'] = $item['third_order_msg'] ?? '';
            $item['order_status_name'] = AdditionalOrderItem::$ORDER_STATUS[$item['order_status']] ?? '';
            $item['third_order_status_name'] = AdditionalOrderItem::$ORDER_STATUS[$item['third_order_status']] ?? '';
            $image = empty($item['image']) ? [] : (is_array(json_decode($item['image'], true)) ? json_decode($item['image'], true) : []);
            $recordMirror = json_decode($item['record_mirror'], true);
            unset($item['image']);
            unset($item['record_mirror']);
            return array_merge($item, $image, $recordMirror);
        })->toArray();

        return $result;
    }

    /**
     * 获取补单详情
     *
     * @param $params
     * @return array
     */
    public function additionalDetail($params)
    {
        $id = array_get($params, 'id', 0);
        $additionalRecord = $this->historyAdditionalRepository->getOneRecordByParams(['id' => $id]);
        if (empty($additionalRecord)) {
            return [];
        }
        $image = empty($additionalRecord['image']) ? [] : json_decode($additionalRecord['image'], true);
        unset($additionalRecord['image']);
        $recordMirror = json_decode($additionalRecord['record_mirror'], true);
        unset($additionalRecord['record_mirror']);
        $additionalRecord['approve_status_name'] = AdditionalOrderRecord::$APPROVE_STATUS[$additionalRecord['approve_status']];

        $additionalItem = $this->historyAdditionalRepository->getOneAdditionItemByParams(
            ['record_id' => $additionalRecord['id']],
            ['order_id', 'order_status', 'order_msg', 'third_order_id', 'third_order_status', 'third_order_msg']
        );
        $itemFormat = [
            'order_id'                => '',
            'order_status'            => 0,
            'order_status_name'       => '',
            'order_msg'               => '',
            'third_order_id'          => '',
            'third_order_status'      => 0,
            'third_order_status_name' => '',
            'third_order_msg'         => '',
        ];
        if (!empty($additionalItem)) {
            $additionalItem['order_status_name'] = AdditionalOrderItem::$ORDER_STATUS[$additionalItem['order_status']] ?? '';
            $additionalItem['third_order_status_name'] = AdditionalOrderItem::$ORDER_STATUS[$additionalItem['third_order_status']] ?? '';
        }

        return array_merge($additionalRecord, $image, $recordMirror, $itemFormat, $additionalItem);
    }

    /**
     * 补单机构配置
     *
     * @param $params
     * @return array
     */
    public function additionalOrgFuzzySearch($params)
    {
        $org_name = array_get($params, 'org_name', '');
        $page = array_get($params, 'page', 1);
        $limit = array_get($params, 'limit', 10);
        $orgFuzzySearch = (new Foss())->getAdditionalOrg() ?? [];
        if (!empty($org_name)) {

            foreach ($orgFuzzySearch as $orgCode => $item) {

                if (mb_strstr($item['org_name'], $org_name)) {

                    continue;
                }
                unset($orgFuzzySearch[$orgCode]);
            }
        }
        return array_slice($orgFuzzySearch, ($page - 1) * $limit, $limit);
    }
}