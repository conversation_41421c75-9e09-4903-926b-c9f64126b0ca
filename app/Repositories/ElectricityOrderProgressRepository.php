<?php

namespace App\Repositories;

use App\Models\ElectricityOrderProgressModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ElectricityOrderProgressRepository
{
    protected $electricityOrderProgressModel;

    public const OFFLINE    = 10;
    public const STARTING   = 20;
    public const PROCESSING = 30;
    public const STOPING    = 40;
    public const FINISHED   = 50;
    public const BAD        = 60;

    public static $statusMap = [
        self::OFFLINE    => '离线',
        self::STARTING   => '启动中',
        self::PROCESSING => '充电中',
        self::STOPING    => '停止中',
        self::FINISHED   => '结束',
        self::BAD        => '异常',
    ];

    public function __construct
    (
        ElectricityOrderProgressModel $electricityOrderProgressModel
    ) {
        $this->electricityOrderProgressModel = $electricityOrderProgressModel;
    }

    /**
     * @param int $orderId
     * @param array $select
     * @param bool $lock
     * @return ElectricityOrderProgressModel|Builder|Model|\Illuminate\Database\Query\Builder|object|null
     */
    public function getElectricityOrderProgressByOrderId(int $orderId, array $select = ['*'], bool $lock = false)
    {
        return $this->electricityOrderProgressModel::getElectricityOrderProgressByOrderId($orderId, $select, $lock);
    }

    public function insertElectricityOrderProgress(array $params): bool
    {
        return $this->electricityOrderProgressModel::insertElectricityOrderProgress($params);
    }

    public function insertOrUpdateElectricityOrderProgressByOrderId(int $orderId, array $params): bool
    {
        return $this->electricityOrderProgressModel::insertOrUpdateElectricityOrderDetail($orderId, $params);
    }
}
