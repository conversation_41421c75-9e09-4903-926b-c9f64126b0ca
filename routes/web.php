<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', 'HelloController@index');

/**
 * order服务对web服务提供的接口
 *
 * 前后端分离,关闭CSRF校验
 */
Route::group(['prefix' => 'history/v1', 'namespace' => 'History', 'middleware' => ['ajax']], function () {
    /**
     * 交易流水
     */
    // 获取交易流水
    Route::post('/getHistoryPaginate', 'HistoryController@getHistoryPaginate')->name('admin.trade.getHistoryPaginate');
    //对外
    Route::post('/outer/getHistoryPaginate', 'HistoryController@getHistoryPaginate')->name('admin.trade.getHistoryPaginate.outer');
    // 查看小票
    Route::post('/getTicket', 'HistoryController@getTicket');
    /**
     * 交易流水补录、退款
     */
    // G7能源账户补录审核列表
    Route::post('/approve/getApprovePaginate', 'HistoryApproveController@getApprovePaginate')->name('admin.approve.getApprovePaginate');
    // G7能源账户补录审核列表导出
    Route::get('/approve/exportApprovePaginate', 'HistoryApproveController@exportApprovePaginate')->name('admin.approve.exportApprovePaginate');
    // G7能源账户补录审核单详情
    Route::post('/approve/getApproveDetail', 'HistoryApproveController@getApproveDetail')->name('admin.approve.getApproveDetail');
    // 创建｜编辑G7能源账户补录审核单
    Route::post('/approve/additionalAddOrUpdate', 'HistoryApproveController@additionalAddOrUpdate')->name('admin.approve.additionalAddOrUpdate');
    // 创建G7能源账户退款审核单
    Route::post('/approve/refundAdd', 'HistoryApproveController@refundAdd')->name('admin.approve.refundAdd');
    // G7能源账户审核单审核通过
    Route::post('/approve/approvePass', 'HistoryApproveController@approvePass')->name('admin.approve.approvePass');
    // G7能源账户审核单审核驳回
    Route::post('/approve/approveRefuse', 'HistoryApproveController@approveRefuse')->name('admin.approve.approveRefuse');
    /**
     * 则一、狮桥等机构补录
     */
    // 创建或更新补单信息
    Route::post('/additional/addOrUpdate', 'HistoryAdditionalController@addOrUpdate')->name('admin.additional.addOrUpdate');
    // 补单审核通过
    Route::post('/additional/approve', 'HistoryAdditionalController@approve')->name('admin.additional.approvePass');
    // 获取补录列表
    Route::post('/additional/additionalPaginate', 'HistoryAdditionalController@additionalPaginate')->name('admin.additional.getAdditionalPaginate');
    // 获取补录详情
    Route::post('/additional/additionalDetail', 'HistoryAdditionalController@additionalDetail');
    // 补录机构白名单
    Route::post('/additional/additionalOrgFuzzySearch', 'HistoryAdditionalController@additionalOrgFuzzySearch');
});


/**
 * 手机管站
 */
Route::group(['prefix' => 'mt',  'middleware' => ['mt']], function (){
    Route::group(['prefix' => 'history/v1', 'namespace' => 'History',], function () {
        // G7能源账户退款审核列表
        Route::post('getMtApprovePaginate', 'HistoryApproveController@getMtApprovePaginate');
        // 退款审核通过
        Route::post('approvePass', 'HistoryApproveController@approvePass');
        // 退款审核驳回
        Route::post('approveRefund', 'HistoryApproveController@approveRefuse');
        // 创建退款申请
        Route::post('createRefundApprove', 'HistoryApproveController@refundAdd');
        // 手机管站获取交易流水
        Route::post('getMtHistoryPaginate', 'HistoryController@getMtHistoryPaginate');

        Route::post('getRefundReason', 'HistoryController@getOperateHistoryReason');
        // 退款申请是否已存在
        Route::post('checkIfRefundRecordExists', '\App\Http\Controllers\Order\OrderApproveController@checkIfRefundRecordExists');
    });
    Route::group(['prefix' => 'order/v1', 'namespace' => 'Order',], function () {
        // 根据订单号查询分配记录
        Route::post('getAssignListByOrderId', 'OrderController@getAssignListByOrderId');
    });
});

/**
 * 订单
 */
Route::group(['prefix' => 'newOrder/v1', 'namespace' => 'Order', 'middleware' => ['ajax']], function () {
    // 分页获取订单列表
    Route::post('/getOrderPaginate', 'OrderController@getOrderPaginate')->name('admin.order.getOrderPaginate');
    // 获取订单详情
    Route::post('/getOrderItem', 'OrderController@getOrderItem')->name('admin.order.getOrderItem');
    // 订单超时
    Route::post('/orderTimeOut', 'OrderController@orderTimeOut')->name('admin.order.orderTimeOut');
    // 补录
    Route::post('/approve/additionalAddOrUpdate', 'OrderApproveController@additionalAddOrUpdate')->name('admin.order.additionalAddOrUpdate');
    // 退款
    Route::post('/approve/refundApprove', 'OrderApproveController@refundApprove')->name('admin.order.orderRefund');
    // 预检查待异常修改订单是否满足条件
    Route::post('/approve/exceptionModificationPreCheck', 'OrderApproveController@exceptionModificationPreCheck')->name('admin.order.exceptionModificationPreCheck');
    // 异常修改
    Route::post('/approve/exceptionModificationAddOrEdit', 'OrderApproveController@exceptionModificationAddOrEdit')->name('admin.order.exceptionModificationAddOrEdit');
    // 补录、退款审核通过
    Route::post('/approve/approvePass', 'OrderApproveController@approvePass')->name('admin.order.approvePass');
    // 查看小票
    Route::post('/getTicket', 'OrderController@getTicket')->name('admin.order.getTicket');
    //获取司机退款申请工单列表
    Route::post('/getDriverRefundApplicationRecordList', 'OrderController@getDriverRefundApplicationRecordList')->name('admin.order.getDriverRefundApplicationRecordList');
    //审核司机退款申请工单
    Route::post('/reviewDriverRefundApplicationRecord', 'OrderController@reviewDriverRefundApplicationRecord')->name('admin.order.reviewDriverRefundApplicationRecord');
    //核销码获取
    Route::post('/getPayMentQrcode', 'OrderController@getPayMentQrcode')->name('admin.order.getPayMentQrcode');
    //获取订单异常修改/补录/客户后台下单/退款申请原因列表
    Route::post('/getOperateOrderReason', 'OrderController@getOperateOrderReason');
    //补录三方单号
    Route::post('/approve/addApproveThirdOrder', 'OrderApproveController@addApproveThirdOrder')->name('admin.order.addApproveThirdOrder');

    Route::post('/approve/checkIsReceipt', 'OrderApproveController@checkOrderIsReceipt');
    //下发订单状态
    Route::post('/distributeOrderStatus', 'OrderController@distributeOrderStatus');
    //机构付款方式
    Route::post('/getOrgSettlementType', 'OrderApproveController@getOrgSettlementType');
    // 退款记录是否存在
    Route::post('/approve/checkIfRefundRecordExists', 'OrderApproveController@checkIfRefundRecordExists');
    //根据机构获取付款公司信息
    Route::post('/getCompanyListByOrg', 'OrderApproveController@getCompanyListByOrg');
    // 充电订单停止充电
    Route::post('/orderStopElectricity', 'OrderController@orderStopElectricityForUser');
    // 充电订单列表
    Route::post('/getElectricityOrderList', 'OrderController@getElectricityOrderList');
    // 充电订单详情
    Route::post('/getElectricityOrderInfo', 'OrderController@getElectricityOrderInfo');
});

/**
 * 导出接口
 */
Route::group(['prefix' => 'export/v1', 'middleware' => ['ajax']], function () {
    // G7能源账户交易流水导出
    Route::get('/exportHistory', 'ExportController@exportHistory')->name('admin.trade.export');
    // 订单导出
    Route::get('/exportOrder', 'ExportController@exportOrder')->name('admin.order.export');
    // G7能源账户交易流水导出-对外
    Route::get('/outer/exportHistory', 'ExportController@exportHistoryOuter')->name('admin.outer.trade.export');
});
/**
 * 二维码解析
 */
Route::group(['prefix' => 'qrCode/v1', 'middleware' => ["order"]], function () {
    // 二维码解析
    Route::post('/parse', 'QrCodeController@parse');
});