<?php


namespace App\Services;


use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Repositories\CardWhiteListRepository;
use App\Repositories\TruckCardRepository;
use App\Servitization\Gas;
use Illuminate\Support\Facades\Redis;

class TruckCardService
{
    protected $errorCodeService;
    protected $truckCardRepository;
    protected $cardWhiteListRepository;

    public function __construct
    (
        ErrorCodeService $errorCodeService,
        TruckCardRepository $truckCardRepository,
        CardWhiteListRepository $cardWhiteListRepository
    )
    {
        $this->errorCodeService = $errorCodeService;
        $this->truckCardRepository = $truckCardRepository;
        $this->cardWhiteListRepository = $cardWhiteListRepository;
    }

    /**
     * G7能源车辆账户(车辆卡)获取真实的手机号
     *
     * @param $cardNo
     * @param $orgCode
     * @param $limitType
     * @return array
     * @throws \Exception
     */
    public function checkCardAndGetInfo($cardNo, $orgCode, $limitType)
    {
        $whereParams = [
            'last_card_no' => $cardNo,
            'pcode' => '20003JCP',
            'last_bind_status' => 1,
        ];
        $result = $this->truckCardRepository->getOneByParams($whereParams);
        if (empty($result)) {
            throw new \RuntimeException('验证G7能源账户失败,无效的G7能源车辆账户!', $this->errorCodeService->createOrderError(40350));
        }
        if ((time() - strtotime($result['last_bind_datetime'])) > 600) {
            throw new \RuntimeException('验证G7能源账户失败,G7能源车辆账户已过期', $this->errorCodeService->createOrderError(40351));
        }

        $phoneNo = $result['phone'];
        // 限制机构
        if ($limitType == 1) {
            $whereParams = [
                'orgcode' => $orgCode,
                'driver_phone' => $phoneNo,
                'limit_type' => 1,
            ];
        } else {
            // 限制司机
            $whereParams = [
                'orgcode' => $cardNo,
                'driver_phone' => $phoneNo,
                'limit_type' => 2
            ];
        }
        $result = $this->cardWhiteListRepository->getOneWhiteListByParams($whereParams);
        if (empty($result)) {
            throw new \RuntimeException('验证G7能源账户失败,不在白名单内', $this->errorCodeService->createOrderError(40352));
        }

        return [
            'driver_phone' => $phoneNo,
            'driver_name' => $result['driver_name']
        ];
    }

    public function checkTruckCardTake($mobile, $viceNo)
    {
        $data = (new Gas())->checkTruckCardTake($mobile, $viceNo);
        return empty($data) ? false : true;
    }

    public function dispatchTruckCardReturnJob($mobile, $viceNo)
    {
        if (empty($mobile) || empty($viceNo)) {
            throw new \RuntimeException('手机号、车辆卡卡号不能为空', CommonError::USER_ERROR);
        }

        $queue = 'queue:foss:card';
        $msg = [
            'trace_id'  => Common::uuid(),
            'module'    => 'truck_card',
            'action'    => 'return',
            'data'      => [[
                'mobile'    => $mobile,
                'vice_no'   => $viceNo,
                'active'    => false
            ]]
        ];

        try {
            Redis::rpush($queue, json_encode($msg));

            Common::log('error', '车辆账户归还任务分发', $msg);
        } catch (\Exception $e) {
            Common::log('error', '车辆账户归还任务分发 失败', [$msg, strval($e)]);
        }
    }
}