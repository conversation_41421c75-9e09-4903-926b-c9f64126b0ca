<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2021/12/9
 * Time: 10:54 AM
 */

namespace Tests\Unit;


use App\Servitization\FeiShu;

class FeishuTest
{
    public function testSendRefundFailed()
    {
        $params = [
            'category' => 'test',
            'order_id' => '123',
            'station_name' => '站9090',
            'goods' => 'test 92#',
            'pay_money' => '89.9',
            'org_name' => '——————',
            'card_no' => '121212',
            'driver_phone' => '15010687937',
            'failed_reason' => '*****'
        ];

        (new FeiShu())->sendRefundFailed($params);
    }
}