<?php


namespace App\Services;


use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Models\OrderModel;
use App\Repositories\CardRepository;
use App\Repositories\OilerRepository;
use App\Repositories\OrdersRepository;
use App\Repositories\QrCodeParseRepository;
use App\Repositories\StationRepository;
use App\Servitization\Adapter;
use App\Servitization\FossStation;
use RuntimeException;
use Throwable;


class QrCodeService
{
    /**
     * 解析二维码
     * @param array $parameters 请求参数
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/20 3:19 下午
     */
    public static function parse(array $parameters): array
    {
        $oilerInfo = OilerRepository::getOilerInfo($parameters['uid'], [
            'rules'    => [
                'station_id' => 'required'
            ],
            'messages' => [
                'station_id.required' => CommonError::OILER_NO_BIND_STATION,
            ]
        ]);
        $stationInfo = StationRepository::getStationInfoById($oilerInfo['station_id'], [
            'rules'    => [
                'is_stop'       => 'required|in:0,1',
                'card_classify' => 'required|in:1,2,3',
            ],
            'messages' => [
                'is_stop.required'       => CommonError::SYSTEM_ERROR,
                'is_stop.in'             => CommonError::SYSTEM_ERROR,
                'card_classify.required' => CommonError::SYSTEM_ERROR,
                'card_classify.in'       => CommonError::SYSTEM_ERROR,
            ]
        ]);
        StationRepository::checkStationStatus($stationInfo['is_stop'], $stationInfo['card_classify']);
        $qrCode = explode('_', $parameters['qr_code']);
        $isAccessPlatformDriver = false;
        $qrCodeParseResultCheckData = [
            'rules'    => [
                'card_no' => 'required|numeric',
            ],
            'messages' => [
                'card_no.required' => CommonError::QR_CODE_INVALID,
                'card_no.numeric'  => CommonError::QR_CODE_INVALID,
            ],
        ];

        //根据二维码段数区分解析服务
        switch (count($qrCode)) {
            case 0:
            case 1:
                $qrCode = explode('payment/', $parameters['qr_code']);
                if (count($qrCode) > 1) {
                    $isAccessPlatformDriver = true;
                    $parseResult = QrCodeParseRepository::oa(
                        $parameters['qr_code'],
                        $oilerInfo['station_id'],
                        $qrCodeParseResultCheckData
                    );
                    break;
                }
                throw new RuntimeException("", CommonError::QR_CODE_INVALID);
            case 2:

                if ($qrCode[0] == 'HT' or $qrCode[0] == 'G7') {
                    $parseResult = QrCodeParseRepository::fossUser($parameters['qr_code']);
                    break;
                }
                $isAccessPlatformDriver = true;
                $parseResult = QrCodeParseRepository::oa(
                    $parameters['qr_code'],
                    $oilerInfo['station_id'],
                    $qrCodeParseResultCheckData
                );
                break;
            default:

                $isAccessPlatformDriver = true;
                $parseResult = QrCodeParseRepository::oa(
                    $parameters['qr_code'],
                    $oilerInfo['station_id'],
                    $qrCodeParseResultCheckData
                );
        }

        $cardInfo = CardRepository::getCardInfoByNo($parseResult['card_no'], [
            'rules'    => [
                'card_status'    => 'required',
                'orgcode'        => 'required',
                'account_status' => 'required',
            ],
            'messages' => [
                'orgcode.required'        => CommonError::SYSTEM_ERROR,
                'card_status.required'    => CommonError::SYSTEM_ERROR,
                'account_status.required' => CommonError::SYSTEM_ERROR,
            ],
        ]);
        CardRepository::checkCardUseStatus($cardInfo['card_status'], true);
        CardRepository::checkCardAccountStatus($cardInfo['account_status'], true);
        CardRepository::checkCardForStationAndGoodsConsumeLimit(
            $cardInfo['orgcode'],
            $oilerInfo['station_id'],
            "",
            true
        );

        if ($isAccessPlatformDriver) {
            $truckNo = $parseResult['truck_no'] ?? '';
            $driverPhone = $parseResult['driver_phone'] ?? '';
            $driverName = $parseResult['driver_name'] ?? '';
        } else {
            $truckNo = empty($parseResult['truck_no']) ? ($cardInfo['truck_no'] ?? '') : $parseResult['truck_no'];

            if ($cardInfo['card_level'] != OrderModel::CAR_CARD) {
                $driverPhone = $cardInfo['driver_tel'] ?? '';
                $driverName = $cardInfo['driver_name'] ?? '';
            } else {
                if ($cardInfo['truck_version'] == '2.0') {
                    $driverPhone = $cardInfo['driver_tel'] ?? '';
                    $driverName = $cardInfo['driver_name'] ?? '';
                } else {
                    $info = app(TruckCardService::class)->checkCardAndGetInfo(
                        $parseResult['card_no'],
                        $cardInfo['orgcode'],
                        $cardInfo['limit_type']
                    );
                    $driverPhone = $info['driver_phone'] ?? '';
                    $driverName = $info['driver_name'] ?? '';
                }
            }
        }
        $cacheParseResult = json_encode([
            'card_no'                   => $parseResult['card_no'],
            'is_access_platform_driver' => $isAccessPlatformDriver,
            'extends'                   => $parseResult['extends'] ?? '',
            'truck_no'                  => $truckNo,
            'driver_phone'              => $driverPhone,
            'driver_name'               => $driverName,
            'order_no'                  => $parseResult['order_no'] ?? '',
            'ocr_truck_no_id'           => $parseResult['ocr_truck_no_id'] ?? '',
            'qr_code_source'            => CardTradeConf::$qrCodeSourceFlagToEnumMapping[$parseResult['qr_code_source'] ?? ''] ?? 0,
        ]);
        Common::log('info', 'pda扫码解码数据' . $cacheParseResult);
        return [
            'code' => 0,
            'data' => [
                'token' => OrdersRepository::generateOrderToken($cacheParseResult),
            ],
            'msg'  => '成功',
        ];
    }

    /**
     * 为对接系统提供解析二维码接口
     * @param array $parameters 请求参数
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/20 3:19 下午
     */
    public static function parseForOa(array $parameters): array
    {
        $stationInfo = StationRepository::getStationInfoById($parameters['station_id'], [
            'rules'    => [
                'is_stop'       => 'required|in:0,1',
                'card_classify' => 'required|in:1,2,3',
            ],
            'messages' => [
                'is_stop.required'       => CommonError::SYSTEM_ERROR,
                'is_stop.in'             => CommonError::SYSTEM_ERROR,
                'card_classify.required' => CommonError::SYSTEM_ERROR,
                'card_classify.in'       => CommonError::SYSTEM_ERROR,
            ]
        ]);
        StationRepository::checkStationStatus($stationInfo['is_stop'], $stationInfo['card_classify']);
        $parseResult = QrCodeParseRepository::fossUser($parameters['qr_code']);
        $cardInfo = CardRepository::getCardInfoByNo($parseResult['card_no'], [
            'rules'    => [
                'card_status'    => 'required',
                'orgcode'        => 'required',
                'account_status' => 'required',
                'truck_no'       => 'string|nullable',
                'driver_name'    => 'string|nullable',
                'driver_tel'     => 'numeric|nullable',
                'card_balance'   => 'required',
                'ischeck'        => 'required',
            ],
            'messages' => [
                'orgcode.required'        => CommonError::SYSTEM_ERROR,
                'card_status.required'    => CommonError::SYSTEM_ERROR,
                'account_status.required' => CommonError::SYSTEM_ERROR,
                'truck_no.required'       => CommonError::SYSTEM_ERROR,
                'driver_name.required'    => CommonError::SYSTEM_ERROR,
                'driver_tel.required'     => CommonError::SYSTEM_ERROR,
                'card_balance.required'   => CommonError::SYSTEM_ERROR,
                'ischeck.required'        => CommonError::SYSTEM_ERROR,
            ],
        ]);
        CardRepository::checkCardUseStatus($cardInfo['card_status'], true);
        CardRepository::checkCardAccountStatus($cardInfo['account_status'], true);
        CardRepository::checkCardForStationAndGoodsConsumeLimit(
            $cardInfo['orgcode'],
            $stationInfo['id'],
            "",
            true
        );
        $goodsInfo = (new FossStation)->getOilListAndPrice([
            'station_id'        => $stationInfo['id'],
            'orgcode'           => $cardInfo['orgcode'],
            'force_query_price' => true,
        ]);
        $usableLiters = [];
        foreach ($goodsInfo as $v) {
            if ($v['can_use']) {
                $usableLiters[] = [
                    'oil_name'  => $v['oil_name'],
                    'oil_type'  => $v['oil_type'],
                    'oil_level' => $v['oil_level'],
                    'oil_num'   => bcdiv($cardInfo['card_balance'], $v['platform_price'], 2),
                    'price'     => $v['platform_price'],
                ];
            }
        }

        if ($cardInfo['card_level'] != OrderModel::CAR_CARD) {
            $driverPhone = $cardInfo['driver_tel'] ?? '';
            $driverName = $cardInfo['driver_name'] ?? '';
        } else {
            if ($cardInfo['truck_version'] == '2.0') {
                $driverPhone = $cardInfo['driver_tel'] ?? '';
                $driverName = $cardInfo['driver_name'] ?? '';
            } else {
                $info = app(TruckCardService::class)->checkCardAndGetInfo(
                    $cardInfo['card_no'],
                    $cardInfo['orgcode'],
                    $cardInfo['limit_type']
                );
                $driverPhone = $info['driver_phone'] ?? '';
                $driverName = $info['driver_name'] ?? '';
            }
        }

        return [
            'code' => 0,
            'data' => [
                'card_no'         => $cardInfo['card_no'],
                'org_code'        => $cardInfo['orgcode'],
                'driver_tel'      => $driverPhone,
                'driver_name'     => $driverName,
                'balance'         => $cardInfo['card_balance'],
                'truck_no'        => empty($parseResult['truck_no']) ? ($cardInfo['truck_no'] ?? '') : $parseResult['truck_no'],
                'oil_num'         => $usableLiters,
                'is_check_pwd'    => $cardInfo['ischeck'] == 1,
                'order_no'        => $parseResult['order_no'] ?? '',
                'qr_code_source'  => $parseResult['qr_code_source'] ?? '',
                'ocr_truck_no_id' => $parseResult['ocr_truck_no_id'] ?? '',
            ],
            'msg'  => '成功',
        ];
    }

    /**
     * @throws \Exception
     */
    public function parseForElectricity(string $qrCode): array
    {
        parse_str(parse_url($qrCode, PHP_URL_QUERY) ?? '', $parsedData);
        $connectorId = $parsedData['pNum'] ?? '';
        $platformIdentify = 'ykc';
        if (empty($connectorId)) {
            $connectorId = explode(".", explode("//", $qrCode)[1] ?? '')[0] ?? '';
            if (empty($connectorId)) {
                $connectorId = app(Adapter::class)->parseQrCodeForElectricity([
                    "qr_code"           => $qrCode,
                    "name_abbreviation" => 'ykc',
                ])['connector_id'];
            }
        }
        if (empty($connectorId)) {
            throw new RuntimeException('', CommonError::ELECTRICITY_DEVICE_EXCEPTION);
        }
        $connectorInfo = app(FossStation::class)->getConnectorByThirdIdAndSupplierCode([
            'connector_id'      => $connectorId,
            'supplier_code' => CommonDefine::getElectricitySupplierByIdentify($platformIdentify),
        ]);
        return [
            'connector_id' => $connectorInfo['id'],
            'station_id'   => $connectorInfo['station_id'],
            'supplier_code' => CommonDefine::getElectricitySupplierByIdentify($platformIdentify),
        ];
    }
}