<?php
/**
 * Functions description
 * Created by PhpStorm.
 * User: youki
 * Date: 2018/4/11
 * Time: 上午10:02
 */

namespace App\Http\Defines;


class CommonError
{
    //服务码（2位） + 业务码（2位） + 错误类（3位） + 子码（4位）
    const SUCCESS_CODE   = '0';
    const SYSTEM_ERROR   = '32015030001';
    const SYSTEM_NOFOUND = "32014040001";
    const NO_AUTH        = '32014010001';
    const GAS_ERROR      = '32014030001';
    const ADAPTER_ERROR  = '32014030003';
    const HTTP_REQUEST   = '32014030002';
    //参数缺少
    const SYSTEM_NO_PARAMS = '32024040001';
    const USER_ERROR       = '32024040002';
    //业务错误
    const STATION_OFF                                    = '32034040001';
    const NO_CARD                                        = '32034040002';
    const ERROR_CARD_PCODE                               = '32034040003';
    const CARD_PASSWORD                                  = '32034040004';
    const ERROR_CARD_PWD                                 = "32034040005";
    const OVER_CARD_PWD                                  = '32034040014';
    const NO_GUN                                         = '32034040006';
    const GUN_NUM                                        = '32034040011';
    const QRCODE_ERROR                                   = '32034040007';
    const ERROR_PRICE                                    = '32034040008';
    const BILL_NO_FOUND                                  = '32034040009';
    const ORDER_ERROR                                    = '32034040010';
    const GUN_FAIL                                       = '32034040012';
    const ERROR_MONEY                                    = '32034040013';
    const STATION_OVERDUE                                = '32034040015';
    const GUN_ERROR_PRICE                                = '32034040016';
    const NO_QRCODE                                      = '32034040017';
    const ADAPTER_NO_GUN                                 = "32034040018";
    const ERROR_OIL_NUM                                  = '32034040019';
    const ERROR_PCODE_PRICE                              = '32034040020';
    const WX_TO_USER_REQUIRED                            = '**********';
    const WX_TO_USER_INVALID                             = '**********';
    const WX_TEMPLATE_DATA_REQUIRED                      = '**********';
    const WX_TEMPLATE_DATA_INVALID                       = '**********';
    const WX_TEMPLATE_ID_REQUIRED                        = '**********';
    const WX_TEMPLATE_ID_INVALID                         = '**********';
    const QR_CODE_INVALID                                = "***********";
    const ABNORMAL_CARD_ACCOUNT                          = "***********";
    const ABNORMAL_CARD                                  = "***********";
    const CARD_STATION_CONSUME_LIMIT_FAILED              = "***********";
    const CARD_GOODS_CONSUME_LIMIT_FAILED                = "***********";
    const STATION_NOT_ENABLED                            = "***********";
    const STATION_NOT_LISTED                             = "***********";
    const OILER_NO_BIND_STATION                          = "***********";
    const CARD_PARSE_FAILED                              = "***********";
    const QR_CODE_EXPIRED                                = "***********";
    const STATION_ID_MISSING                             = "***********";
    const ORDER_REFUND_RECORD_ALREADY_EXISTS             = "***********";
    const PAGE_INVALID                                   = "***********";
    const PAGE_LIMIT_INVALID                             = "***********";
    const DRIVER_PHONE_INVALID                           = "***********";
    const ORDER_NO_INVALID                               = "32044120009";
    const PLATFORM_ORDER_NO_INVALID                      = "32044120010";
    const ID_REQUIRED                                    = "32044120011";
    const ID_INVALID                                     = "32044120012";
    const APPROVAL_STATUS_REQUIRED                       = "32044120013";
    const APPROVAL_STATUS_INVALID                        = "32044120014";
    const APPROVAL_REASON_INVALID                        = "32044120015";
    const APPROVAL_RECORD_NOT_EXISTS                     = "32045010008";
    const APPROVAL_RECORD_AlREADY_REVIEWED               = "32045010009";
    const WAIT_PAY_ORDER                                 = "32044130009";
    const CANNOT_CANCEL_NON_PENDING_ORDERS               = "32045010010";
    const INVALID_CARD_NO                                = "32045010011";
    const COUPON_TYPE_INVALID                            = "32045010012";
    const INVALID_SUPPLIER_PCODE                         = "32045010013";
    const HB_NOT_ALLOW_REFUND                            = "32045010014";
    const NOT_ALLOW_REFUND_FOR_WRITTEN_OFF               = "32045010015";
    const NOT_ALLOW_CANCEL_ORDER_COUPON                  = "32045010016";
    const NOT_ALLOW_CANCEL_ORDER_COUPON_BY_PAID_CANCELED = "32045010017";
    const APP_STATION_ID_INVALID                         = "32044120016";
    const COUPONS_NOT_EXISTS                             = "32044120017";
    const COUPONS_NOT_AVAILABLE_FOR_SALE                 = "32044120018";
    const INSUFFICIENT_COUPON_INVENTORY                  = "32044130019";
    const ORDER_PAID                                     = "32044121001";
    const ORDER_FAILED                                   = "32044121002";
    const ORDER_CANCELED                                 = "***********";
    const ORDER_REFUNDING                                = "***********";
    const ORDER_REFUNDED                                 = "***********";
    const ORDER_EXCEPTION                                = "***********";
    const CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_1       = "***********";
    const CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_2       = "***********";
    const STATION_CONNECT_ERROR                          = "***********";
    const ELECTRICITY_ACCOUNT_MONEY_NOT_ENOUGH           = "***********";
    const ELECTRICITY_ORDER_STATUS_EXCEPTION             = "***********";
    const ACCOUNT_MONEY_NOT_ENOUGH                       = "***********";
    const ELECTRICITY_DEVICE_EXCEPTION                   = "***********";

    static public $codeMsg = [
        self::SUCCESS_CODE                                   => '成功',
        self::SYSTEM_ERROR                                   => '服务异常,请稍后再试',
        self::HTTP_REQUEST                                   => '不支持的请求方式',
        self::SYSTEM_NOFOUND                                 => '找不到服务',
        self::NO_AUTH                                        => '无权限',
        self::SYSTEM_NO_PARAMS                               => '缺少参数',
        self::STATION_OFF                                    => '该油站已下线,请司机以站点列表中显示的站点为准',
        self::NO_CARD                                        => '账户信息不存在',
        self::ERROR_CARD_PCODE                               => '不支持该账户',
        self::CARD_PASSWORD                                  => '请输入账户密码',
        #self::ERROR_CARD_PWD  => '密码有误 请重新输入',
        self::NO_GUN                                         => '请选择油枪',
        self::QRCODE_ERROR                                   => '二维码识别失败，请重新扫码',
        self::ERROR_PRICE                                    => '价格设置有误，请检查',
        self::BILL_NO_FOUND                                  => '订单不存在',
        self::USER_ERROR                                     => '获取不到用户信息',
        self::ORDER_ERROR                                    => '下单失败',
        self::GUN_NUM                                        => '请输入枪号',
        self::ADAPTER_ERROR                                  => 'adpter服务异常',
        self::GUN_FAIL                                       => '输入的枪号有误,请和加油员确认枪号后重新输入',
        self::ERROR_MONEY                                    => '输入正确的金额',
        self::OVER_CARD_PWD                                  => '密码连续错误超过5次,账户已被锁定',
        self::STATION_OVERDUE                                => '未能扣款成功，油站信息有更新，请您确认油站信息后重新支付',
        self::GUN_ERROR_PRICE                                => '油站实际结算价格有误，请联系站点或采用其他方式支付',
        self::NO_QRCODE                                      => '无需生成核销凭证或该订单已退款',
        self::ADAPTER_NO_GUN                                 => '获取油枪列表失败',
        self::ERROR_OIL_NUM                                  => '输入正确的升数',
        self::ERROR_PCODE_PRICE                              => '站点应收单价有误，请联系G7人员进行核对',
        self::WX_TEMPLATE_DATA_REQUIRED                      => '微信模版消息模版变量不能为空',
        self::WX_TEMPLATE_DATA_INVALID                       => '微信模版消息模版变量不正确',
        self::WX_TO_USER_REQUIRED                            => '微信模版消息接收用户不能为空',
        self::WX_TO_USER_INVALID                             => '微信模版消息接收用户不正确',
        self::WX_TEMPLATE_ID_REQUIRED                        => '微信模版消息模版ID不能为空',
        self::WX_TEMPLATE_ID_INVALID                         => '微信模版消息模版ID不正确',
        self::QR_CODE_INVALID                                => '该二维码不是G7能源付款二维码，请司机出示正确的付款二维码',
        self::ABNORMAL_CARD                                  => '该账户状态异常，请司机联系车队长',
        self::ABNORMAL_CARD_ACCOUNT                          => '该账户异常，请司机联系车队长',
        self::CARD_STATION_CONSUME_LIMIT_FAILED              => '车队长禁止司机在此油站加油，请司机联系车队长',
        self::CARD_GOODS_CONSUME_LIMIT_FAILED                => '该账户在本站点没有可用商品，请司机联系车队长确认是否有商品限制',
        self::STATION_NOT_ENABLED                            => '站点已停用，请司机使用其他方式进行支付',
        self::STATION_NOT_LISTED                             => '站点已下线，请司机使用其他方式进行支付',
        self::OILER_NO_BIND_STATION                          => '该账号未绑定G7能源油站，请联系管理员。',
        self::CARD_PARSE_FAILED                              => '司机二维码识别失败，不能收款。',
        self::QR_CODE_EXPIRED                                => '二维码已失效，请司机刷新二维码后重新出示',
        self::STATION_ID_MISSING                             => '站点ID不能为空',
        self::ORDER_REFUND_RECORD_ALREADY_EXISTS             => '该订单已发起退款申请,请耐心等待审核结果',
        self::PAGE_INVALID                                   => '页码不正确',
        self::PAGE_LIMIT_INVALID                             => '每页显示数不正确',
        self::DRIVER_PHONE_INVALID                           => '司机手机号不正确',
        self::ORDER_NO_INVALID                               => '订单号不正确',
        self::PLATFORM_ORDER_NO_INVALID                      => '平台订单号不正确',
        self::ID_REQUIRED                                    => '数据唯一编号不能为空',
        self::ID_INVALID                                     => '数据唯一编号不正确',
        self::APPROVAL_STATUS_REQUIRED                       => '审核状态不能为空',
        self::APPROVAL_STATUS_INVALID                        => '审核状态不正确',
        self::APPROVAL_REASON_INVALID                        => '审核原因长度不正确',
        self::APPROVAL_RECORD_NOT_EXISTS                     => '审核记录不存在',
        self::APPROVAL_RECORD_AlREADY_REVIEWED               => '审核记录已被审核,请勿重复审核',
        self::WAIT_PAY_ORDER                                 => '请完成上一笔订单',
        self::CANNOT_CANCEL_NON_PENDING_ORDERS               => '订单状态非待支付无法取消,请核实后重试',
        self::INVALID_CARD_NO                                => '该授权机构仅支持本机构及子级机构下单!',
        self::COUPON_TYPE_INVALID                            => '选中的金额库存不足！',
        self::INVALID_SUPPLIER_PCODE                         => '该站点不支持核销二维码模式!',
        self::HB_NOT_ALLOW_REFUND                            => "supplier_name电子券当前状态为：status_desc<br>请先在GMS系统执行作废操作后，待supplier_name审核通过后再进行退款！（三方对接客户在OA退款）",
        self::NOT_ALLOW_REFUND_FOR_WRITTEN_OFF               => '已核销的订单不允许退款！',
        self::NOT_ALLOW_CANCEL_ORDER_COUPON                  => 'supplier_name的订单不支持执行该操作！',
        self::NOT_ALLOW_CANCEL_ORDER_COUPON_BY_PAID_CANCELED => 'G7订单已退款或supplier_name已核销或已注销的订单不允许再次注销！',
        self::APP_STATION_ID_INVALID                         => '供应商站点ID不能为空',
        self::COUPONS_NOT_EXISTS                             => "券不存在，请重新选择",
        self::COUPONS_NOT_AVAILABLE_FOR_SALE                 => "该券暂不支持对外销售，请选择其他面额券",
        self::INSUFFICIENT_COUPON_INVENTORY                  => "该券暂不支持对外销售，请选择其他面额券",
        self::ORDER_PAID                                     => "该笔订单已支付成功，请勿重复支付！	",
        self::ORDER_FAILED                                   => "该笔订单已支付失败，请重新下单支付！	",
        self::ORDER_CANCELED                                 => "该笔订单已超时取消，请重新下单支付！	",
        self::ORDER_REFUNDING                                => "该笔订单退款中，请重新下单支付！	",
        self::ORDER_REFUNDED                                 => "该笔订单已退款，请重新下单支付！	",
        self::ORDER_EXCEPTION                                => "该笔订单支付异常，请重新下单支付！	",
        self::CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_1       => "当前账户有未完成的订单，请换卡或订单结束后重试。",
        self::CARD_HAS_IN_PROGRESS_ELECTRICITY_ORDER_2       => "当前账户有未完成的订单，请订单结束后重试。",
        self::STATION_CONNECT_ERROR                          => "充电设备信息异常，请稍后再试",
        self::ELECTRICITY_ACCOUNT_MONEY_NOT_ENOUGH           => "充电金额过低，无法启动充电。请修改金额或联系车队充值后重试。",
        self::ELECTRICITY_ORDER_STATUS_EXCEPTION             => "订单状态异常,处理失败",
        self::ACCOUNT_MONEY_NOT_ENOUGH                       => "余额不足，请检查账户余额",
        self::ELECTRICITY_DEVICE_EXCEPTION                   => "设备无法识别或不可用，请重试或更换设备。",
    ];

    static function getMsgByCode($code)
    {
        return self::$codeMsg[$code] ?? "";
    }

    //转化提示语
    static public function formatMsg($errorMsg = '')
    {
        if (empty($errorMsg)) {
            return "";
        }
        $errorMsg = str_replace("[FOSS]", "", $errorMsg);
        if (stripos($errorMsg, "本月消费已达上限") !== false ||
            stripos($errorMsg, "本次加油金额超过月限额") !== false) {
            $errorMsg = "本次消费金额已超出该账户的月限额,请联系您的车队、客服~";
        } elseif (stripos($errorMsg, "今日消费已达上限") !== false ||
                  stripos($errorMsg, "已超过本日交易限额") !== false
        ) {
            $errorMsg = "本次消费金额已超出该账户的日限额,请联系您的车队、客服~";
        } elseif (stripos($errorMsg, "您每次的交易总额不得超过") !== false ||
                  stripos($errorMsg, "已超过本次交易限额") !== false
        ) {
            $errorMsg = "本次消费金额已超出该账户的次限额,请联系您的车队、客服~";
        } elseif (stripos($errorMsg, "账户余额不足本次消费") !== false || stripos($errorMsg, "信用额度不足") !== false ||
            stripos($errorMsg, "余额不足") !== false) {
            $errorMsg = "余额不足！请先检查司机余额，如充足则联系车队检查企业账户余额";
        } elseif (stripos($errorMsg, "不允许在本站点") !== false ||
                  stripos($errorMsg, "站点被限定使用，不能加油") !== false ||
                  stripos($errorMsg, "油站限制") !== false
        ) {
            $errorMsg = "站点被限定使用,请司机联系车队长、客服~";
        } elseif (stripos($errorMsg, "账户非使用状态") !== false) {
            $errorMsg = "该账户被锁定或冻结，请联系车队或换账户支付";
        } elseif (stripos($errorMsg, "授信账户已被锁定") !== false ||
                  stripos($errorMsg, "授信产品已被停用") !== false || stripos($errorMsg, "授信账户异常") !== false) {
            $errorMsg = "该账户被禁用，请联系车队或换账户支付";
        } elseif (stripos($errorMsg, "该商品被限定") !== false) {
            $errorMsg = "该商品被限定使用，请司机联系车队长~";
        }
        return $errorMsg;
    }
}