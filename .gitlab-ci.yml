stages:
- ship
- deploy
cache:
  untracked: true
  key: ${CI_BUILD_REF}_${CI_BUILD_REF_NAME}
before_script:
- export PATH=$PATH:/tmp/project
job_deploy_PHP-DEMO:
  stage: deploy
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_RANCHER}
  variables:
    RANCHER_ENVIRONMENT: OIL-DEMO
  environment:
    name: OIL-DEMO
  script:
  - project ci deploy
  only:
  - master
job_deploy_PHP-DEV:
  stage: deploy
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_RANCHER}
  variables:
    RANCHER_ENVIRONMENT: OIL-DEV
  environment:
    name: OIL-DEV
  script:
  - project ci deploy --latest
  only:
  - develop
job_deploy_PHP-TEST:
  stage: deploy
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_RANCHER}
  variables:
    RANCHER_ENVIRONMENT: OIL-TEST
  environment:
    name: OIL-TEST
  script:
  - project ci deploy
  only:
  - /^release\/.*$/
  - /^hotfix\/.*$/
job_ship:
  stage: ship
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_DOCKER}
  environment:
    name: OIL-TEST
  script:
  - project ci ship --push
  only:
  - /^release\/.*$/
  - /^hotfix\/.*$/
job_ship_latest:
  stage: ship
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_DOCKER}
  script:
  - project ci ship --latest --push
  only:
  - develop
job_ship_demo:
  stage: ship
  image: ${G7PAY_DOCKER_REGISTRY}/${BUILDER_DOCKER}
  environment:
    name: OIL-DEMO
  script:
  - project ci ship --push
  only:
  - master
