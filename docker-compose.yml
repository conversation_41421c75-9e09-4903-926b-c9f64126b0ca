version: "2"
services:
  foss-order:
    restart: always
    image: ${G7PAY_DOCKER_REGISTRY}/${PROJECT_GROUP}/${PROJECT_NAME}:${PROJECT_VERSION}
    labels:
      io.rancher.container.pull_image: always
      io.rancher.container.start_once: "true"
      project_description: G7能源账户订单服务
      project_group: php-spec
      project_name: foss-order
      project_type: php
      project_version: ${PROJECT_VERSION}
    environment:
      PHPENV: ${PHPENV}
      APPLICATION__NAME: ${APPLICATION__NAME}
      APPLICATION__DEBUG: ${APPLICATION__DEBUG}
      DB__CONNECTION: ${DB__CONNECTION}
      DB__HOST: ${DB__HOST}
      DB__PORT: ${DB__PORT}
      DB__DATABASE: ${DB__DATABASE}
      DB__USERNAME: ${DB__USERNAME}
      DB__PASSWORD: ${DB__PASSWORD}
      APP__NAME: ${APP__NAME}
      APP__ENV: ${APP__ENV}
      APP__KEY: ${APP__KEY}
      APP__DEBUG: ${APP__DEBUG}
      APP__LOG__LEVEL: ${APP__LOG__LEVEL}
      APP__URL: ${APP__URL}
      BROADCAST__DRIVER: ${BROADCAST__DRIVER}
      CACHE__DRIVER: ${CACHE__DRIVER}
      SESSION__DRIVER: ${SESSION__DRIVER}
      SESSION__LIFETIME: ${SESSION__LIFETIME}
      QUEUE__DRIVER: ${QUEUE__DRIVER}
      QUEUE_CONNECTION: ${QUEUE_CONNECTION}
      REDIS__HOST: ${REDIS__HOST}
      REDIS__PASSWORD: ${REDIS__PASSWORD}
      REDIS__PORT: ${REDIS__PORT}
      RUN__MODE: ${RUN__MODE}
      RUN_SCRIPTS: ${RUN_SCRIPTS}
      CRONTAB__ENABLE: ${CRONTAB__ENABLE}
      SUPERVISOR__ENABLE: ${SUPERVISOR__ENABLE}
      WEBROOT: '/data/web/public'
      PHP_ERRORS_STDERR: 'true'
      CAT__AGENT__TYPE: ${CAT__AGENT__TYPE}
      CAT__AGENT__HOST: ${CAT__AGENT__HOST}
      CAT__AGENT__PORT: ${CAT__AGENT__PORT}
    external_links:
    - tools-mid/redis:redis
    - tools-mid/consul:consul
    - tools-mid/mysql:mysql
    - tools-mid/cat-agent-udp:cat-agent-udp
    - tools-mid/host-cat:host-cat
    cap_add:
    - SYS_PTRACE
    mem_limit: 2147483648
