<?php

namespace App\Repositories\GetCanUseCoupon;

use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use App\Models\CouponsModel;
use RuntimeException;

class GetCanUseCoupon
{
    protected $mapping = [
        CommonDefine::HB_TEST_SUPPLIER_CODE  => CNPC::class,
        CommonDefine::HB_PROD_SUPPLIER_CODE  => CNPC::class,
        CommonDefine::HB2_TEST_SUPPLIER_CODE => HBKJ::class,
        CommonDefine::HB2_PROD_SUPPLIER_CODE => HBKJ::class,
        ''                                   => CNPC::class,
        CommonDefine::SH_PROD_SUPPLIER_CODE  => SH::class,
        CommonDefine::SH_TEST_SUPPLIER_CODE  => SH::class,
    ];

    public function handle($couponTypeId, $supplierCode = ''): ?CouponsModel
    {
        if (!isset($this->mapping[$supplierCode])) {
            throw new RuntimeException('', CommonError::SYSTEM_ERROR);
        }
        $worker = app($this->mapping[$supplierCode]);
        if (!$worker instanceof GetCanUseCouponInterface) {
            throw new RuntimeException('', CommonError::SYSTEM_ERROR);
        }
        return $worker->handle($couponTypeId);
    }
}