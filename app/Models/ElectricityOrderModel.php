<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

/**
 * Class ElectricityOrderModel
 * @package App\Models
 * @version June 9, 2025, 11:23 am CST
 *
 * @property string order_id
 * @property string supplier_order_id
 * @property string customer_order_id
 * @property string driver_phone
 * @property string station_id
 * @property string org_code
 * @property string supplier_code
 * @property boolean order_status
 * @property string card_no
 * @property number order_price
 * @property number order_money
 * @property number electricity_num
 * @property number electricity_money
 * @property number price
 * @property number service_money
 * @property number money
 * @property number supplier_electricity_money
 * @property number supplier_price
 * @property number supplier_service_money
 * @property number supplier_money
 * @property number customer_electricity_money
 * @property number customer_price
 * @property number customer_service_money
 * @property number customer_money
 * @property number pay_electricity_money
 * @property number pay_price
 * @property number pay_service_money
 * @property number pay_money
 * @property string|\Carbon\Carbon create_time
 * @property string|\Carbon\Carbon pay_time
 */
class ElectricityOrderModel extends BaseModel
{

    public $table = 'foss_electricity_order';

    public $timestamps = false;

    public $primaryKey = 'order_id';

    public $incrementing = false;

    public $connection = 'mysql_gas';

    public $fillable = [
        'order_id',
        'supplier_order_id',
        'customer_order_id',
        'driver_phone',
        'station_id',
        'org_code',
        'supplier_code',
        'order_status',
        'card_no',
        'order_price',
        'order_money',
        'electricity_num',
        'electricity_money',
        'price',
        'service_money',
        'money',
        'supplier_electricity_money',
        'supplier_price',
        'supplier_service_money',
        'supplier_money',
        'customer_electricity_money',
        'customer_price',
        'customer_service_money',
        'customer_money',
        'pay_electricity_money',
        'pay_price',
        'pay_service_money',
        'pay_money',
        'create_time',
        'pay_time'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'order_id'                   => 'integer',
        'supplier_order_id'          => 'string',
        'customer_order_id'          => 'string',
        'driver_phone'               => 'string',
        'station_id'                 => 'string',
        'org_code'                   => 'string',
        'supplier_code'              => 'string',
        'order_status'               => 'integer',
        'card_no'                    => 'string',
        'order_price'                => 'float',
        'order_money'                => 'float',
        'electricity_num'            => 'float',
        'electricity_money'          => 'float',
        'price'                      => 'float',
        'service_money'              => 'float',
        'money'                      => 'float',
        'supplier_electricity_money' => 'float',
        'supplier_price'             => 'float',
        'supplier_service_money'     => 'float',
        'supplier_money'             => 'float',
        'customer_electricity_money' => 'float',
        'customer_price'             => 'float',
        'customer_service_money'     => 'float',
        'customer_money'             => 'float',
        'pay_electricity_money'      => 'float',
        'pay_price'                  => 'float',
        'pay_service_money'          => 'float',
        'pay_money'                  => 'float',
        'create_time'                => 'datetime',
        'pay_time'                   => 'datetime'
    ];
    public const WAIT_START   = 10;
    public const CHARGING     = 20;
    public const STOPPING     = 30;
    public const WAIT_PAY     = 40;
    public const PAY_SUCCESS  = 50;
    public const PAY_FAIL     = 60;
    public const ORDER_CANCEL = 70;

    public static $orderStatus = [
        self::WAIT_START   => '待启动',
        self::CHARGING     => '充电中',
        self::STOPPING     => '停止中',
        self::WAIT_PAY     => '已结束待支付',
        self::PAY_SUCCESS  => '支付成功',
        self::PAY_FAIL     => '支付失败',
        self::ORDER_CANCEL => '订单取消',
    ];

    /**
     * @param array $whereParams
     * @param array $select
     * @param bool $lock
     * @return Model|Builder|mixed|object|null|ElectricityOrderModel
     */
    public static function getOneElectricityOrderByParams(array $whereParams, array $select = ['*'], bool $lock = false)
    {
        if ($lock) {
            return self::scopeWithCondition(self::select($select), $whereParams)->lockForUpdate()->first();
        }
        return self::scopeWithCondition(self::select($select), $whereParams)->first();
    }

    public static function insertElectricityOrder(array $params): bool
    {
        return self::insert($params);
    }

    public static function updateElectricityOrderByOrderId($orderId, array $updateArray)
    {
        return self::where(['order_id' => $orderId])->update($updateArray);
    }
}
