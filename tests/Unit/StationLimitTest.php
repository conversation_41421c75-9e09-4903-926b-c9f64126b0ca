<?php

namespace Tests\Unit;

use App\Repositories\StationLimitConfigRepository;
use Tests\TestCase;

class StationLimitTest extends TestCase
{
    protected $limitRepository;


    public function setUp(): void
    {
        parent::setUp();

        $this->limitRepository = app(StationLimitConfigRepository::class);
    }




    public function testStationLimitTest()
    {
        $orgCode = '200NYJ22';
        $data = $this->limitRepository->getBatchLimitStation($orgCode);
        $this->assertTrue(true);

    }
}
