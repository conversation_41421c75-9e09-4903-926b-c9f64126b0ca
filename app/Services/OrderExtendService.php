<?php


namespace App\Services;


use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrderExtendRepository;
use App\Library\Helper\Common;
use Illuminate\Support\Facades\Validator;

class OrderExtendService
{
    protected $orderExtendRepository;
    protected $historyRepository;
    protected $ossConfig;

    public function __construct
    (
        OrderExtendRepository $orderExtendRepository,
        HistoryRepository $historyRepository
    )
    {
        $this->orderExtendRepository = $orderExtendRepository;
        $this->historyRepository = $historyRepository;
        $this->ossConfig = config('oss');
    }

    /**
     * 保存司机签名
     *
     * @param $params
     * @return bool
     */
    public function addDriverSignature($params)
    {
        $orderId = $params['order_id'];
        $driverSignature = $this->ossConfig['host'] . ltrim($params['driver_signature'], '/');
        // 订单小票维护
        $this->orderExtendRepository->extendInsertOrUpdate($orderId, ['driver_signature' => $driverSignature]);
        // 交易流水小票维护
        $this->historyRepository->updateHistory(['id' => $orderId], ['imgurl' => ltrim($params['driver_signature'], '/')]);
        return true;
    }

    /**
     * 更新订单扩展表信息
     * @param $module
     * @param $content array
     */
    public function updateOrderExtInfo($module, $content)
    {
        if (empty($module) || empty($content))
            return;

        $ruleCal = [
            'order_id' => 'required',
            'straight_down_rebate' => 'required',
            'after_rebate' => 'required'
        ];

        $ruleO = [
            'order_id' => 'required',
            'upstream_settle_price' => 'required',
            'upstream_settle_money' => 'required',
        ];

        $taskFanliCalModule = 'task_upstream_fanli_calculate';

        foreach ($content as $item) {
            if ($module == $taskFanliCalModule) {
                $rule = $ruleCal;
            } else {
                $rule = $ruleO;
            }

            $validator = Validator::make($item, $rule);
            if ($validator->fails()) {
                Common::log('error', 'queue_order_operation_consume 参数不合法', $item);
                continue;
            }

            $order = app(NewOrderRepository::class)->getOneOrderByParams(['order_id' => $item['order_id']]);
            if (empty($order)) {
                // 订单不存在
                Common::log('error', 'queue_order_operation_consume 订单不存在', $item);
                continue;
            }

            if ($module == $taskFanliCalModule) {

                $data = [];

                $money = $order['ext']['mac_amount'];

                $data['plummet_rebate_money'] = $item['straight_down_rebate'];
                $data['later_rebate_money'] = $item['after_rebate'];
                $data['charge_rebate_money'] = 0;
                if(isset($item['mark_rebate'])) {
                    $data['charge_rebate_money'] = array_get($item, "mark_rebate", 0);
                }
                $_tmpCost = bcsub(bcsub($money, $item['straight_down_rebate'], 6), $item['after_rebate'], 6);
                $data['cal_cost_money'] = bcsub($_tmpCost, $data['charge_rebate_money'], 6);
                $data['cal_cost_price'] = bcdiv($data['cal_cost_money'], $order['real_oil_num'], 6);


            } else {
                $data = $item;
                unset($data['order_id']);
            }

            if (empty($data)) {
                continue;
            }

            $this->orderExtendRepository->update($item['order_id'], $data);
        }


    }
}