<?php


namespace App\Http\Controllers\Order;


use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Services\HistoryApproveService;
use App\Services\NewOrderService;
use App\Services\OrderApproveService;
use App\Servitization\Foss;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Throwable;

class OrderApproveController extends Controller
{
    protected $orderApproveService;
    protected $historyApproveService;

    public function __construct
    (
        OrderApproveService $orderApproveService,
        HistoryApproveService $historyApproveService
    )
    {
        $this->orderApproveService = $orderApproveService;
        $this->historyApproveService = $historyApproveService;
    }

    /**
     * 发起退款审核
     *
     * @return JsonResponse
     */
    public function refundApprove()
    {
        $field = [
            'order_id'      => '',
            'remark'        => '', // 退款原因
            'ticket_image'  => '', // 加油凭证
            'truck_image'   => '', // 加油车辆
            'other_image'   => '', // 其他信息
            'force'         => 0, // 四川壳牌是否强制退款
            'approve_pass'  => 0, // 强制退款false
            'refund_system' => 1, // 退款系统
        ];

        $rule = [
            'order_id'      => 'required|numeric',
            'remark'        => 'required|string|max:200',
            'force'         => 'required|integer|in:0,1',
            'approve_pass'  => 'in:0,1',
            'refund_system' => 'in:1,2',
        ];

        $message = [
            'order_id.required' => '订单号必传!',
            'order_id.numeric'  => '订单号格式错误!',
            'reason.required'   => '退款原因必传!',
            'reason.string'     => '退款原因格式错误!',
            'approve_pass.in'   => '自动审核退款单 0否 1是'
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->orderApproveService->refundApprove($params);
            return $this->success($result, true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 创建补录申请单
     *
     * @return JsonResponse
     */
    public function additionalAddOrUpdate()
    {
        $field = [
            'id'                => 0,
            'card_no'           => null, // 账号 原16位卡号
            'card_account_type' => null, //司机扣款账户类型 1现金账户
            'driver_phone'      => '', // 司机手机号
            'truck_no'          => '', // 车牌号
            'driver_name'       => '', // 司机姓名
            'station_id'        => '', // 油站ID
            'oil_type'          => '', // 油品类型
            'oil_name'          => '', // 油品名称
            'oil_level'         => '', // 油品等级
            'tank_id'           => '', // 油罐ID
            'gun_id'            => '', // 油枪ID
            'oil_time'          => '', // 实际加油时间
            'oil_unit'          => null, // 加油方式 1按金额 2按升
            'mac_price'         => null,
            'supplier_price'    => null,
            'platform_price'    => null,
            'mac_amount'        => null,
            'supplier_money'    => null,
            'oil_money'         => null, // 司机应付金额,  之前叫 pay_money
            //            'pay_price'         => null, // 司机应付单价   去掉
            'oil_num'           => null, // 加油数量
            'remark'            => '', // 备注
            'ticket_image'      => '', // 加油凭证
            'truck_image'       => '', // 加油车辆
            'other_image'       => '', // 其他信息
            'source'            => 0, // 来源
            'force'             => 0, // 在G7能源账户余额不足时是否创建补录申请单
            'third_order_id'    => '', // 三方订单号
            'discount_rate'     => '', // 折扣率
            'settlement_type'   => '', //结算方式
            'pay_company_id'    => '', // 付款公司ID
        ];

        $rule = [
            'id'                => 'integer',
            'card_no'           => 'required|numeric',
            'card_account_type' => 'required|in:1',
            'driver_phone'      => 'required|phone_no',
            'truck_no'          => 'string|max:20',
            'driver_name'       => 'string|max:10',
            'station_id'        => 'required|string',
            'oil_type'          => 'string',
            'oil_name'          => 'required|string',
            'oil_level'         => 'string',
            'tank_id'           => 'required|string',
            'gun_id'            => 'required|string',
            'oil_time'          => 'required|date_format:Y-m-d H:i:s',
            'oil_unit'          => 'required|integer|in:1,2',
            'mac_price'         => 'required|range_price',
            'supplier_price'    => 'required|range_price',
            'platform_price'    => 'required|range_price',
            'mac_amount'        => 'required|money',
            'supplier_money'    => 'required|money',
            'oil_money'         => 'required|money', // 司机应付金额,  之前叫 pay_money
            'oil_num'           => 'required|money',
            'remark'            => 'required|string|max:200',
            'ticket_image'      => 'string',
            'truck_image'       => 'string',
            'other_image'       => 'string',
            'force'             => 'required|in:0,1',
            'third_order_id'    => 'string',
            'discount_rate'     => 'numeric', //折扣率
            'settlement_type'   => 'integer', //结算方式：1按单价*升数=结算金额；2按油机金额*折扣率=结算金额
            'pay_company_id'    => 'nullable|integer',
        ];

        $message = [
            'card_account_type.in'    => '无效的扣款账户 1现金账户',
            'truck_no.max'            => '车牌号长度不得超过20字符',
            'remark.required'         => '申请原因不能为空',
            'remark.max'              => '申请原因不得超过200个字符',
            'oil_unit.in'             => '无效的加油类型 1指定金额加油 2指定升数加油',
            'driver_phone.phone_no'   => '手机号格式错误',
            'discount_rate.numeric'   => '折扣率格式错误',
            'settlement_type.integer' => '结算方式错误',
            'pay_company_id.integer'  => '付款公司ID格式不正确',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);

            $result = $this->orderApproveService->additionalAddOrUpdate($params);
            return $this->success($result, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 审核通过
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvePass()
    {
        $field = [
            'id' => '',
        ];
        $rule = [
            'id' => 'required|integer',
        ];
        $message = [
            'id.required' => '审核单单号必传!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->orderApproveService->approvePass($params);
            return $this->success($result, true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 预检查异常修改申请单
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function exceptionModificationPreCheck(): JsonResponse
    {
        $field = [
            'order_id' => 0,
        ];
        $rule = [
            'order_id' => 'required|numeric',
        ];
        $message = [
            'order_id.required' => '请选择需要修改的订单',
            'order_id.numeric'  => '订单号格式不正确',
        ];
        try {

            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);
            $this->orderApproveService->exceptionModificationPreCheck($params);
            return $this->success([], true, '成功');
        } catch (\Throwable $t) {

            return $this->fail(empty($t->getCode()) ? 403 : $t->getCode(), $t->getMessage());
        }
    }

    /**
     * 创建异常修改申请单
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function exceptionModificationAddOrEdit(): JsonResponse
    {
        $field = [
            'id'             => null,
            'order_id'       => 0,
            'oil_type'       => '', // 油品类型
            'oil_name'       => '', // 油品名称
            'oil_level'      => '', // 油品等级
            'oil_unit'       => null, // 加油方式 1按金额 2按升
            'mac_price'      => null,
            'supplier_price' => null,
            'platform_price' => null,
            'mac_amount'     => null,
            'supplier_money' => null,
            'oil_money'      => null, // 司机应付金额
            'oil_num'        => null, // 加油数量
            'ticket_image'   => '', // 加油凭证
            'truck_image'    => '', // 加油车辆
            'other_image'    => '', // 其他信息
            'source'         => 1, // 来源
            'remark'         => '',
            'settlement_type' => '',
            'discount_rate'   => '',
        ];
        $rule = [
            'id'             => 'required_without:order_id|numeric|nullable',
            'order_id'       => 'required_without:id|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required|string',
            'oil_level'      => 'string',
            'oil_unit'       => 'required|in:1,2',
            'mac_price'      => 'required|range_price',
            'supplier_price' => 'required|range_price',
            'platform_price' => 'required|range_price',
            'mac_amount'     => 'required|money|gt:0',
            'supplier_money' => 'required|money|gt:0',
            'oil_money'      => 'required|money|gt:0', // 司机应付金额
            'oil_num'        => 'required|money|gt:0',
            'ticket_image'   => 'string',
            'truck_image'    => 'string',
            'other_image'    => 'string',
            'remark'         => 'required|string|max:512',
            'settlement_type'  => 'integer',
            'discount_rate'    => 'numeric',
        ];
        $message = [
            'id.required_without'       => '审核单ID格式不正确',
            'id.numeric'                => '审核单ID格式不正确',
            'order_id.required_without' => '请选择需要修改的订单',
            'order_id.numeric'          => '订单号格式不正确',
            'oil_num.money'             => '只能输入小数后两位',
            'oil_name.required'         => '请选择商品',
            'oil_name_name.required'    => '请选择商品',
            'remark.required'           => '申请原因不能为空',
            'remark.max'                => '申请原因不能超过512个字符',
            'oil_unit.required'         => '无效的计费模式: 1指定金额加油 2指定升数加油',
            'oil_unit.in'               => '无效的计费模式: 1指定金额加油 2指定升数加油',
            'mac_price.required'        => '油机价不正确',
            'mac_price.money'           => '油机价不正确',
            'platform_price.required'   => 'G7销价不正确',
            'platform_price.money'      => 'G7销价不正确',
            'mac_amount.required'       => '枪金额不正确',
            'mac_amount.money'          => '枪金额不正确',
            'oil_money.required'        => '司机应付金额不正确',
            'oil_money.money'           => '司机应付金额不正确',
            'supplier_price.required'   => 'G7进价不正确',
            'supplier_price.money'      => 'G7进价不正确',
            'supplier_money.required'   => '站点应收金额不正确',
            'supplier_money.money'      => '站点应收金额不正确',
            'settlement_type.integer'   => '结算方式不正确',
            'discount_rate.numeric'     => '折扣率填写不正确',
        ];
        try {

            $params = $this->getParamAll($field);
            $params['operator'] = Request::get('user_name');
            $this->validator($params, $rule, $message);
            $this->orderApproveService->exceptionModificationApprove($params);
            return $this->success([], true, '成功');
        } catch (\Exception $e) {

            return $this->fail(empty($e->getCode()) ? 403 : $e->getCode(), $e->getMessage());
        }
    }

    /**
     * @throws Throwable
     */
    public function approveResultCallback(Request $request)
    {
        $rule = [
            'id' => 'required|integer',
            'approve_status' => 'required|in:1,2',
        ];
        $message = [
            'id.required' => '审核单单号必传!',
            'id.integer' => '审核单单号必传!',
            'approve_status.required' => '审核结果枚举值不能为空',
            'approve_status.in' => '审核结果枚举值不正确',
        ];
        $params = $request->all();
        $this->validator($params, $rule, $message);
        $params['source'] = 'oa';
        $params['operator'] = '系统';
        if ($params['approve_status'] == 1) {
            $this->orderApproveService->approvePass($params);
            return $this->success();
        }
        $this->historyApproveService->approveRefuse($params);
        return $this->success();
    }

    /**
     * 是否订单发起退款
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrderRefundInfo()
    {
        $res= [];
        $field = [
            'order_id' => null,
        ];

        $rule = [
            'order_id' => 'required|string',
        ];

        $message = [
            'order_id.required' => '订单ID必传',
        ];
        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);
            $result = $this->historyApproveService->getApproveInfoByOrderId($params['order_id']);
            if($result){
                $res = $result;
            }
            return $this->success($res, true, '成功');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * Desc: 补录三方单号
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 10/1/23 下午3:47
     * @return JsonResponse
     * @throws Throwable
     */
    public function addApproveThirdOrder()
    {
        $field = [
            'id' => '',
            'third_order_id' => '',
        ];
        $rule = [
            'id' => 'required|integer',
            'third_order_id' => 'required',
        ];
        $message = [
            'id.required' => '审核单单号必传!',
            'third_order_id.required' => '三方单号必传!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->orderApproveService->addApproveThirdOrder($params);
            return $this->success($result, true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    public function checkOrderIsReceipt()
    {
        $field = [
            'order_id' => '',
        ];
        $rule = [
            'order_id' => 'required',
        ];
        $message = [
            'order_id.required' => '订单号必传!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->orderApproveService->checkOrderIsReceipt($params);
            return $this->success($result, true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 对接客户发起退款申请
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function oaRefundApplication(\Illuminate\Http\Request $request)
    {
        $rule = [
            'order_id'      => 'required|numeric',
            'refund_reason' => 'nullable|string|max:200',
        ];
        $message = [
            'order_id.required'    => '订单号不能为空',
            'order_id.numeric'     => '订单号格式不正确',
            'refund_reason.string' => '退款原因格式不正确',
            'refund_reason.max'    => '退款原因超长',
        ];
        try {
            $params = $request->all();
            $this->validator($params, $rule, $message);
            $request->offsetSet('order_channel', 6);
            $request->offsetSet('order_channel_enum', [6 => 'OA']);
            $params['remark'] = $params['refund_reason'] ?? '';
            $result = $this->orderApproveService->refundApprove($params);
            return $this->success($result, true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * @Notes:机构机构结算方式（1：升数*单价；2：油机金额*折扣率）
     * @Interface getOrgSettlementType
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     * @author: yuanzhi
     * @Time: 2023/12/20   4:04 PM
     */
    public function getOrgSettlementType(\Illuminate\Http\Request $request): JsonResponse
    {
        $rule = [
            'orgcode'      => 'required|string',
        ];
        $message = [
            'orgcode.required'    => '机构编码不为空',
        ];
        try {
            $params = $request->all();
            $this->validator($params, $rule, $message);
            $type = NewOrderService::getOrderSettlementType($params['orgcode']);
            return $this->success(['orgcode' => $params['orgcode'], 'settlement_type' => $type], true, '成功!');
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    public function checkIfRefundRecordExists(\Illuminate\Http\Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $this->validator($params, [
                'third_order_id' => 'required|alpha_dash',
            ], [
                'third_order_id.required'   => '三方单号不能为空',
                'third_order_id.alpha_dash' => '三方单号格式不正确',
            ]);
            return $this->success([
                'exists' => $this->orderApproveService->checkIfRefundRecordExists($params)
            ], true, '成功');
        } catch (\Exception $e) {
            return $this->fail(empty($e->getCode()) ? 5000001 : $e->getCode(), $e->getMessage());
        }
    }

    /**
     * @throws ValidationException
     */
    public function getCompanyListByOrg(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'orgcode'      => 'required|string',
        ], [
            'orgcode.required'    => '机构编码不为空',
        ]);
        return $this->success((new Foss())->getCompanyListByOrgCode([
            'org_code' => $params['orgcode'],
            'fields'   => ['id', 'company_name'],
            'status'   => 1,
        ]), true, '成功!');
    }
}