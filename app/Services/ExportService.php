<?php


namespace App\Services;


use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Library\Export\ExportCsv;
use App\Library\Helper\Common;
use App\Library\Upload\Upload;
use App\Models\HistoryModel;
use App\Models\OrderModel;
use App\Repositories\CityRepository;
use App\Repositories\DictRepository;
use App\Repositories\HistoryPayRepository;
use App\Repositories\HistoryRepository;
use App\Repositories\NewOrderRepository;
use App\Repositories\OrgRepository;
use App\Repositories\StationGunRepository;
use App\Repositories\StationRepository;
use App\Repositories\SupplierRepositories;
use App\Servitization\Foss;

class ExportService
{
    protected $stationService;
    protected $orderService;
    protected $newOrderService;
    protected $cityRepository;
    protected $orgRepository;
    protected $stationRepository;
    protected $historyRepository;
    protected $supplierRepositories;
    protected $historyPayRepository;
    protected $stationGunRepository;
    protected $dictRepository;
    protected $newOrderRepository;
    protected $foss;
    protected $exportCsv;

    public function __construct
    (
        StationService       $stationService,
        OrderService         $orderService,
        NewOrderService      $newOrderService,
        OrgRepository        $orgRepository,
        CityRepository       $cityRepository,
        StationRepository    $stationRepository,
        HistoryRepository    $historyRepository,
        SupplierRepositories $supplierRepositories,
        HistoryPayRepository $historyPayRepository,
        StationGunRepository $stationGunRepository,
        DictRepository       $dictRepository,
        NewOrderRepository   $newOrderRepository,
        Foss                 $foss,
        ExportCsv            $exportCsv
    )
    {
        $this->stationService = $stationService;
        $this->orderService = $orderService;
        $this->newOrderService = $newOrderService;
        $this->orgRepository = $orgRepository;
        $this->cityRepository = $cityRepository;
        $this->stationRepository = $stationRepository;
        $this->historyRepository = $historyRepository;
        $this->supplierRepositories = $supplierRepositories;
        $this->historyPayRepository = $historyPayRepository;
        $this->stationGunRepository = $stationGunRepository;
        $this->dictRepository = $dictRepository;
        $this->newOrderRepository = $newOrderRepository;
        $this->foss = $foss;
        $this->exportCsv = $exportCsv;
    }

    /**
     * 获取交易流失日期最大范围
     * @return int
     */
    public function getMaxDay()
    {
        $maxDay = 90; //天

        try{
            //请求foss配置
            $data = $this->foss->getStationTimeLimit([]);
            if($data['time_limit'])
            {
                $maxDay = $data['time_limit'];
            }
        }catch (\Exception $e){
            Common::log('info','getMaxDay获取交易流失日期最大范围值失败：'.$e->getMessage(),[]);
        }

        return $maxDay;
    }

    /**
     * 交易流水报表导出
     *
     * @param $params
     * @param $header
     * @param $filterField
     * @return int|mixed
     * @throws \Exception
     */
    public function exportHistory($params,$header=[],$exportField=[])
    {
        $selectParams = [];

        $stationCode = [];
        // 渠道ID,站点编码
        if (!empty($params['channel_id']) && !empty($params['station_code'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
            $stationCode = array_intersect($channelStationCode, [$params['station_code']]);
            if (empty($stationCode)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
        } elseif (!empty($params['channel_id'])) {
            $channelStationCode = $this->foss->getStationCodeByChannel(['supplier_id' => $params['channel_id']]);
            if (empty($channelStationCode)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
            $stationCode = $channelStationCode;
        } elseif (!empty($params['station_code'])) {
            $stationCode = [$params['station_code']];
        }

        // 站点名称,站点编码
        if (!empty($params['station_id']) && !empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }

            $station_id_array = explode(',', $params['station_id']);
            $arrayIntersectStation = array_intersect($station_id_array, $stationIds);
            if (empty($arrayIntersectStation)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
            $selectParams['station_id'] = $arrayIntersectStation;
        } elseif (!empty($params['station_id'])) {
            $selectParams['station_id'] = explode(',', $params['station_id']);
        } elseif (!empty($stationCode)) {
            $stationIds = $this->stationService->getStationIdsByStationCodes($stationCode);
            if (empty($stationIds)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
            $selectParams['station_id'] = $stationIds;
        }
        // 交易流水ID
        if (!empty($params['business_log_id'])) {
            $selectParams['id'] = $params['business_log_id'];
        }
        // G7流水ID
        if (!empty($params['serial_num'])) {
            $selectParams['serial_num'] = $params['serial_num'];
        }
        // 三方站点ID
        if (!empty($params['stream_no'])) {
            $id = $this->orderService->getHistoryIdByOrderId($params['stream_no']);
            if (empty($id)) {
                throw new \Exception('查询结果集为空,无须导出!');
            }
            $selectParams['id'] = $id;
        }
        // 单据类型
        $selectParams['log_type'] = [
            HistoryModel::COST_SUCCESS,
            HistoryModel::COST_ABNORMAL,
            HistoryModel::COST_SUCCESS_ADD,
            HistoryModel::COST_INVALID,
            HistoryModel::COST_INVALID_2,
            HistoryModel::COST_RESERVATION_REFUEL,
            HistoryModel::COST_RESERVATION_REFUEL_REFUND,
        ];
        if (!empty($params['log_type'])) {
            $selectParams['log_type'] =  explode(',', $params['log_type']);
        }
        // 账号
        if (!empty($params['card_no'])) {
            $selectParams['card_no'] = $params['card_no'];
        }
        // 站点供应商
        if (!empty($params['pcode'])) {
            $selectParams['pcode'] = $params['pcode'];
        }
        // 司机机构(查看子机构)
        if (!empty($params['org_code']) && !empty($params['need_relevant_org_order'])) {
            $selectParams['left_gascode'] = $params['org_code'];
        } else if (!empty($params['org_code'])) {
            $selectParams['gascode'] = $params['org_code'];
        }
        // 司机姓名
        if (!empty($params['driver_name'])) {
            $selectParams['like_driver_name'] = $params['driver_name'];
        }
        // 司机手机号
        if (!empty($params['driver_phone'])) {
            $selectParams['left_driver_phone'] = $params['driver_phone'];
        }
        // 司机车牌号
        if (!empty($params['truck_no'])) {
            $selectParams['like_truck_no'] = $params['truck_no'];
        }
        // 所属省市
        if (!empty($params['province_code'])) {
            $selectParams['provice_code'] = $params['province_code'];
        }
        if (!empty($params['city_code'])) {
            $selectParams['city_code'] = $params['city_code'];
        }
        // 商品(name、type、level)
        if (!empty($params['oil_type'])) {
            $selectParams['oil_type'] = $params['oil_type'];
        }
        if (!empty($params['oil_name'])) {
            $selectParams['oil_name'] = $params['oil_name'];
        }
        if (!empty($params['oil_level'])) {
            $selectParams['oil_level'] = $params['oil_level'];
        }

        // 创建时间
        if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
            // 不能超过180天
            if (strtotime($params['le_create_time']) - strtotime($params['ge_create_time']) > 180*86400) {
                throw new \Exception('最多可查180天创建记录!');
            }
            $selectParams['ge_createtime'] = date('Y-m-d H:i:s', strtotime($params['ge_create_time']));
            $selectParams['le_createtime'] = date('Y-m-d H:i:s', strtotime($params['le_create_time']));
        }
        // 加油时间
        $selectParams['ge_oil_time'] = date('Y-m-d 00:00:00', time() - 180*86400);
        $selectParams['le_oil_time'] = date('Y-m-d 23:59:59', time());
        if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
            // 不能超过180天
            if (strtotime($params['le_oil_time']) - strtotime($params['ge_oil_time']) > 180*86400) {
                throw new \Exception('最多可查180天加油记录!');
            }
            $selectParams['ge_oil_time'] = date('Y-m-d H:i:s', strtotime($params['ge_oil_time']));
            $selectParams['le_oil_time'] = date('Y-m-d H:i:s', strtotime($params['le_oil_time']));
        }

        // G7能源账户筛选条件
        $selectParams['support_type'] = 2;
        $selectParams['xpcode'] = '20003JCP';

        // 获取需要导出的条数
        $count = $this->historyRepository->getHistoryCountByParams($selectParams);
        if (empty($count)) {
            throw new \Exception('查询结果集为空,无须导出!');
        }

        /**
         * 同步导出
         */
        $fileName = date('YmdHis').'站点交易流水.csv';
        /**
         * 分块写入csv文件
         */
        $this->exportCsv->getCsvHeader($fileName);
        $resource = fopen('php://output', 'a');// 打开文件句柄,追加方式写入

        // 设定每次导出500条
        $limit = 500;
        // 油品数据字典
        $oilDict = $this->dictRepository->getOilDict();
        // 省市字典
        $provinceCityDict = $this->cityRepository->getProvinceCityDict(false)->keyBy('city_code')->toArray();
        // 对G7能源账户开放站点信息
        $stationInfo = $this->stationRepository->getAllOneCardStation(['id', 'station_name', 'station_code'],false)->keyBy('id')->all();
        // 供应商信息
        $supplierInfo = $this->supplierRepositories->getOneCardSupplier(['scode', 'supplier_name'], false)->keyBy('scode')->all();
        // G7能源账户机构信息
        $orgInfo = $this->orgRepository->getOneCardOrg(['orgcode', 'org_name'], false)->keyBy('orgcode')->all();
        /**
         * csv文件表头
         */
        if(!empty($header)){
            $exportHeader=$header;
        }else{
            $exportHeader = [
                'ID', 'G7流水ID', '单据类型', '站点名称', '站点编码', '站点运营商', '商品', '站点应收金额', '客户应付金额', '站点应收单价',
                '客户应付单价', '数量', '账号', '司机机构', '司机姓名', '司机手机号', '司机车牌号', '省', '市', '枪号',
                '订单来源', '三方订单号', '操作人', '创建时间', '交易时间',
            ];
        }
        if(empty($exportField)){
            $exportField=[
                'id', 'serial_num', 'log_type', 'station_name', 'station_code', 'supplier_name', 'goods', 'money', 'pay_money', 'price', 'pay_price', 'oil_num', 'card_no',
                'gascode_name', 'driver_name', 'driver_phone', 'driver_phone', 'truck_no', 'province_name', 'city_name', 'gun_name', 'data_type', 'stream_no',
                'truename', 'createtime', 'oil_time'
            ];
        }
        fwrite($resource, "\xEF\xBB\xBF".implode(',', $exportHeader)."\n");

        for ($page = 1; $page <= ceil($count/$limit); $page++) {
            $data = $this->historyRepository->getExportHistoryPaginate($selectParams, $page, $limit);

            if (empty($data)) {
                continue;
            }
            // 订单流水信息
            $historyIdBatch = array_unique(array_column($data, 'id'));
            $select = ['order_id', 'history_id'];
            $historyPayInfo = $this->historyPayRepository->getHistoryPayInfoByParamsBatch(['history_id' => $historyIdBatch], $select, false)->keyBy('history_id')->all();
            // 油枪信息
            $gunIdBatch = array_unique(array_column($data, 'gun_id'));
            $select = ['id', 'name'];
            $gunInfo = $this->stationGunRepository->getBatchGunByParams(['id' => $gunIdBatch], $select, false)->keyBy('id')->all();
            $str = '';
            foreach ($data as $key => $item) {
                if(in_array('id',$exportField))$value['id'] = $item['id']."\t";
                if(in_array('serial_num',$exportField))$value['serial_num'] = $item['serial_num']."\t";
                if(in_array('log_type',$exportField))$value['log_type'] = HistoryModel::$LOG_TYPE_ENUM[$item['log_type']]['msg'] ?? '';
                if (in_array('station_name', $exportField)) $value['station_name'] = isset($stationInfo[$item['station_id']]['station_name']) ? implode(' ', explode(',', $stationInfo[$item['station_id']]['station_name'])) : '';
                if (in_array('station_code', $exportField)) $value['station_code'] = $stationInfo[$item['station_id']]['station_code'] ?? '';
                if (in_array('supplier_name', $exportField)) $value['supplier_name'] = $supplierInfo[$item['pcode']]['supplier_name'] ?? '';
                if (in_array('goods', $exportField)) $value['goods'] = array_get($oilDict, 'oil_type.' . $item['oil_type'], '') . array_get($oilDict, 'oil_name.' . $item['oil_name'], '') . array_get($oilDict, 'oil_level.' . $item['oil_level'], '');
                if (in_array('money', $exportField)) $value['money'] = $item['xpcode_pay_money']; //站点应收金额
                if (in_array('pay_money', $exportField)) $value['pay_money'] = $item['data_type'] == '13' ? $item['money'] : $item['pay_money']; //客户应收金额
                if (in_array('price', $exportField)) $value['price'] = $item['xpcode_pay_price']; //站点应收单价
                if (in_array('pay_price', $exportField)) $value['pay_price'] = $item['pay_price']; //客户应付单价
                if (in_array('oil_num', $exportField)) $value['oil_num'] = $item['oil_num'];
                if (in_array('card_no', $exportField)) $value['card_no'] = $item['card_no'] . "\t";
                if (in_array('gascode_name', $exportField)) $value['gascode_name'] = isset($orgInfo[$item['gascode']]['org_name']) ? trim($orgInfo[$item['gascode']]['org_name']) : ''; //司机机构
                if (in_array('driver_name', $exportField)) $value['driver_name'] = $item['driver_name'] ?: '';
                if (in_array('driver_phone', $exportField)) $value['driver_phone'] = $item['driver_phone'] ? $item['driver_phone'] . "\t" : '';
                if (in_array('truck_no', $exportField)) $value['truck_no'] = $item['truck_no'] ?: '';
                //$imgUrl = json_decode($item['imgData'], true);
                //if (in_array('ticket_url', $exportField)) $value['ticket_url'] = !empty($imgUrl['carnumUrl']) ?  Upload::getSignUrl($imgUrl['carnumUrl']) : ''; //电子票
                if (in_array('province_name', $exportField)) $value['province_name'] = $provinceCityDict[$item['provice_code']]['city_name'] ?? '';
                if (in_array('city_name', $exportField)) $value['city_name'] = $provinceCityDict[$item['city_code']]['city_name'] ?? '';
                if (in_array('gun_name', $exportField)) $value['gun_name'] = $gunInfo[$item['gun_id']]['name'] ?? '';
                $dataType = $item['data_type'] == '35' ? '31' : ($item['data_type'] == '41' ? '4' : $item['data_type']);
                if (in_array('data_type', $exportField)) $value['data_type'] = HistoryModel::$dataTypeList[$dataType] ?? '';
                if (in_array('stream_no', $exportField)) $value['stream_no'] = !empty($historyPayInfo[$item['id']]['order_id']) ? $historyPayInfo[$item['id']]['order_id'] . "\t" : (isset($item['stream_no']) ? $item['stream_no'] . "\t" : ''); //第三方订单号
                if (in_array('old_id', $exportField)) $value['old_id'] = !empty($item['old_id']) ? $item['old_id'] . "\t" : ''; //原始流水号
                if (in_array('truename', $exportField)) $value['truename'] = $item['truename'] ?: ''; //操作人
                if (in_array('createtime', $exportField)) $value['createtime'] = $item['createtime']; //创建时间
                if (in_array('oil_time', $exportField)) $value['oil_time'] = $item['oil_time']; //交易时间
                foreach ($value as &$v) {
                    $v = trim($v, "\n");
                }
                //拼接写入临时存放文件的字符串
                $str .= implode(',', $value) . "\n";

            }
            /**
             * csv文件内容
             */
            fwrite($resource, $str);

            if (ob_get_level() > 0) {
                ob_flush();
                flush();
            }
        }

        fclose($resource);
    }

    /**
     * 订单导出
     *
     * @param $params
     * @throws \Exception
     */
    public function exportOrder($params)
    {

        $selectParams = $this->newOrderService->getOrderSelectParams($params);

        if (($selectParams['select'] == true) && ($selectParams['hit'] == false)) {
            throw new \Exception('查询结果集为空,无数据导出!');
        }

        $count = $this->newOrderRepository->getOrderCount($selectParams['selectParams']);
        if (empty($count)) {
            throw new \Exception('查询结果为空,无数据导出!');
        }

        /**
         * 同步导出
         */
        $fileName = date('YmdHis').'站点交易订单.csv';
        $this->exportCsv->getCsvHeader($fileName);
        $resource = fopen('php://output', 'a');
        $exportHeader = [
            '订单号', '订单类型','订单状态', '站点名称', '站点编码', '站点运营商', '商品', '加注数量', '枪金额', '站点应收金额', '客户应付金额','下游直降总额',
            '客户司机实付金额',  '下游后返现金', '下游后返积分', '下游成本单价', '下游成本总额','实际盈利','挂牌价', '油机价', '站点应收单价', '客户应付单价',
            '客户司机实付单价',  '上游直降总额', '直降成本单价', '直降成本总额', '消费后返总额','充值后返总额', '上游成本单价',
            '上游成本总额', '账号', '司机机构', '司机姓名', '司机手机号', '司机车牌号',
            '交易流水号', '订单失败原因', '订单来源', '三方订单号', '创建人', '更新人',
            '创建时间', '更新时间', '支付时间', '省', '市', '枪号'
        ];
        fwrite($resource, "\xEF\xBB\xBF".implode(',', $exportHeader)."\n");
        /**
         * 每次导出1000条
         */
        $limit = 1000;
        for ($page = 1; $page <= ceil($count/$limit); $page++) {
            $result = $this->newOrderRepository->getOrderExportPaginate($selectParams['selectParams'], ['*'], $page, $limit, true);
            $exportStr = '';
            $ids = [];
            foreach ($result as $item) {
                $ids[] = $item['order_id'];
            }
            $viceCardInfos = (new Foss())->getViceInfoByOrderIds(['order_ids' => $ids]);
            foreach ($result as $item) {
                $mirror = $item['ext'];
                if (empty($mirror)) {
                    $mirror = json_decode($item['mirror'], true);
                }

                $supplier_money = sprintf('%.2f', round(bcmul($item['real_oil_num'], $mirror['supplier_price'], 3), 2)); // 站点应收
                if (in_array($item['supplier_code'], CommonDefine::showOriginalSupplierMoney())) {
                    $supplier_money = $mirror['original_supplier_money'];
                }
                $mac_amount = array_get($mirror,"mac_amount",0);
                if (empty($mac_amount) || bccomp($mac_amount, 0, 2) == 0) {
                    $mac_amount = $item['oil_money'];
                }
                $fanliMoney = floatval($viceCardInfos[$item['order_id']]['fanli_money'] ??  '0.00');
                $fanliJifen = floatval($viceCardInfos[$item['order_id']]['fanli_jifen'] ?? '0.00');
                $markRebate = $viceCardInfos[$item['order_id']]['mark_rebate'] ?? '0.00';
                $calCostMoney = $viceCardInfos[$item['order_id']]['cal_cost_money'] ?? '0.00';
                $calCostPrice = $viceCardInfos[$item['order_id']]['cal_cost_price'] ?? '0.00';
                $plummetRebateMoney = $viceCardInfos[$item['order_id']]['plummet_rebate_money'] ?? '0.00';
                $laterRebateMoney = $viceCardInfos[$item['order_id']]['later_rebate_money'] ?? '0.00';
                $direct_total = round(bcsub($supplier_money, $item['oil_money'], 6),2);//下游直降总额 = 应付金额 - 结算金额
                $back_cost_money = round(bcsub($item['oil_money'], bcadd($fanliJifen, $fanliMoney, 6), 6),2);//下游成本总额 = 结算总额 - 下游返利总额
                $back_cost_price = $item['oil_num'] > 0 ? round($back_cost_money/$item['oil_num'], 2) : '0.00';
                $back_cost_price = sprintf('%.2f',$back_cost_price);
                $real_money = '';//实际盈利 = 下游成本总额 - 上游成本总额
                $fanliJifen = round($fanliJifen, 2);
                $fanliMoney = round($fanliMoney, 2);

                $exportArray = [
                    'order_id'           => $item['order_id']."\t",
                    'order_sale_type_name' => $item['order_sale_type_name'],
                    'order_status'       => OrderModel::$ORDER_STATUS[$item['order_status']],
                    'station_name'       => $mirror['station_name'],
                    'station_code'       => $item['station_code'],
                    'supplier_name'      => $mirror['supplier_name'],
                    'goods'              => $mirror['goods'],
                    'oil_num'            => $item['oil_num'],
                    'gun_money'          => $mac_amount,
                    'supplier_money'     => $supplier_money,
                    'platform_money'     => $item['oil_money'],
                    'direct_total'       => $direct_total,
                    'third_actual_fee'   => $item['third_actual_fee'] ?: $item['oil_money'],
//                     'upstream_settle_money' => NewOrderService::formatUpstreamPriceMoney(['supplier_money' => $supplier_money], $mirror, 'upstream_settle_money', 'supplier_money'),
                    'fanli_money'        => $fanliMoney,
                    'fanli_jifen'        => $fanliJifen,
                    'back_cost_price'    => $back_cost_price,
                    'back_cost_money'    => $back_cost_money,
                    'real_money'         => '',
                    'ndrc_price'         => $mirror['ndrc_price'] ?? '',
                    'mac_price'          => $mirror['mac_price'] ?? '',
                    'supplier_price'     => $mirror['supplier_price'],
                    'platform_price'     => $mirror['platform_price'],
                    'third_actual_price' => $item['third_actual_price'],
//                     'upstream_settle_price' => NewOrderService::formatUpstreamPriceMoney(['supplier_price' => $mirror['supplier_price']], $mirror, 'upstream_settle_price', 'supplier_price'),
                    'plummet_rebate_money' => $mirror['plummet_rebate_money'] ?? '0.00',
                    'down_cost_money_price' => '',
                    'down_cost_money'       => '',
                    'later_rebate_money'    => $mirror['later_rebate_money'] ?? '0.00',
                    'charge_rebate_money'    => $mirror['charge_rebate_money'] ?? '0.00',
                    'cal_cost_price'        => '' ,
                    'cal_cost_money'    => '',
                    'card_no'            => $item['card_no']."\t",
                    'org_name'           => $mirror['org_name'],
                    'driver_name'        => $item['driver_name'],
                    'driver_phone'       => $item['driver_phone'],
                    'truck_no'           => $item['truck_no'],
                    'history_id'         => $item['history_id']."\t",
                    'remark'             => $item['remark'],
                    'order_channel_name' => CardTradeConf::$trade_from[$item['order_channel']] ?? '',
                    'third_order_id'     => $item['third_order_id'],
                    'creator'            => $item['creator'],
                    'updator'            => $item['updator'],
                    'create_time'        => $item['create_time'],
                    'update_time'        => $item['update_time'],
                    'pay_time'           => $item['pay_time'],
                    'province_name'      => $mirror['province_name'],
                    'city_name'          => $mirror['city_name'],
                    'gun_name'           => $mirror['gun_name'],
                ];

                if ($item['order_status'] == OrderModel::REFUND) {
                    $exportArray['plummet_rebate_money'] = "0.00";
                    $exportArray['later_rebate_money'] = "0.00";
                    $exportArray['charge_rebate_money'] = "0.00";
                }

                $down_cost_money = $mac_amount - $exportArray['plummet_rebate_money'];
                $exportArray['down_cost_money'] = sprintf('%.2f', round($down_cost_money,2));
                $exportArray['cal_cost_money'] = $cal_cost_money = $down_cost_money - $exportArray['later_rebate_money'] - $exportArray['charge_rebate_money'];
                $exportArray['cal_cost_money'] = sprintf('%.2f', round($exportArray['cal_cost_money'],2));

                $exportArray['plummet_rebate_money'] = sprintf('%.2f', round($exportArray['plummet_rebate_money'], 2));
                $exportArray['later_rebate_money'] = sprintf('%.2f', round($exportArray['later_rebate_money'], 2));
                $exportArray['charge_rebate_money'] = sprintf('%.2f', round($exportArray['charge_rebate_money'], 2));

                if (bccomp($item['real_oil_num'], 0) == 0) {
                    $exportArray['down_cost_money_price'] = $exportArray['mac_price'];
                    $exportArray['cal_cost_price'] = $exportArray['mac_price'];
                } else {
                    $exportArray['down_cost_money_price'] = sprintf('%.2f', round(bcdiv($down_cost_money, $item['real_oil_num'], 6), 2));
                    $exportArray['cal_cost_price'] = sprintf('%.2f', round(bcdiv($cal_cost_money, $item['real_oil_num'], 6), 2));
                }
                $exportArray['real_money'] = round(bcsub($back_cost_money, $exportArray['cal_cost_money'], 6), 2);
                foreach ($exportArray as &$v) {
                    $v = trim($v, "\n");
                }
                $exportStr .= implode(',', $exportArray) . "\n";
            }
            fwrite($resource, $exportStr);

            if (ob_get_level() > 0) {
                ob_flush();
                flush();
            }
        }

        fclose($resource);
    }

    public function getExportHeadsAndFields(){
        $exportField=[
            'id', 'serial_num', 'log_type', 'station_name', 'station_code', 'goods', 'money', 'price', 'oil_num',
            'truck_no', 'province_name', 'city_name', 'gun_name', 'data_type', 'stream_no', 'old_id', 'truename', 'createtime', 'oil_time'
        ];
        $header=[
            'ID', 'G7流水ID', '单据类型', '站点名称', '站点编码', '商品', '站点应收金额', '站点应收单价',
            '数量', '司机车牌号', '省', '市', '枪号', '订单来源', '三方订单号', '原始流水号', '操作人', '创建时间', '交易时间',
        ];
        return ['exportField'=>$exportField,'header'=>$header];
    }
}