<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OilAccountMoneyTransfer extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_account_money_transfer';

    protected $guarded = ["id"];

    protected $fillable = ['id','no','org_id',"into_org_id","money"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}