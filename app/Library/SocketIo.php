<?php

namespace App\Library;

use App\Library\Helper\Common;

class SocketIo
{
    public static function pushMessage($userId, $message, $namespace = 'hyr/driver') {
        try {
            $url = env('SOCKETIO_HOST').'/message/push/'.$namespace;
            $pushParam = [  // 参数组
                'userId' => strval($userId) ?? '',
                'message' => $message
            ];
            return Common::requestJson($url, $pushParam);
        } catch (\Exception $e) {
            Common::log("error", "推送异常:" . $e->getMessage(), ['exception' => $e, 'param' => $pushParam]);
        }
    }

}