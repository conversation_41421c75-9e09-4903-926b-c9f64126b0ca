APP_NAME=foss-order
APP_ENV=dev
APP_KEY=base64:SWIee/WX8+dyPSV7nCAvo+plDgUGf72dxRbMrw/imuw=
APP_DEBUG=true
APP_URL=http://************:7098

LOG_CHANNEL=single

DB_CONNECTION=mysql
DB_HOST=***********
DB_PORT=3306
DB_DATABASE=cat_gas
DB_USERNAME=cat
DB_PASSWORD=cathyr

#gas主库
DB_GAS_HOST=***********
DB_GAS_PORT=3306
DB_GAS_DATABASE=cat_gas
DB_GAS_USERNAME=cat
DB_GAS_PASSWORD=cathyr

DB_SLAVE_GAS_HOST=***********
DB_SLAVE_GAS_PORT=3306
DB_SLAVE_GAS_DATABASE=cat_gas
DB_SLAVE_GAS_USERNAME=cat
DB_SLAVE_GAS_PASSWORD=cathyr

DB_FOS_HOST=***********
DB_FOS_PORT=3306
DB_FOS_DATABASE=gsp_fuel
DB_FOS_USERNAME=cat
DB_FOS_PASSWORD=cathyr

#foss服务配置[gsp_fuel]
FOSS_API_URL=http://dev.zqx.chinawayltd.com/api.php
FOSS_API_KEY=oil_hyr
FOSS_API_SECRET=06398E117560D564BBDE870F6B3ADA58

#gas服务配置
GAS_API_KEY=fossOrder_api
GAS_API_SECRET=ffd5db10e854e85c178bac9643b459a2
GAS_API_URL=http://dev.gas.chinawayltd.com/rest


#gos的sdk配置
GOS_API = http://test.gos.chinawayltd.com/api
GOS_API_CALLBACK_URL=http://test.zqx.chinawayltd.com/api.php?method=gasAgent.callbackCenter.resultNotify
GOS_API_APP_ID=94a24035-915c-4d78-9361-4ac6b2227424
GOS_API_PUBLIC_KEY=A864DD8C9784CC16FAB4D5BA586BC91C

#foss_user配置
FOSS_USER_API_URL=http://foss.dev.chinawayltd.com/user
FOSS_USER_API_KEY =
FOSS_USER_API_SECRET =

#oiladapter服务配置
ADAPTER_API_KEY=foss_order
ADAPTER_API_SECRET=2aba4acbe40b09a513ff63f68f47d9a6
ADAPTER_API_URL=http://adapter.test.chinawayltd.com/


OA_API_URL=http://test.gas.chinawayltd.com/partner
OA_API_KEY=foss_station_api
OA_API_SECRET=0e8121544ed1741f425ca5b90d58e766

#foss-station 服务配置
FOSS_STATION_URL=http://station.dev.chinawayltd.com

BROADCAST_DRIVER=log
CACHE_DRIVER=array
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=*************
REDIS_PASSWORD=123456
REDIS_PORT=6382
REDIS_DB=11
REDIS_CACHE_DB=12

MAIL_DRIVER=smtp
MAIL_HOST=smtp.partner.outlook.cn
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=norepay_605@gas*
MAIL_FROM_NAME=gms系统管理员
MAIL_FROM_ADDRESS=<EMAIL>

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

LOG_STORAGE_PATH=/data/log/foss-order/laravel.log
DINGDING_TOKEN=677c8e4110a24008c2f9538d25e6f2f17d9c262715c8e1cae5e56b8245be160d
FEISHU_TOKEN=7014b97a9cef4e0c8b74e005978a1947
FEISHU_OPERATOR_TOKEN=7014b97a9cef4e0c8b74e005978a1947

FEI_SHU_TOKEN_OIL_PRICE_CHANGE=fb72d9ee-7c55-4882-a4ba-a9f7686fb55a
FEI_SHU_TOKEN_UPSTREAM_EXCEPTION_TRADE=60a695f4-1bb8-43e7-8ea8-3f7907679bba
FEI_SHU_TOKEN_DOWNSTREAM_ORDER_FAILED=c13925da-262c-4420-9d78-c4e50e06b76a
FEI_SHU_TOKEN_TRADE_REFUND=45f3d240-e837-47bf-ac02-fd4049b1d6f4S
FEI_SHU_TOKEN_ORDER_ABNORMAL_CHANGE_PATCH=75bbf625-2081-4805-b04a-939049aa314d

#阿里云图片
IMG_OSS_BUCKET_NAME=gsp-fuel-test
IMG_OSS_BUCKET_URL=oss-cn-chengdu.aliyuncs.com
IMG_OSS_BUCKET_HOST='https://gsp-fuel-test.oss-cn-chengdu.aliyuncs.com/'
IMG_OSS_BUCKET_ID=LTAI5tJMs1451zDmYNsEFffw
IMG_OSS_BUCKET_KEY=******************************
IMG_OSS_BUCKET_FILE_NAME='images/order/dev'

#部分机构下的站创建退款审核单需走OA校验状态
PCODE_OA_CHECK_BEFORE_REFUND=2000K2TC,20003VMM,2000VSVCD4TMLV,2000LTCS

#foss-api
SERVICE_FOSS_API=http://fuel.dev.chinawayltd.com

#微信配置
WX_TOKEN_URL=https://api.weixin.qq.com/cgi-bin/token
WX_TEMPLATE_MESSAGE_SEND_URL=https://api.weixin.qq.com/cgi-bin/message/template/send
WX_APP_ID=wx3d57ca38506068c2
WX_SECRET=a70a275c93bdda725346c937218df24a
WX_CREATE_ORDER_TEMPLATE=1-tR3kq8qKrexwVnh4mIO4VWMRPS2sDNTM8ww9m1lj0
WX_REFUND_TEMPLATE=hc8AwXv0pU5bUoiu8fBJ5ESX7BMoenLs4WJXf3eWmoI
#消息通知服务
NOTIFY_SERVICE=https://notify.test.chinawayltd.com

#open_api推送事件kafka消息中间件集群配置
OPEN_API_EVENT_PUSH_KAFKA_BROKERS=*************:9092