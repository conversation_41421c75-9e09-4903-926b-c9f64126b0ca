<?php


namespace App\Services;


use App\Http\Defines\CardTradeConf;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CouponDefine;
use App\Models\CouponsModel;
use App\Models\HistoryModel;
use App\Models\OrderModel;
use App\Repositories\NewOrderExtRepository;
use App\Repositories\NewOrderRepository;
use App\Servitization\FeiShu;
use App\Servitization\FossUser;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Servitization\FossStation;
use App\Library\Helper\Common;
use Throwable;

class RefundService
{
    protected $newOrderRepository;
    protected $errorCodeService;
    protected $historyService;
    protected $fossUser;
    protected $feishu;
    protected $couponSrv;
    protected $orderExtend;
    protected $fossStation;
    private   $messageService;

    public function __construct
    (
        NewOrderRepository $newOrderRepository,
        HistoryService $historyService,
        ErrorCodeService $errorCodeService,
        FossUser $fossUser,
        FeiShu $feiShu,
        CouponService $couponSrv,
        NewOrderExtRepository $orderExtend,
        FossStation $fossStation,
        MessageService $messageService
    ) {
        $this->historyService = $historyService;
        $this->newOrderRepository = $newOrderRepository;
        $this->errorCodeService = $errorCodeService;
        $this->fossUser = $fossUser;
        $this->feishu = $feiShu;
        $this->couponSrv = $couponSrv;
        $this->orderExtend = $orderExtend;
        $this->fossStation = $fossStation;
        $this->messageService = $messageService;
    }

    /**
     * 退款[成功返回交易流水ID]
     *
     * @param $params
     * @param string $updater
     * @param bool $businessWarning
     * @return string
     * @throws Exception
     */
    public function fossRefund($params, string $updater = '系统', bool $businessWarning = false, $isTrans = true)
    {
        $originalHistoryId = $params['original_history_id'];
        $orderId = $params['order_id'];
        $orderInfo = $this->newOrderRepository->getOrderInfoWithExt([
            'order_id' => $orderId,
        ]);
        if (!$orderInfo) {
            throw new Exception('退款失败,订单不存在!', $this->errorCodeService->gotoRefund(40304));
        }
        if (!in_array($orderInfo['order_status'], [2, 5])) {
            throw new Exception('订单状态不合法，不允许退款!', $this->errorCodeService->gotoRefund(40304));
        }

        $coupon_flag = array_get($orderInfo, "ext.g7_coupon_flag", "");
        $logType = $params['update_history_log_type'] ?? HistoryModel::COST_INVALID;
        if ($orderInfo['order_sale_type'] == OrderModel::ORDER_SALE_TYPE_RESERVATION_REFUEL) {
            $logType = HistoryModel::COST_RESERVATION_REFUEL_REFUND;
        }
        try {
            $this->fossUser->cancelTrade([
                'order_no'          => $orderId,
                'document_type'     => CardTradeConf::$history_type_to_foss_mapping[$logType],
                'original_order_id' => $originalHistoryId,
            ]);
        } catch (Exception $e) {
            $result = $this->fossUser->queryPayResult(['order_no' => $orderId]);
            if ($result['pay_status'] == 1) {
                $updateArray = [
                    'order_status' => OrderModel::SUCCESS_PAY,
                    'updator'      => $updater
                ];
                $this->newOrderRepository->updateOrderByOrderId($orderId, $updateArray);
            }
            $this->feishu->sendExceptionToFeiShu($e);
            if ($businessWarning) {
                $this->feishu->sendRefundFailed([
                    'category'      => '退款审核',
                    'order_id'      => $orderId,
                    'station_name'  => $orderInfo['ext']['station_name'] ?? '',
                    'goods'         => $orderInfo['ext']['goods'] ?? '',
                    'pay_money'     => $orderInfo['oil_money'] ?? '',
                    'org_name'      => $orderInfo['ext']['org_name'] ?? '',
                    'card_no'       => $orderInfo['card_no'] ?? '',
                    'driver_phone'  => $orderInfo['driver_phone'] ?? '',
                    'failed_reason' => $e->getMessage(),
                ]);
            }
            throw $e;
        }

        if ($isTrans) {
            DB::connection('mysql_gas')->beginTransaction();
        }
        try {
            // 修改、添加交易流水
            $historyId = $this->historyService->updateHistoryAddRefund(
                $originalHistoryId,
                $logType
            );
            if (empty($historyId)) {
                throw new Exception('退款失败,添加交易流水失败!', $this->errorCodeService->gotoRefund(40304));
            }
            // 修改订单状态
            $result = $this->newOrderRepository->updateOrderByOrderId($orderId, [
                'order_status' => OrderModel::REFUND,
                'updator'      => $updater,
            ]);
            if (empty($result)) {
                throw new Exception('退款失败,修改订单状态失败!', $this->errorCodeService->gotoRefund(40305));
            }
            if (!empty($coupon_flag)) {
                $this->couponSrv->updateCoupon($coupon_flag, ['status' => CouponDefine::NO_USE, 'modifier' => $updater]
                );
            }
            if ($isTrans) {
                DB::connection('mysql_gas')->commit();

                // 注销电子券
                $this->couponSrv->cancelCoupon(
                    $orderInfo['supplier_code'],
                    array_get($orderInfo, "ext.g7_coupon_flag", ""),
                    $updater
                );
            }
        } catch (Exception $e) {
            if ($isTrans) {
                DB::connection('mysql_gas')->rollBack();
            }
            throw $e;
        }

        return $historyId;
    }

    /**
     * OA退款
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function oaRefund($params)
    {
        $orderId = $params['order_id'];
        try {
            DB::connection('mysql_gas')->beginTransaction();
            $orderInfo = $this->newOrderRepository->getOrderInfoByLock(['order_id' => $orderId]);
            if (empty($orderInfo)) {
                throw new \RuntimeException(
                    '退款失败,无效的订单号,请核对!', $this->errorCodeService->gotoRefund(40301)
                );
            }
            if ($orderInfo['driver_source'] != OrderModel::THIRD_DRIVER) {
//            throw new \RuntimeException('退款失败,非平台司机下单,不允许退款!', $this->errorCodeService->gotoRefund(40302)); // 解除非平台司机不允许退款限制
            }
            // 三方订单无退款中状态
            //if ($orderInfo['order_status'] != OrderModel::SUCCESS_PAY) {
            if (!in_array($orderInfo['order_status'], [OrderModel::SUCCESS_PAY, OrderModel::REFUNDING])) {
                throw new \RuntimeException(
                    '退款失败,订单状态为[' . OrderModel::$ORDER_STATUS[$orderInfo['order_status']] . '],不允许退款!',
                    $this->errorCodeService->gotoRefund(40403)
                );
            }
            if (!empty($orderInfo['mirror'])) {
                $mirror = json_decode($orderInfo['mirror'], true);
            } else {
                $mirror = $orderInfo['ext'];
                unset($mirror['order_id']);
                $mirror['update_time'] = $orderInfo['update_time'];
                $mirror['order_channel_name'] = CardTradeConf::getFrom($orderInfo['order_channel']);
            }
            if (isset(CommonDefine::linkedRefundSupplier()[$orderInfo['supplier_code']])) {
                try {
                    $this->messageService->pushData2OA([
                        'order_id'          => $orderInfo['order_id'],
                        'order_holder_code' => $orderInfo['supplier_code'],
                    ], 'REFUND');
                } catch (Throwable $throwable) {
                    if (isset(
                            CommonDefine::linkedRefundSupplier()[$orderInfo['supplier_code']]['wait_refund_result']
                        ) and CommonDefine::linkedRefundSupplier()[$orderInfo['supplier_code']]['wait_refund_result']) {
                        throw $throwable;
                    }
                }
            }
            $needParams = [
                'order_id'            => $orderId,
                'third_order_id'      => $params['third_order_id'],
                'original_history_id' => $orderInfo['history_id'],
            ];

            $this->fossRefund($needParams, $mirror['org_name'], false, false);
            DB::connection('mysql_gas')->commit();

            $this->couponSrv->cancelCoupon(
                $orderInfo['supplier_code'],
                array_get($mirror, 'g7_coupon_flag', ''),
                $mirror['org_name']
            );
        } catch (\RuntimeException $e) {
            DB::connection('mysql_gas')->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }
}