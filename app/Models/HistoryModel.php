<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class HistoryModel extends BaseModel
{
    /**
     * log_type enum
     */
    CONST COST_SUCCESS = 1111;
    CONST COST_ABNORMAL = 1112;
    CONST COST_SUCCESS_ADD = 1113;
    CONST COST_ORIGINAL = 1200;
    CONST COST_INVALID = 1120;
    CONST COST_INVALID_2 = 1130;
    CONST MODIFY_ING = 1220;
    CONST COST_RESERVATION_REFUEL = 6111;
    CONST COST_RESERVATION_REFUEL_REFUND = 6120;
    CONST COST_ELECTRICITY_ORDER = 6211;
    CONST CHARGE = 2;
    CONST REBATE = 21;
    CONST CUT_SERVICE_FEE = 22;
    CONST ASSIGN = 23;
    CONST CANCEL_ASSIGN = 24;
    CONST ASSIGN_2 = 25;
    CONST CANCEL_ASSIGN_2 = 26;
    CONST WECHAT_RECHARGE = 27;
    CONST IN_OIL = 3;
    CONST TRANSFER_ASSIGN = 4;
    CONST TRANSFER_OIL = 28;
    CONST BUY_OIL = 29;

    public static $LOG_TYPE_ENUM = [
        self::CHARGE => [
            'code' => self::CHARGE,
            'msg' => '充值',
        ],
        self::IN_OIL => [
            'code' => self::IN_OIL,
            'msg' => '进油',
        ],
        self::TRANSFER_ASSIGN => [
            'code' => self::TRANSFER_ASSIGN,
            'msg' => '库存调整',
        ],
        self::REBATE => [
            'code' => self::REBATE,
            'msg' => '返利',
        ],
        self::CUT_SERVICE_FEE => [
            'code' => self::CUT_SERVICE_FEE,
            'msg' => '待扣服务费',
        ],
        self::ASSIGN => [
            'code' => self::ASSIGN,
            'msg' => '分配',
        ],
        self::CANCEL_ASSIGN => [
            'code' => self::CANCEL_ASSIGN,
            'msg' => '撤销分配',
        ],
        self::ASSIGN_2 => [
            'code' => self::ASSIGN_2,
            'msg' => '分配',
        ],
        self::CANCEL_ASSIGN_2 => [
            'code' => self::CANCEL_ASSIGN_2,
            'msg' => '撤销分配',
        ],
        self::WECHAT_RECHARGE => [
            'code' => self::WECHAT_RECHARGE,
            'msg' => '微信充值',
        ],
        self::TRANSFER_OIL => [
            'code' => self::TRANSFER_OIL,
            'msg' => '转油',
        ],
        self::BUY_OIL => [
            'code' => self::BUY_OIL,
            'msg' => '收油',
        ],
        self::COST_SUCCESS => [
            'code' => self::COST_SUCCESS,
            'msg' => '消费--正常',
        ],
        self::COST_ABNORMAL => [
            'code' => self::COST_ABNORMAL,
            'msg' => '消费--修改',
        ],
        self::COST_SUCCESS_ADD => [
            'code' => self::COST_SUCCESS_ADD,
            'msg' => '消费--补录',
        ],
        self::COST_INVALID => [
            'code' => self::COST_INVALID,
            'msg' => '作废撤销',
        ],
        self::COST_INVALID_2 => [
            'code' => self::COST_INVALID_2,
            'msg' => '修改撤销'
        ],
        self::COST_ORIGINAL => [
            'code' => self::COST_ORIGINAL,
            'msg' => '消费(修改或作废前的原始数据)'
        ],
        self::MODIFY_ING => [
            'code' => self::MODIFY_ING,
            'msg' => '修改中'
        ],
        self::COST_RESERVATION_REFUEL => [
            'code' => self::COST_RESERVATION_REFUEL,
            'msg' => '消费--预扣'
        ],
        self::COST_RESERVATION_REFUEL_REFUND => [
            'code' => self::COST_RESERVATION_REFUEL_REFUND,
            'msg' => '退款-预扣'
        ],
    ];

    /**
     * sale_type的枚举类
     */
    CONST ALL_SALE_TYPE = '';
    CONST CASH_SALE_TYPE = 11;
    CONST OVER_DRAW = 12;
    CONST OVER_DRAW_SERVICE_FEE = 13;
    CONST CASH_SERVICE_FEE = 14;

    public static $SALE_TYPE = [
        self::ALL_SALE_TYPE => [
            'code' => self::ALL_SALE_TYPE,
            'msg' => '全部',
        ],
        self::CASH_SALE_TYPE => [
            'code' => self::CASH_SALE_TYPE,
            'msg' => '现金消费',
        ],
        self::OVER_DRAW => [
            'code' => self::OVER_DRAW,
            'msg' => '透支消费',
        ],
        self::OVER_DRAW_SERVICE_FEE => [
            'code' => self::OVER_DRAW_SERVICE_FEE,
            'msg' => '透支服务费',
        ],
        self::CASH_SERVICE_FEE => [
            'code' => self::CASH_SERVICE_FEE,
            'msg' => '现金服务费',
        ]
    ];

    /**
     * date_type enum
     */
    public static $dataTypeList = [
        '11' => '移动端',
        '12' => '移动端',
        '13' => '移动端',//移动端民营加油
        '2' => '平台端',
        '21' => '平台端', // G7能源账户 API 来源
        '31' => '卡机',
        '32' => '卡机',
        '33' => '卡机蓝牙',
        '34' => '油机价',
        '35' => 'HTTP',
        '36' => '卡机',
        '4' => '扫码',
        '41' => '扫码 HTTP',
        '51' => '会员卡支付',
        '52' => '微信支付',
        '53' => '脱机加注',
        '91' => '',
        '92' => '',
        '93' => '',
        '94' => '',
        '95' => '', // 9开头的是PDA-H5新增的渠道
    ];

    /**
     * unit enum
     */
    CONST MONEY_UTIL = 1;
    CONST OIL_NUM_UTIL = 2;
    public static $OIL_UTIL = [
        self::MONEY_UTIL => '定金额加油',
        self::OIL_NUM_UTIL => '定升数加油',
    ];

    /**
     * 连接名
     */
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_history';

    protected $guarded = ["id"];

    protected $fillable = ['id','serial_num','pcode','xpcode','scode','gascode','card_id','card_no','pay_price','unit',
        'pay_status','log_type','sale_type','xpcode_sale_type','data_type','truck_no','truename','creator','orgcode',
        'money','pay_money','xpcode_pay_money','balance','xpcode_balance','oil_balance','oil_type','oil_name',
        'oil_level','oil_num','oil_count','every_fuelnum','price_id','xpcode_price_id','price','buy_price',
        'xpcode_pay_price','support_type','return_price','use_price','service_price','xpcode_service_price',
        'station_id','tank_id','gun_id','oil_time','createtime','updatetime','from_orgcode', 'from_orgname',
        'consumer_name','imgurl','uploadtime','imgData','no','old_id','ifreturn','remark','is_pay','video_status',
        'refuel_no','stream_no','list_price','oil_starttime','mileage','card_classify','last_stock','provice_code',
        'city_code','driver_name','driver_phone','pay_tag'
    ];

    /**
     * 非自增主键
     */
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * @param array $field
     * @param int $page
     * @param int $limit
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getHistoryInfoByLimit(array $field, $page = 1, $limit = 10, array $select = [], $toArray = false)
    {
        $select = empty($select) ? $this->fillable : $select;

        $query = self::select($select);

        // 拼接参数
        $query = BaseModel::scopeWithCondition($query, $field, true);
        // 统计求和
        $count = $query->count();
        // 站点应收金额
        $totalMoney = $query->sum('xpcode_pay_money');
        // 客户加油升数(非油品升数是按单位折算,暂时按照油品类统计)
        $totalOilNum = $query->sum('oil_num');
        // 排序
        $query = $query->orderBy('oil_time', 'desc');
        // 分页
        $result = $query->offset(($page - 1) * $limit)->limit($limit)->get();
        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return [
            'data' => $result,
            'count' => $count,
            'total_pay_money' => $totalMoney,
            'total_oil_num' => $totalOilNum,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 交易流水导出分页查询
     *
     * @param array $whereParams
     * @param $page
     * @param $limit
     * @param array $select
     * @return array
     */
    public function getExportHistoryPaginate(array $whereParams, $page, $limit, array $select)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->offset(($page - 1) * $limit)->limit($limit)
            ->orderBy('oil_time', 'desc')->get();

        return empty($result) ? [] : $result->toArray();
    }

    /**
     * 获取报表导出数据量
     *
     * @param array $whereParams
     * @return mixed
     */
    public function getHistoryCountByParams(array $whereParams)
    {
        return BaseModel::scopeWithCondition(self::select(['*']), $whereParams)->count();
    }

    /**
     * 获取一条交易流水
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneHistoryByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 添加交易流水
     *
     * @param array $insertArray
     * @return mixed
     */
    public function insertHistory(array $insertArray)
    {
        return self::insert($insertArray);
    }

    /**
     * 更新交易流水
     *
     * @param array $whereParams
     * @param array $update
     * @return mixed
     */
    public function updateHistory(array $whereParams, array $update)
    {
        return BaseModel::scopeWithCondition(self::from($this->table), $whereParams)->update($update);
    }
}