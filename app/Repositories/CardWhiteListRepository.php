<?php


namespace App\Repositories;


use App\Models\CardWhiteListModel;

class CardWhiteListRepository
{
    protected $cardWhiteListModel;

    public function __construct
    (
        CardWhiteListModel $cardWhiteListModel
    )
    {
        $this->cardWhiteListModel = $cardWhiteListModel;
    }

    /**
     * 获取1条白名单信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneWhiteListByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->cardWhiteListModel->getOneWhiteListByParams($whereParams, $select, $toArray);
    }
}