<?php


namespace App\Repositories;


use App\Models\GasHistoryApproveModel;

class HistoryApproveRepository
{
    protected $historyApproveModel;

    public function __construct
    (
        GasHistoryApproveModel $historyApproveModel
    )
    {
        $this->historyApproveModel = $historyApproveModel;
    }

    /**
     * 分页查询审核列表
     *
     * @param array $whereParams
     * @param array $select
     * @param int $page
     * @param int $limit
     * @param bool $toArray
     * @return array
     */
    public function getApprovePaginate(array $whereParams = [], $select = ['*'], $page = 1, $limit = 10, $toArray = true)
    {
        return $this->historyApproveModel->getHistoryApprovePaginate($whereParams, $select, $page, $limit, $toArray);
    }

    /**
     * 获取一条审核单
     *
     * @param array $wherePrams
     * @param array $select
     * @param bool $toArray
     * @param bool $lock
     * @return array|object
     */
    public function getOneApproveInfoByParams(array $wherePrams, array $select = ['*'],
                                              bool $toArray = true, bool $lock = false)
    {
        return $this->historyApproveModel->getOneApproveInfoByParams($wherePrams, $select, $toArray, $lock);
    }

    /**
     * 获取批量审核记录
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchApproveInfoByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        return $this->historyApproveModel->getBatchApproveInfoByParams($whereParams, $select, $toArray);
    }

    /**
     * 创建审核单
     *
     * @param array $insertArray
     * @return mixed
     */
    public function approveInsert(array $insertArray)
    {
        return $this->historyApproveModel->approveInsert($insertArray);
    }

    /**
     * 更新审核单
     *
     * @param $id
     * @param array $updateArray
     * @return mixed
     */
    public function approveUpdateById($id, array $updateArray)
    {
        return $this->historyApproveModel->approveUpdateById($id, $updateArray);
    }
    /**
     * 统计
     * @param array $whereParams
     * @param string[] $select
     * @return mixed
     */
    public function getHistoryApproveCount(array $whereParams = [], $select = ['*'])
    {
        return $this->historyApproveModel->getHistoryApproveCount($whereParams, $select);
    }
}