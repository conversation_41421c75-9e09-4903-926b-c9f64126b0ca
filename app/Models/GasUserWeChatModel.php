<?php


namespace App\Models;


class GasUserWeChatModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_user_wx';

    protected $fillable = ['gas_user_id', 'openid', 'create_time', 'update_time'];

    public $timestamps = false;

    /**
     * 批量获取open_id
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return mixed
     */
    public function getOpenIdsByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = $result->toArray();
        }

        return $result;
    }

}