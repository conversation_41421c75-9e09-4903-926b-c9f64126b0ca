<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/15
 * Time: 17:35
 */

namespace App\Http\Middleware;
use App\Http\Defines\CommonDefine;
use App\Http\Defines\CommonError;
use Library\Monitor\Falcon;
use Closure;
use App\Library\Helper\Common;

class MiniApi
{
    public function handle($request, Closure $next)
    {
        $header = $request->header();
        if(!isset($header['x-g7-api-uid']) || empty($header['x-g7-api-uid'])){
            throw new \RuntimeException('',CommonError::NO_AUTH);
        }
        $request->offsetSet("_user_id" , $request->header('x-g7-api-uid'));
        $request->offsetSet("_user_type" , "driver");
        $response = $next($request);
        return $response;
    }
}
