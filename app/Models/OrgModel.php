<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OrgModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_org';

    protected $guarded = ["id"];

    protected $fillable = ['id','orgcode',"pcode","orgname"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 批量获取机构信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneOrgByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 批量获取机构信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchOrgByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

}