<?php

/**
 * <PERSON>vel - A PHP Framework For Web Artisans
 *
 * @package  <PERSON><PERSON>
 * <AUTHOR> <<EMAIL>>
 */

use G7\Tracing\TraceContext;
use G7\Tracing\TracerBuilder;

define('LARAVEL_START', microtime(true));

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| our application. We just need to utilize it! We'll simply require it
| into the script here so that we don't have to worry about manual
| loading any of our classes later on. It feels great to relax.
|
*/

require __DIR__.'/../vendor/autoload.php';

/*
|--------------------------------------------------------------------------
| Turn On The Lights
|--------------------------------------------------------------------------
|
| We need to illuminate PHP development, so let us turn on the lights.
| This bootstraps the framework and gets it ready for use, then it
| will load up this application so that we can run it and send
| the responses back to the browser and delight our users.
|
*/

$app = require_once __DIR__.'/../bootstrap/app.php';

// 创建Tracer
TracerBuilder::create()
             ->setServiceName('foss-order-web')
             ->setAgentHostPort('127.0.0.1:6831')
             ->build();

// 获取Tracer
$tracer = TraceContext::instance()->getTracer();
$tracer->setTag('host.name', gethostname());
$tracer->setTag('cluster.id', env('APP_CLUSTER_ID', ''));
$tracer->setTag('cluster.name', env('APP_CLUSTER_NAME', ''));
$tracer->setTag('span.kind', 'server');
// 创建Span
global $rootSpan;
$rootSpan = $tracer->startSpan(explode('?', $_SERVER['REQUEST_URI'])[0], microtime(true));

register_shutdown_function(function () use ($tracer, $rootSpan) {
    $rootSpan->log([
        'title' => 'php执行结束回调',
        'current_time' => (string)microtime(true),
    ]);
    $rootSpan->finish();
    $tracer->flush();
});



/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request
| through the kernel, and send the associated response back to
| the client's browser allowing them to enjoy the creative
| and wonderful application we have prepared for them.
|
*/

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

$response->send();

$kernel->terminate($request, $response);
