<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class OilAccountAssignDetails extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_account_assign_details';

    protected $guarded = ["id"];

    protected $fillable = ['id','assign_id','org_id',"vice_id","assign_money","service_money","actual_money"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}