<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class CardViceModel extends BaseModel
{
    protected $connection = 'mysql_foss';
    /**
     * 表名
     */
    protected $table = 'oil_card_vice';

    protected $guarded = ["id"];

    public $incrementing = true;

    protected $fillable = ['id','vice_no',"vice_password","status"];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function org()
    {
        return $this->belongsTo(OilOrgModel::class, 'org_id', 'id');
    }
}